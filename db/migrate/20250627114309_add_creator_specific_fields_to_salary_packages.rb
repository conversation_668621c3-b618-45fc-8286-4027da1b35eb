class AddCreatorSpecificFieldsToSalaryPackages < ActiveRecord::Migration[8.0]
  def change
    # Add creator tracking field
    add_column :salary_packages, :created_by_id, :bigint

    # Add cancellation audit fields
    add_column :salary_packages, :cancelled_at, :timestamp
    add_column :salary_packages, :cancellation_reason, :text

    # Add foreign key constraint to employees table
    add_foreign_key :salary_packages, :employees, column: :created_by_id

    # Add indexes for performance
    add_index :salary_packages, :created_by_id
    add_index :salary_packages, [:employee_id, :created_by_id, :status], name: 'index_salary_packages_on_employee_creator_status'
    add_index :salary_packages, [:created_by_id, :status], name: 'index_salary_packages_on_creator_status'

    # Note: Status enum update (adding cancelled: 4) will be handled in the model
    # Rails enums are defined in the model, not in migrations
  end

  def down
    # Remove indexes first
    remove_index :salary_packages, name: 'index_salary_packages_on_creator_status'
    remove_index :salary_packages, name: 'index_salary_packages_on_employee_creator_status'
    remove_index :salary_packages, :created_by_id

    # Remove foreign key constraint
    remove_foreign_key :salary_packages, :employees

    # Remove columns
    remove_column :salary_packages, :cancellation_reason
    remove_column :salary_packages, :cancelled_at
    remove_column :salary_packages, :created_by_id
  end
end
