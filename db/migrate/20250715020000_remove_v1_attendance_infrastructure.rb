class RemoveV1AttendanceInfrastructure < ActiveRecord::Migration[8.0]
  def up
    v1_settings_to_remove = [
      'auto_leave_enabled',
      'auto_leave_counts_against_balance',
      'notify_managers_auto_leave',
      'notify_employees_auto_leave',
      'notify_auto_leave_created',
      'required_work_minutes'  # V1 setting replaced by daily_expected_hours in V2
    ]

    v1_settings_to_remove.each do |key|
      removed_count = execute("DELETE FROM settings WHERE namespace = 'attendance' AND key = '#{key}'")
    end
  end

  def down
    create_setting('attendance', 'auto_leave_enabled', 'false', 'Enable automatic leave detection (V1 - deprecated)', false, :boolean)
    create_setting('attendance', 'auto_leave_counts_against_balance', 'false', 'Whether auto-generated leaves count against leave balance (V1 - deprecated)', false, :boolean)
    create_setting('attendance', 'notify_managers_auto_leave', 'false', 'Notify managers when auto-generated leaves are created (V1 - deprecated)', false, :boolean)
    create_setting('attendance', 'notify_employees_auto_leave', 'false', 'Notify employees when auto-generated leaves are created (V1 - deprecated)', false, :boolean)
    create_setting('attendance', 'notify_auto_leave_created', 'false', 'Send notifications when auto-generated leaves are created (V1 - deprecated)', false, :boolean)
    create_setting('attendance', 'required_work_minutes', '480', 'Required work minutes per day (V1 - deprecated, replaced by daily_expected_hours)', false, :integer)
  end

  private

  def create_setting(namespace, key, value, description, is_editable, setting_type)
    type_value = case setting_type
                 when :string then 0
                 when :integer then 1
                 when :boolean then 2
                 when :decimal then 3
                 when :time then 4
                 when :json then 5
                 when :email then 6
                 when :phone then 7
                 when :url then 8
                 when :currency then 9
                 when :array then 10
                 when :date then 11
                 when :datetime then 12
                 else 0
                 end

    execute <<-SQL
      INSERT INTO settings (namespace, key, value, description, is_editable, setting_type, created_at, updated_at)
      VALUES ('#{namespace}', '#{key}', '#{value}', '#{description}', #{is_editable}, #{type_value}, NOW(), NOW())
      ON CONFLICT (namespace, key) DO NOTHING
    SQL
  end
end
