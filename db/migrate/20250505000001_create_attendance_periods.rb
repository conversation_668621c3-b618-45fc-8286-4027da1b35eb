class CreateAttendancePeriods < ActiveRecord::Migration[8.0]
  def change
    create_table :attendance_periods do |t|
      t.references :employee, null: false, foreign_key: true
      t.date :date, null: false
      t.string :period_type, null: false
      t.integer :start_timestamp, null: false
      t.integer :end_timestamp, null: false
      t.integer :duration_minutes, null: false
      t.string :activity_type
      t.boolean :is_predicted, default: false
      t.text :notes

      t.timestamps
    end

    add_index :attendance_periods, [:employee_id, :date]
    add_index :attendance_periods, :period_type
  end
end
