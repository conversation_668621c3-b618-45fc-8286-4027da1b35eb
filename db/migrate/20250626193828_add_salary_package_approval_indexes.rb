class AddSalaryPackageApprovalIndexes < ActiveRecord::Migration[8.0]
  def change
    # Index for permission-based queries (employee + status + date)
    add_index :salary_packages, [:employee_id, :status, :effective_date],
              name: 'idx_salary_packages_employee_status_date'

    # Index for current package queries (status + date range)
    add_index :salary_packages, [:status, :effective_date, :end_date],
              name: 'idx_salary_packages_status_date_range'

    # Index for approval workflow queries (status only, since actor_user_id is virtual)
    add_index :salary_packages, [:status],
              name: 'idx_salary_packages_status'

    # Index for salary calculations relationship
    add_index :salary_calculations, [:salary_package_id, :period_start_date],
              name: 'idx_salary_calculations_package_period'

    # Index for employee salary calculation queries
    add_index :salary_calculations, [:employee_id, :period_start_date, :period_end_date],
              name: 'idx_salary_calculations_employee_period'
  end
end
