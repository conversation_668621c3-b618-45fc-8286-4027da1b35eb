class CreateAttendanceEvents < ActiveRecord::Migration[8.0]
  def change
    create_table :attendance_events do |t|
      t.references :employee, null: false, foreign_key: true
      t.datetime :timestamp, null: false
      t.integer :event_type, default: 2 # 0: check_in, 1: check_out, 2: undetermined
      t.integer :activity_type, default: 0 # 0: break (default), 1: lunch, etc.
      t.string :location
      t.text :notes

      t.timestamps
    end

    # Index for efficient querying
    add_index :attendance_events, [ :employee_id, :timestamp ]
  end
end
