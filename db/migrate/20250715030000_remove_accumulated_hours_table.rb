class RemoveAccumulatedHoursTable < ActiveRecord::Migration[8.0]
  def up
    drop_table :attendance_accumulated_hours
  end

  def down
    create_table :attendance_accumulated_hours do |t|
      t.references :employee, null: false, foreign_key: true
      t.date :date, null: false
      t.decimal :missing_hours, precision: 5, scale: 2, null: false, default: 0.0
      t.string :status, null: false, default: 'active'
      t.text :notes
      t.timestamps
    end

    # Add indexes
    add_index :attendance_accumulated_hours, [ :employee_id, :date ], unique: true
    add_index :attendance_accumulated_hours, :status
    add_index :attendance_accumulated_hours, :date
  end
end
