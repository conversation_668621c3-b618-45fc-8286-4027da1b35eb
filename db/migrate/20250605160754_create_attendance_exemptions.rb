class CreateAttendanceExemptions < ActiveRecord::Migration[8.0]
  def change
    create_table :attendance_exemptions do |t|
      t.string :name, null: false
      t.date :start_date, null: false
      t.date :end_date, null: false
      t.integer :exemption_type, default: 0, null: false
      t.text :description
      t.boolean :is_active, default: true, null: false
      t.boolean :affects_salary, default: true, null: false
      t.boolean :affects_attendance, default: true, null: false

      t.timestamps
    end

    add_index :attendance_exemptions, [ :start_date, :end_date ]
    add_index :attendance_exemptions, :exemption_type
    add_index :attendance_exemptions, :is_active
  end
end
