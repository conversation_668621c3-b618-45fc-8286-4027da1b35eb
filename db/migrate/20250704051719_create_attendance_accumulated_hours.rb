class CreateAttendanceAccumulatedHours < ActiveRecord::Migration[8.0]
  def change
    create_table :attendance_accumulated_hours do |t|
      t.references :employee, null: false, foreign_key: true
      t.date :date, null: false
      t.decimal :missing_hours, precision: 8, scale: 2, null: false, default: 0.0
      t.string :status, null: false, default: 'active'

      t.timestamps
    end

    # Indexes for efficient querying
    add_index :attendance_accumulated_hours, [ :employee_id, :date ], unique: true
    add_index :attendance_accumulated_hours, :status
    add_index :attendance_accumulated_hours, [ :employee_id, :status ]
  end
end
