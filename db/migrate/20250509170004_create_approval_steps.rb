class CreateApprovalSteps < ActiveRecord::Migration[8.0]
  def change
    create_table :approval_steps do |t|
      t.references :approval_request, null: false, foreign_key: true
      t.string :step_id, null: false
      t.string :name, null: false
      t.integer :sequence, null: false
      t.string :approval_type, null: false, default: "any"
      t.json :approver_ids, null: false, default: []
      t.string :condition
      t.boolean :skip_if_no_approvers, default: false
      t.string :description
      t.string :dynamic_approver_method
      t.text :selection_explanation
      t.timestamps

      t.index [:approval_request_id, :sequence], unique: true
    end
  end
end
