class ConsolidateV2AttendanceChanges < ActiveRecord::Migration[8.0]
  def up
    # Changes from 20250704051834_update_attendance_settings.rb:
    # 1. Remove monthly_expected_hours setting (deductions_enabled is already created by 20250712000001)
    execute("DELETE FROM settings WHERE namespace = 'attendance' AND key = 'monthly_expected_hours'")

    # Changes from 20250711012846_add_setting_type_to_settings.rb:
    # 2. Update setting types (removing monthly_expected_hours from decimal type)
    execute <<~SQL
      UPDATE settings SET setting_type = 3
      WHERE namespace = 'attendance' AND key IN (
        'daily_work_threshold',
        'accumulated_hours_threshold'
      ) AND setting_type IS NULL
    SQL

    # 3. Update deductions_enabled to 'true' (it was created as 'false' in 20250712000001)
    execute("UPDATE settings SET value = 'true' WHERE namespace = 'attendance' AND key = 'deductions_enabled'")

    # 4. Add exclude_weekends and exclude_holidays boolean settings for configurable exclusions
    create_setting('attendance', 'exclude_weekends', 'true', 'Exclude weekends from attendance deduction calculations', true, 2)
    create_setting('attendance', 'exclude_holidays', 'true', 'Exclude holidays from attendance deduction calculations', true, 2)

    # 5. Fix setting types for boolean settings created by 20250712000001
    execute("UPDATE settings SET setting_type = 2 WHERE namespace = 'attendance' AND key = 'deductions_enabled'")
  end

  def down
    # Reverse all changes

    # Restore deductions_enabled to 'false' (original value from 20250712000001)
    execute("UPDATE settings SET value = 'false' WHERE namespace = 'attendance' AND key = 'deductions_enabled'")

    # Remove exclude_weekends, exclude_holidays, and weekend_days settings
    execute("DELETE FROM settings WHERE namespace = 'attendance' AND key IN ('exclude_weekends', 'exclude_holidays', 'weekend_days')")

    # Re-add monthly_expected_hours setting
    create_setting('attendance', 'monthly_expected_hours', '180.0', 'Expected working hours per month')

    # Restore setting types to include monthly_expected_hours
    execute <<~SQL
      UPDATE settings SET setting_type = 3
      WHERE namespace = 'attendance' AND key IN (
        'daily_work_threshold',
        'accumulated_hours_threshold',
        'monthly_expected_hours'
      )
    SQL
  end

  private

  def create_setting(namespace, key, value, description, is_editable = true, setting_type = 0)
    execute(<<~SQL)
      INSERT INTO settings (namespace, key, value, description, is_editable, setting_type, created_at, updated_at)
      VALUES ('#{namespace}', '#{key}', '#{value}', '#{description}', #{is_editable}, #{setting_type}, NOW(), NOW())
      ON CONFLICT (namespace, key) DO UPDATE SET
        value = EXCLUDED.value,
        description = EXCLUDED.description,
        is_editable = EXCLUDED.is_editable,
        setting_type = EXCLUDED.setting_type,
        updated_at = NOW()
    SQL
  end
end
