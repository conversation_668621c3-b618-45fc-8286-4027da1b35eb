class AddMissingAttendanceSettings < ActiveRecord::Migration[8.0]
  def up
    # Settings that should have been created by 20250704051834_update_attendance_settings.rb
    create_setting('attendance', 'weekend_days', '5,6', 'Weekend days (0=Sunday, 1=Monday, ..., 6=Saturday)', true, 10) # array type

    # Settings that should have been created by 20250712000001_add_v2_attendance_deduction_settings.rb
    create_setting('attendance', 'daily_expected_hours', '8.0', 'Expected work hours per day for deduction calculations', true, 3) # decimal type
    create_setting('attendance', 'daily_work_threshold', '5.0', 'Minimum hours to avoid full-day absence', true, 3) # decimal type
    create_setting('attendance', 'accumulated_hours_threshold', '9.0', 'Missing hours threshold before converting to unpaid leave days', true, 3) # decimal type
    create_setting('attendance', 'exclude_weekends', 'true', 'Exclude weekends from attendance deduction calculations', true, 2) # boolean type
    create_setting('attendance', 'exclude_holidays', 'true', 'Exclude holidays from attendance deduction calculations', true, 2) # boolean type
  end

  def down
    # Remove the settings we added
    remove_setting('attendance', 'weekend_days')
    remove_setting('attendance', 'daily_expected_hours')
    remove_setting('attendance', 'daily_work_threshold')
    remove_setting('attendance', 'accumulated_hours_threshold')
    remove_setting('attendance', 'exclude_weekends')
    remove_setting('attendance', 'exclude_holidays')
  end

  private

  def create_setting(namespace, key, value, description, is_editable = true, setting_type = 0)
    # Use raw SQL to avoid enum issues during migration and handle conflicts
    execute(<<~SQL)
      INSERT INTO settings (namespace, key, value, description, is_editable, setting_type, created_at, updated_at)
      VALUES ('#{namespace}', '#{key}', '#{value}', '#{description}', #{is_editable}, #{setting_type}, NOW(), NOW())
      ON CONFLICT (namespace, key) DO UPDATE SET
        value = EXCLUDED.value,
        description = EXCLUDED.description,
        is_editable = EXCLUDED.is_editable,
        setting_type = EXCLUDED.setting_type,
        updated_at = NOW()
    SQL
  end

  def remove_setting(namespace, key)
    # Use raw SQL to avoid model issues during migration
    execute("DELETE FROM settings WHERE namespace = '#{namespace}' AND key = '#{key}'")
  end
end
