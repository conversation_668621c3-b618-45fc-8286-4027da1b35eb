class CreateApprovalActions < ActiveRecord::Migration[8.0]
  def change
    create_table :approval_actions do |t|
      t.references :approval_request, null: false, foreign_key: true
      t.references :approval_step, null: false, foreign_key: true
      t.string :user_id, null: false
      t.string :action, null: false
      t.text :comment
      t.timestamps

      t.index [:approval_step_id, :user_id, :action], name: 'index_approval_actions_on_step_user_action'
    end
  end
end
