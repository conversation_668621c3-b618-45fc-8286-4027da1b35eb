class CreateSettings < ActiveRecord::Migration[8.0]
  def change
    create_table :settings do |t|
      t.string :namespace, null: false, default: 'system'
      t.string :key, null: false
      t.text :value, null: false
      t.text :description
      t.boolean :is_editable, default: true

      t.timestamps
    end

    add_index :settings, [:namespace, :key], unique: true

    # Add default settings
    reversible do |dir|
      dir.up do
        # Define default settings
        default_settings = [
          { namespace: 'attendance', key: 'work_start_time', value: '09:00', description: 'Default work start time' },
          { namespace: 'attendance', key: 'work_end_time', value: '17:00', description: 'Default work end time' },
          { namespace: 'attendance', key: 'duplicate_threshold_seconds', value: '60', description: 'Threshold for identifying duplicate events in seconds' },
          { namespace: 'attendance', key: 'required_work_minutes', value: '480', description: 'Required work minutes per day (8 hours)' },
          { namespace: 'attendance', key: 'break_threshold_minutes', value: '120', description: 'Maximum break duration before flagging (2 hours)' }
        ]

        # Insert settings using ActiveRecord's insert method
        now = Time.current

        default_settings.each do |setting|
          # Use the insert method with a table name and column-value hash
          attrs = {
            namespace: setting[:namespace],
            key: setting[:key],
            value: setting[:value],
            description: setting[:description],
            is_editable: setting[:is_editable] || true,
            created_at: now,
            updated_at: now
          }

          # Use the insert method with a column-value hash
          connection.insert_fixture(attrs, 'settings')
        end
      end
    end
  end
end
