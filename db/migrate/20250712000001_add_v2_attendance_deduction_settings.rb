class AddV2AttendanceDeductionSettings < ActiveRecord::Migration[8.0]
  def up
    # V2 Attendance Deduction Settings
    create_setting('attendance', 'deductions_enabled', 'false', 'Enable V2 on-the-fly attendance deductions', true, :boolean)
    create_setting('attendance', 'daily_expected_hours', '8.0', 'Expected work hours per day for deduction calculations', true, :decimal)
    create_setting('attendance', 'daily_work_threshold', '5.0', 'Minimum hours to avoid full-day absence', true, :decimal)
    create_setting('attendance', 'accumulated_hours_threshold', '9.0', 'Missing hours threshold before converting to unpaid leave days', true, :decimal)
    create_setting('attendance', 'exclude_weekends', 'true', 'Exclude weekends from attendance deduction calculations', true, :boolean)
    create_setting('attendance', 'exclude_holidays', 'true', 'Exclude holidays from attendance deduction calculations', true, :boolean)
  end

  def down
    # Remove V2 settings
    remove_setting('attendance', 'deductions_enabled')
    remove_setting('attendance', 'daily_expected_hours')
    remove_setting('attendance', 'daily_work_threshold')
    remove_setting('attendance', 'accumulated_hours_threshold')
    remove_setting('attendance', 'exclude_weekends')
    remove_setting('attendance', 'exclude_holidays')
  end

  private

  def create_setting(namespace, key, value, description, is_editable = true, setting_type = :string)
    # Convert setting_type symbol to integer
    type_mapping = {
      string: 0,
      integer: 1,
      boolean: 2,
      decimal: 3,
      time: 4
    }
    type_int = type_mapping[setting_type] || 0

    # Use raw SQL to avoid enum issues during migration
    execute(<<~SQL)
      INSERT INTO settings (namespace, key, value, description, is_editable, setting_type, created_at, updated_at)
      VALUES ('#{namespace}', '#{key}', '#{value}', '#{description}', #{is_editable}, #{type_int}, NOW(), NOW())
      ON CONFLICT (namespace, key) DO UPDATE SET
        value = EXCLUDED.value,
        description = EXCLUDED.description,
        is_editable = EXCLUDED.is_editable,
        setting_type = EXCLUDED.setting_type,
        updated_at = NOW()
    SQL
  end

  def remove_setting(namespace, key)
    # Use raw SQL to avoid model issues during migration
    execute("DELETE FROM settings WHERE namespace = '#{namespace}' AND key = '#{key}'")
  end
end
