class CreateSalaryCalculations < ActiveRecord::Migration[8.0]
  def change
    create_table :salary_calculations do |t|
      t.references :employee, null: false, foreign_key: true
      t.references :salary_package, null: false, foreign_key: true
      t.string :period, null: false # Format: YYYY-MM
      t.date :period_start_date, null: false
      t.date :period_end_date, null: false
      t.decimal :gross_salary, precision: 10, scale: 2, null: false
      t.jsonb :deductions, default: {}, null: false
      t.decimal :net_salary, precision: 10, scale: 2, null: false
      t.integer :status, default: 0, null: false
      t.datetime :calculation_date
      t.datetime :payment_date
      t.references :approved_by, foreign_key: { to_table: :employees }
      t.text :notes

      t.timestamps
    end

    add_index :salary_calculations, [:employee_id, :period], unique: true
    add_index :salary_calculations, :period
    add_index :salary_calculations, :status
  end
end
