class AddSettingTypeToSettings < ActiveRecord::Migration[8.0]
  def change
    add_column :settings, :setting_type, :integer, default: 0, null: false
    add_index :settings, :setting_type

    # Update existing settings with appropriate types
    reversible do |dir|
      dir.up do
        update_existing_setting_types
      end
    end
  end

  private

  def update_existing_setting_types
    puts "Updating existing settings with appropriate types..."

    # Time settings (HH:MM format)
    execute(<<~SQL)
      UPDATE settings SET setting_type = 4
      WHERE key IN ('work_start_time', 'work_end_time')
    SQL

    # Integer settings
    execute(<<~SQL)
      UPDATE settings SET setting_type = 1
      WHERE key IN (
        'duplicate_threshold_seconds',
        'required_work_minutes',
        'break_threshold_minutes'
      )
    SQL

    # Boolean settings
    execute(<<~SQL)
      UPDATE settings SET setting_type = 2
      WHERE key IN (
        'auto_leave_enabled',
        'deductions_enabled',
        'auto_leave_counts_against_balance',
        'notify_managers_auto_leave',
        'notify_employees_auto_leave',
        'notify_auto_leave_created'
      )
    SQL

    # Decimal settings
    execute(<<~SQL)
      UPDATE settings SET setting_type = 3
      WHERE key IN (
        'daily_work_threshold',
        'accumulated_hours_threshold',
        'monthly_expected_hours'
      )
    SQL

    # Email settings
    execute(<<~SQL)
      UPDATE settings SET setting_type = 6
      WHERE namespace = 'company' AND key = 'email'
    SQL

    # Phone settings
    execute(<<~SQL)
      UPDATE settings SET setting_type = 7
      WHERE namespace = 'company' AND key = 'phone'
    SQL

    # URL/Path settings
    execute(<<~SQL)
      UPDATE settings SET setting_type = 8
      WHERE key = 'logo_path'
    SQL

    # Array settings (comma-separated values)
    execute(<<~SQL)
      UPDATE settings SET setting_type = 10
      WHERE key = 'weekend_days'
    SQL

    puts "✅ Setting types updated successfully!"
  end
end
