class CreateSalaryPackages < ActiveRecord::Migration[8.0]
  def change
    create_table :salary_packages do |t|
      t.references :employee, null: false, foreign_key: true
      t.decimal :base_salary, precision: 10, scale: 2, null: false
      t.decimal :housing_allowance, precision: 10, scale: 2, default: 0.0
      t.decimal :transportation_allowance, precision: 10, scale: 2, default: 0.0
      t.decimal :other_allowances, precision: 10, scale: 2, default: 0.0
      t.date :effective_date, null: false
      t.date :end_date
      t.integer :status, default: 0, null: false
      t.text :adjustment_reason
      t.integer :previous_package_id
      t.text :notes

      t.timestamps
    end

    add_index :salary_packages, [:employee_id, :effective_date]
    add_index :salary_packages, :status
    add_index :salary_packages, :previous_package_id
  end
end
