class CreateAttendanceSystem < ActiveRecord::Migration[8.0]
  def change
    # Create attendance devices table
    create_table :attendance_devices do |t|
      t.string :name, null: false
      t.string :adapter_type, null: false
      t.string :ip_address
      t.integer :port
      t.integer :status, null: false, default: 0
      t.string :location
      t.jsonb :connection_config, default: {}
      t.jsonb :sync_config, default: {}
      t.jsonb :capabilities, default: {}
      t.datetime :last_seen_at
      t.text :notes
      t.timestamps
    end

    add_index :attendance_devices, :name, unique: true
    add_index :attendance_devices, :adapter_type
    add_index :attendance_devices, :status
    add_index :attendance_devices, :ip_address
    add_index :attendance_devices, [:adapter_type, :status]
    add_index :attendance_devices, :connection_config, using: :gin
    add_index :attendance_devices, :capabilities, using: :gin

    # Create attendance sync logs table
    create_table :attendance_sync_logs do |t|
      t.references :attendance_device, null: false, foreign_key: true
      t.references :triggered_by, null: true, foreign_key: { to_table: :employees }
      t.references :employee, null: true, foreign_key: true
      t.integer :status, null: false, default: 0
      t.integer :sync_type, null: false, default: 0
      t.string :operation_type, null: true
      t.datetime :started_at
      t.datetime :completed_at
      t.text :sync_params
      t.text :result_summary
      t.text :error_details
      t.integer :retry_count, default: 0
      t.timestamp :next_retry_at
      t.timestamps
    end

    add_index :attendance_sync_logs, :status
    add_index :attendance_sync_logs, :sync_type
    add_index :attendance_sync_logs, :started_at
    add_index :attendance_sync_logs, [:attendance_device_id, :status]
    add_index :attendance_sync_logs, [:attendance_device_id, :started_at]
    add_index :attendance_sync_logs, [:employee_id, :sync_type]
    add_index :attendance_sync_logs, [:status, :next_retry_at]
    add_index :attendance_sync_logs, :operation_type
    add_index :attendance_sync_logs, [:attendance_device_id, :employee_id, :operation_type],
              name: 'idx_sync_logs_device_employee_operation'

    # Create employee device mappings table (multi-device support)
    create_table :employee_device_mappings do |t|
      t.references :employee, null: false, foreign_key: true
      t.references :attendance_device, null: false, foreign_key: { to_table: :attendance_devices }
      t.string :device_user_id, null: false
      t.text :notes, null: true
      t.timestamps
    end

    # Ensure one employee can only have one mapping per device
    add_index :employee_device_mappings, [ :employee_id, :attendance_device_id ],
              unique: true, name: 'idx_employee_device_unique'

    # Ensure one device user ID can only belong to one employee per device
    add_index :employee_device_mappings, [ :attendance_device_id, :device_user_id ],
              unique: true, name: 'idx_device_user_unique'

    # Performance index for lookups
    add_index :employee_device_mappings, :device_user_id

    # Add source_device_id to attendance_events
    add_column :attendance_events, :source_device_id, :integer
    add_column :attendance_events, :raw_data, :text
    add_index :attendance_events, :source_device_id
    add_foreign_key :attendance_events, :attendance_devices, column: :source_device_id
  end
end
