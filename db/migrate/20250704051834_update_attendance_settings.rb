class UpdateAttendanceSettings < ActiveRecord::Migration[8.0]
  def up
    # Create attendance settings for the new system
    puts "Creating attendance system settings..."

    # Core attendance settings
    create_setting('attendance', 'daily_work_threshold', '5.0', 'Minimum hours per day to avoid leave detection')
    create_setting('attendance', 'accumulated_hours_threshold', '9.0', 'Accumulated missing hours threshold for leave conversion')
    create_setting('attendance', 'monthly_expected_hours', '180.0', 'Expected working hours per month')
    create_setting('attendance', 'weekend_days', '5,6', 'Weekend days (0=Sunday, 1=Monday, ..., 6=Saturday)')

    # Auto-generated leave settings
    create_setting('attendance', 'auto_leave_enabled', 'true', 'Enable automatic leave detection')
    create_setting('attendance', 'auto_leave_counts_against_balance', 'false', 'Whether auto-generated leaves count against leave balance')

    # Notification settings
    create_setting('attendance', 'notify_managers_auto_leave', 'true', 'Notify managers when auto-generated leaves are created')
    create_setting('attendance', 'notify_employees_auto_leave', 'true', 'Notify employees when auto-generated leaves are created')
    create_setting('attendance', 'notify_auto_leave_created', 'true', 'Send notifications when auto-generated leaves are created')

    puts "Attendance settings created successfully"
  end

  def down
    # Remove attendance settings
    puts "Removing attendance system settings..."

    setting_keys = [
      'daily_work_threshold',
      'accumulated_hours_threshold',
      'monthly_expected_hours',
      'weekend_days',
      'auto_leave_enabled',
      'auto_leave_counts_against_balance',
      'notify_managers_auto_leave',
      'notify_employees_auto_leave',
      'notify_auto_leave_created'
    ]

    setting_keys.each do |key|
      execute("DELETE FROM settings WHERE namespace = 'attendance' AND key = '#{key}'")
    end

    puts "Attendance settings removed"
  end

  private

  def create_setting(namespace, key, value, description)
    execute(<<~SQL)
      INSERT INTO settings (namespace, key, value, description, created_at, updated_at)
      VALUES ('#{namespace}', '#{key}', '#{value}', '#{description}', NOW(), NOW())
      ON CONFLICT (namespace, key) DO UPDATE SET
        value = EXCLUDED.value,
        description = EXCLUDED.description,
        updated_at = NOW()
    SQL
  end
end
