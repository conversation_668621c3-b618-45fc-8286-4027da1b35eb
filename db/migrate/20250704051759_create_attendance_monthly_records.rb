class CreateAttendanceMonthlyRecords < ActiveRecord::Migration[8.0]
  def change
    create_table :attendance_monthly_records do |t|
      t.references :employee, null: false, foreign_key: true
      t.integer :year, null: false
      t.integer :month, null: false
      t.decimal :expected_hours, precision: 8, scale: 2, null: false, default: 180.0
      t.decimal :actual_hours, precision: 8, scale: 2, null: false, default: 0.0
      t.decimal :deficit_hours, precision: 8, scale: 2, null: false, default: 0.0

      t.timestamps
    end

    # Indexes for efficient querying
    add_index :attendance_monthly_records, [ :employee_id, :year, :month ], unique: true
    add_index :attendance_monthly_records, [ :year, :month ]
    add_index :attendance_monthly_records, :deficit_hours
  end
end
