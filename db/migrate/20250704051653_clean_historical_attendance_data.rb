class CleanHistoricalAttendanceData < ActiveRecord::Migration[8.0]
  def up
    # Using DELETE instead of TRUNCATE for foreign key safety


    # Delete attendance periods (safe with foreign keys)
    deleted_periods = execute("DELETE FROM attendance_periods").cmd_tuples

    # Delete attendance events (safe with foreign keys)
    deleted_events = execute("DELETE FROM attendance_events").cmd_tuples

    # Delete attendance summaries (safe with foreign keys)
    deleted_summaries = execute("DELETE FROM attendance_summaries").cmd_tuples
  end

  def down
    # Cannot restore deleted data
  end
end
