class CreateAttendanceSummaries < ActiveRecord::Migration[8.0]
  def change
    create_table :attendance_summaries do |t|
      t.references :employee, null: false, foreign_key: true
      t.date :date, null: false
      t.datetime :first_check_in
      t.datetime :last_check_out
      t.integer :total_duration_minutes, default: 0
      t.string :work_status
      t.integer :event_count, default: 0
      t.integer :undetermined_count, default: 0
      t.text :notes

      t.timestamps
    end

    add_index :attendance_summaries, [ :employee_id, :date ], unique: true
  end
end
