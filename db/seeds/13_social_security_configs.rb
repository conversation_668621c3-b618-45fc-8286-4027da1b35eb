# Create social security configuration based on Jordan's social security laws
if defined?(SocialSecurityConfig)
  puts "Creating social security configuration..."

  # Check if we already have social security configs
  if SocialSecurityConfig.count > 0
    puts "Social security configurations already exist. Skipping."
  else
    # Create a social security config with Jordan's rates
    # Note: These are example values and should be updated with the latest rates
    SocialSecurityConfig.create!(
      employee_rate: 7.5, # 7.5%
      employer_rate: 14.25, # 14.25%
      max_salary: 3612, # JOD per month
      effective_date: Date.new(2023, 1, 1)
    )

    puts "✅ Social security configuration created!"
  end
else
  puts "SocialSecurityConfig model not defined yet. Skipping social security configuration creation."
end
