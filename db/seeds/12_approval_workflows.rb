# Create approval workflow for leave requests
if defined?(ApprovalWorkflow) && defined?(ApprovalStep)
  puts "Creating leave approval workflow..."

  # Advanced Leave Approval Workflow
  advanced_leave_workflow = ApprovalWorkflow.find_or_create_by(
    name: "Leave Approval",
    action: "request_leave",
    subject_class: "Leave",
    system_name: "people"
  ) do |workflow|
    workflow.description = "Advanced approval workflow for leave requests with context and hierarchy"
    workflow.active = true
    workflow.condition = "days.to_i > 0"
    workflow.required_context_keys = [ "leave_type", "days", "priority" ]
    workflow.empty_steps_behavior = "error"
  end

  # Step 1: Manager Approval
  advanced_manager_step = advanced_leave_workflow.approval_steps.find_or_create_by(sequence: 1) do |step|
    step.name = "Manager Approval"
    step.approval_type = "any"
    step.dynamic_approver_method = "direct_manager"
    step.description = "Approval from the direct (project) manager"
    step.condition = "true" # Always include this step
    step.skip_if_no_approvers = true # Skip if no manager is found, will fall back to Admin step
  end

  # Step 2: HR Approval based on leave type
  advanced_hr_step = advanced_leave_workflow.approval_steps.find_or_create_by(sequence: 2) do |step|
    step.name = "HR Approval"
    step.approval_type = "any"
    step.dynamic_approver_method = "leave_type_based_hr_approvers"
    step.description = "Approval from HR based on leave type"
    step.condition = "true" # Always include this step
    step.skip_if_no_approvers = false
  end

  # Step 3: Admin Approval - only included if no approvers were found for Step 1
  # or if the leave is long duration or high priority
  advanced_admin_step = advanced_leave_workflow.approval_steps.find_or_create_by(sequence: 3) do |step|
    step.name = "Admin Approval"
    step.approval_type = "any"
    step.dynamic_approver_method = "admin_users"
    step.description = "Approval from admin when no manager is available or for special cases"
    step.condition = "previous_steps[1][:approvers].empty? || days.to_i > 14 || priority == 'high'"
    step.skip_if_no_approvers = false
  end

  puts "✅ Leave approval workflow created!"
end
