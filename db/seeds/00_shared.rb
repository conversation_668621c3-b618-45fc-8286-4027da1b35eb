# This file contains shared variables that are used across multiple seed files

# Define departments (used as enum in Employee model)
$department_enum = {
  admin: "Administration",
  hr: "Human Resources",
  finance: "Finance",
  operations: "Operations",
  it: "Information Technology",
  programs: "Programs",
  procurement: "Procurement"
}

# Define leave types
$leave_types = [
  { name: "Annual Leave", code: "AL", days_per_year: 21, description: "Regular vacation leave" },
  { name: "Sick Leave", code: "SL", days_per_year: 14, description: "Leave for illness or medical appointments" },
  { name: "Maternity Leave", code: "ML", days_per_year: 90, description: "Leave for childbirth and recovery" },
  { name: "Paternity Leave", code: "PL", days_per_year: 5, description: "Leave for fathers after childbirth" },
  { name: "Bereavement Leave", code: "BL", days_per_year: 3, description: "Leave for family death" },
  { name: "Unpaid Leave", code: "UL", days_per_year: 0, description: "Leave without pay" }
]

# Define employment types
$employment_types = [
  { name: "Full-time", code: "FT", description: "Regular full-time employee" },
  { name: "Part-time", code: "PT", description: "Regular part-time employee" },
  { name: "Contract", code: "CT", description: "Fixed-term contract employee" },
  { name: "Consultant", code: "CN", description: "External consultant" },
  { name: "Intern", code: "IN", description: "Internship position" },
  { name: "Volunteer", code: "VL", description: "Volunteer position" }
]

# Define sample users (these come from the core service 04_users.rb)
# These are used for development and testing
$sample_users = [
  { id: 1, name: "Super Admin User", email: "<EMAIL>", roles: [ "super_admin" ], global: true, department: :admin },
  { id: 2, name: "Admin User", email: "<EMAIL>", roles: [ "admin" ], global: true, department: :admin },
  { id: 3, name: "Supervisor User", email: "<EMAIL>", roles: [ "supervisor", "project_manager" ], department: :programs },
  { id: 4, name: "Case Manager User", email: "<EMAIL>", roles: [ "case_manager" ], department: :programs },
  { id: 5, name: "Project Manager User", email: "<EMAIL>", roles: [ "project_manager", "supervisor" ], department: :programs },
  { id: 6, name: "Employee User", email: "<EMAIL>", roles: [ "employee" ], department: :operations },
  { id: 7, name: "Financial Manager", email: "<EMAIL>", roles: [ "financial_manager" ], global: true, department: :finance },
  { id: 8, name: "Accountant User", email: "<EMAIL>", roles: [ "accountant" ], global: true, department: :finance },
  { id: 9, name: "HR Manager User", email: "<EMAIL>", roles: [ "hr_manager" ], global: true, department: :hr },
  { id: 10, name: "HR Officer User", email: "<EMAIL>", roles: [ "hr_officer" ], global: true, department: :hr },
  { id: 11, name: "Procurement Manager", email: "<EMAIL>", roles: [ "procurement_manager" ], department: :procurement },
  { id: 12, name: "Procurement Officer", email: "<EMAIL>", roles: [ "procurement_officer" ], department: :procurement }
]

# Define actual employees from the core service (these should match the core service employee data)
# These are the real Athar employees from 09_athar_employees.rb (UPDATED TO MATCH CORE SERVICE)
$athar_employees = [
  # Management and Admin
  { name: "Mohammad Wael Ayob Musa", email: "<EMAIL>", role: "procurement_officer", department: :procurement },
  { name: "Mays Nail Ali Saleh", email: "<EMAIL>", role: "graphic_designer", department: :operations },
  { name: "Farah Mohammad Abedallah Almbarak", email: "<EMAIL>", role: "social_media_specialist", department: :operations },
  { name: "Gufran Awaad Salamh Alkhzalh", email: "<EMAIL>", role: "coach", department: :programs },
  { name: "Anas Mazen Kamel Beliah", email: "<EMAIL>", role: "admin", department: :admin },
  { name: "Sabreen Mahemed Abdalah Sharah", email: "<EMAIL>", role: "coach", department: :programs },
  { name: "Marah Mamoon Jameel Alfaqeh", email: "<EMAIL>", role: "financial_manager", department: :finance },
  { name: "Alaa Naser Al Zoghbe", email: "<EMAIL>", role: "accountant", department: :finance },
  { name: "Samia Hamdi", email: "<EMAIL>", role: "hr_officer", department: :hr },
  { name: "Sondos Jamil Mohmmud Abu Khadair", email: "<EMAIL>", role: "project_manager", department: :programs },

  # Case Managers and Program Staff
  { name: "Rahaf Marwan Abdel Jabbar Ismail", email: "<EMAIL>", role: "case_manager", department: :programs },
  { name: "Heba Saleh Mohammad Alzubi", email: "<EMAIL>", role: "case_manager", department: :programs },
  { name: "Abedalhakim Mohammad Soliman Alkawaldeh", email: "<EMAIL>", role: "case_manager", department: :programs },
  { name: "Raghad Mahmoud Mohammad Ghanam", email: "<EMAIL>", role: "psychologist", department: :programs },
  { name: "Rama Akram Badiea Khader", email: "<EMAIL>", role: "case_manager", department: :programs },
  { name: "Ahmad Saleh Ahmad Alqaisi", email: "<EMAIL>", role: "coach", department: :programs },
  { name: "Shoroq Naim Jaber Alsaqkri", email: "<EMAIL>", role: "coach", department: :programs },
  { name: "Dania Khaled Bakeer", email: "<EMAIL>", role: "case_manager", department: :programs },
  { name: "Sara Zakaria Lutfi Jaber", email: "<EMAIL>", role: "case_manager", department: :programs },
  { name: "Tasnim Rafat Abdel Rahim Saleh", email: "<EMAIL>", role: "case_manager", department: :programs },

  # Additional Staff
  { name: "Esraa Mohammad Tawfik Alkhaderee", email: "<EMAIL>", role: "case_manager", department: :programs },
  { name: "Reem Ramzi Hasan Alowaisy", email: "<EMAIL>", role: "club_facilitator", department: :programs },
  { name: "Saeed Jamal Al Eleisah", email: "<EMAIL>", role: "project_manager", department: :programs },
  { name: "Ola Mohammad Estaitia", email: "<EMAIL>", role: "program_manager", department: :programs },
  { name: "Abdalrhman Mousa", email: "<EMAIL>", role: "employee", department: :operations },
  { name: "Jenan Jamel Khaleel Yousef", email: "<EMAIL>", role: "graphic_designer", department: :operations },
  { name: "Samar Mahmoud Abdallah Alkhatib", email: "<EMAIL>", role: "housekeeping", department: :operations },
  { name: "Rawan Ibraheem Abdalah Hseen", email: "<EMAIL>", role: "coach", department: :programs },
  { name: "Eman Ayman Ahmad Ibrahim", email: "<EMAIL>", role: "coach", department: :programs },
  { name: "Oday Mohammad Ali Estaitia", email: "<EMAIL>", role: "coach", department: :programs },
  { name: "Mohammad Amer Asi", email: "<EMAIL>", role: "community_mobilizer", department: :programs },
  { name: "Yaqin Wael Ayoub Mousa", email: "<EMAIL>", role: "community_mobilizer", department: :programs },
  { name: "Ata Mohammad Elhaj", email: "<EMAIL>", role: "club_facilitator", department: :programs },
  { name: "Dila", email: "<EMAIL>", role: "employee", department: :operations },
  { name: "Sondos Farhan A. Alhayek", email: "<EMAIL>", role: "volunteer", department: :programs },
  { name: "Walaa Qondos", email: "<EMAIL>", role: "volunteer", department: :programs },
  { name: "Bayan Mohammad Said Abushalha", email: "<EMAIL>", role: "volunteer", department: :programs },
  { name: "Maram Othman Mustafa Mohammad", email: "<EMAIL>", role: "volunteer", department: :programs },
  { name: "Aws Abd Elhakim Mohd Abu Jado", email: "<EMAIL>", role: "volunteer", department: :programs },
  { name: "Abedalrahman Ayoub Hasan Ismail", email: "<EMAIL>", role: "volunteer", department: :programs },
  { name: "Raniam Rami Alfaqeeh", email: "<EMAIL>", role: "volunteer", department: :programs },
  { name: "Fatima Rajeh Khaleel", email: "<EMAIL>", role: "community_animator", department: :programs },
  { name: "Ahmad Mahmoud Ahmad Dalal", email: "<EMAIL>", role: "community_animator", department: :programs },
  { name: "Maysoon Yousef Suleiman Hejawi", email: "<EMAIL>", role: "community_animator", department: :programs },
  { name: "Salsabil Faisal Yousef Abdalgani", email: "<EMAIL>", role: "community_animator", department: :programs },
  { name: "Anwar Fatehi Shehadeh Abuajjuir", email: "<EMAIL>", role: "community_animator", department: :programs },
  { name: "Wejdan Rafiq Orabi Hamdan", email: "<EMAIL>", role: "community_animator", department: :programs },
  { name: "Wessam Taiel Farhan Albashhbsheh", email: "<EMAIL>", role: "community_animator", department: :programs },
  { name: "Mahmoud Yaser Abdalslam Abdil Jaleel", email: "<EMAIL>", role: "community_animator", department: :programs },
  { name: "Ehab Jihad Ahmad Al-Bazz", email: "<EMAIL>", role: "community_animator", department: :programs },
  { name: "Bayan Hayel Farhan Madah", email: "<EMAIL>", role: "community_animator", department: :programs },
  { name: "Wafaa Suleiman Hasan Alhkook", email: "<EMAIL>", role: "community_animator", department: :programs }
]

# Define default settings for attendance
$default_settings = [
  { namespace: 'attendance', key: 'work_start_time', value: '09:00', description: 'Default work start time' },
  { namespace: 'attendance', key: 'work_end_time', value: '17:00', description: 'Default work end time' },
  { namespace: 'attendance', key: 'duplicate_threshold_seconds', value: '60', description: 'Threshold for identifying duplicate events in seconds' },
  { namespace: 'attendance', key: 'required_work_minutes', value: '480', description: 'Required work minutes per day (8 hours)' },
  { namespace: 'attendance', key: 'break_threshold_minutes', value: '120', description: 'Maximum break duration before flagging (2 hours)' }
]
