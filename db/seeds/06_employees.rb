# Create employees based on both sample users and actual user data
puts "Creating employees from sample users and actual user data..."

def create_employee_record(user_data, user_id, index, total_employees)
  puts "Processing employee: #{user_data[:name]} (#{user_data[:email]})"

  # Create or find an employee
  employee = Employee.find_or_initialize_by(user_id: user_id)

  # Set department based on employee data
  employee.department = user_data[:department]

  # Set start date incrementally (older employees have earlier start dates)
  # Spread dates over the last 2 years (730 days)
  days_ago = ((total_employees - index).to_f / total_employees * 700).to_i + 30
  employee.start_date ||= Date.today - days_ago.days

  employee.status ||= :active

  # Save the employee (skip validations for seeding to avoid phone format issues)
  employee.save!(validate: false)
  puts "✅ Created/updated employee with ID: #{employee.id} (start_date: #{employee.start_date})"
end

# Create employees for sample users (from core service 04_users.rb)
puts "Creating employees for sample users..."
total_employees = $sample_users.length + $athar_employees.length
$sample_users.each_with_index do |user_data, index|
  create_employee_record(user_data, user_data[:id], index, total_employees)
end

# Create employees for real Athar employees (from core service 09_athar_employees.rb)
puts "Creating employees for Athar employees..."
# Use sequential user_ids continuing from sample users (13-64)
$athar_employees.each_with_index do |employee_data, index|
  user_id = $sample_users.length + index + 1 # Start from 12 + 1 = 13
  create_employee_record(employee_data, user_id, $sample_users.length + index, total_employees)
end

puts "✅ Employees created successfully!"
puts "📊 Processed #{$sample_users.length} sample users and #{$athar_employees.length} Athar employees"
puts "📊 Total: #{$sample_users.length + $athar_employees.length} employees"
