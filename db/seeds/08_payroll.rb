# Create sample payroll records if the model exists
if defined?(Payroll)
  puts "Creating sample payroll records..."

  # Create payroll periods for the last 3 months
  3.times do |i|
    month = Date.today.month - i
    year = Date.today.year
    if month <= 0
      month += 12
      year -= 1
    end

    period = "#{year}-#{month.to_s.rjust(2, '0')}"

    # Create a payroll record for each employee for this period
    Employee.all.each do |employee|
      base_salary = rand(3000..10000)

      Payroll.create!(
        employee_id: employee.id,
        period: period,
        base_salary: base_salary,
        allowances: rand(500..2000),
        deductions: rand(300..1000),
        tax: base_salary * 0.15,
        net_salary: base_salary + rand(500..2000) - rand(300..1000) - (base_salary * 0.15),
        status: [ "draft", "approved", "paid" ].sample,
        payment_date: Date.new(year, month, rand(25..28))
      )
    end
  end
else
  puts "Payroll model not defined yet. Skipping payroll creation."
end
