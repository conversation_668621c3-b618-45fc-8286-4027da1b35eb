# Helper methods for creating different attendance scenarios
def create_normal_workday(employee, date)
  # Morning check-in (8:55 - 9:05) - handle hour overflow properly
  checkin_minutes = rand(55..65)
  checkin_hour = checkin_minutes >= 60 ? 9 : 8
  checkin_min = checkin_minutes >= 60 ? checkin_minutes - 60 : checkin_minutes
  checkin_time = date.to_time.change(hour: checkin_hour, min: checkin_min)
  Attendance::Event.create!(
    employee: employee,
    timestamp: checkin_time.to_i,
    event_type: :check_in,
    activity_type: :regular,
    skip_period_calculation: true
  )

  # Lunch break start (12:00 - 12:30)
  lunch_start = date.to_time.change(hour: 12, min: rand(0..30))
  Attendance::Event.create!(
    employee: employee,
    timestamp: lunch_start.to_i,
    event_type: :check_out,
    activity_type: :lunch,
    skip_period_calculation: true
  )

  # Lunch break end (13:00 - 13:30)
  lunch_end = date.to_time.change(hour: 13, min: rand(0..30))
  Attendance::Event.create!(
    employee: employee,
    timestamp: lunch_end.to_i,
    event_type: :check_in,
    activity_type: :regular,
    skip_period_calculation: true
  )

  # Evening check-out (17:00 - 17:15)
  checkout_time = date.to_time.change(hour: 17, min: rand(0..15))
  Attendance::Event.create!(
    employee: employee,
    timestamp: checkout_time.to_i,
    event_type: :check_out,
    activity_type: :regular,
    skip_period_calculation: true
  )
end

def create_late_arrival(employee, date)
  # Late check-in (9:30 - 10:00) - handle hour overflow properly
  checkin_minutes = rand(30..60)
  checkin_hour = checkin_minutes >= 60 ? 10 : 9
  checkin_min = checkin_minutes >= 60 ? checkin_minutes - 60 : checkin_minutes
  checkin_time = date.to_time.change(hour: checkin_hour, min: checkin_min)
  Attendance::Event.create!(
    employee: employee,
    timestamp: checkin_time.to_i,
    event_type: :check_in,
    activity_type: :regular,
    skip_period_calculation: true
  )

  # Lunch break start
  lunch_start = date.to_time.change(hour: 12, min: rand(30..45))
  Attendance::Event.create!(
    employee: employee,
    timestamp: lunch_start.to_i,
    event_type: :check_out,
    activity_type: :lunch,
    skip_period_calculation: true
  )

  # Lunch break end
  lunch_end = date.to_time.change(hour: 13, min: rand(30..45))
  Attendance::Event.create!(
    employee: employee,
    timestamp: lunch_end.to_i,
    event_type: :check_in,
    activity_type: :regular,
    skip_period_calculation: true
  )

  # Extended evening check-out to compensate (17:30 - 18:00)
  checkout_minutes = rand(30..60)
  checkout_hour = checkout_minutes >= 60 ? 18 : 17
  checkout_min = checkout_minutes >= 60 ? checkout_minutes - 60 : checkout_minutes
  checkout_time = date.to_time.change(hour: checkout_hour, min: checkout_min)
  Attendance::Event.create!(
    employee: employee,
    timestamp: checkout_time.to_i,
    event_type: :check_out,
    activity_type: :regular,
    skip_period_calculation: true
  )
end

def create_early_departure(employee, date)
  # Normal check-in (8:55 - 9:05) - handle hour overflow properly
  checkin_minutes = rand(55..65)
  checkin_hour = checkin_minutes >= 60 ? 9 : 8
  checkin_min = checkin_minutes >= 60 ? checkin_minutes - 60 : checkin_minutes
  checkin_time = date.to_time.change(hour: checkin_hour, min: checkin_min)
  Attendance::Event.create!(
    employee: employee,
    timestamp: checkin_time.to_i,
    event_type: :check_in,
    activity_type: :regular,
    skip_period_calculation: true
  )

  # Short lunch break
  lunch_start = date.to_time.change(hour: 12, min: rand(0..15))
  Attendance::Event.create!(
    employee: employee,
    timestamp: lunch_start.to_i,
    event_type: :check_out,
    activity_type: :lunch,
    skip_period_calculation: true
  )

  # Lunch end (12:45 - 13:00) - handle hour overflow properly
  lunch_end_minutes = rand(45..60)
  lunch_end_hour = lunch_end_minutes >= 60 ? 13 : 12
  lunch_end_min = lunch_end_minutes >= 60 ? lunch_end_minutes - 60 : lunch_end_minutes
  lunch_end = date.to_time.change(hour: lunch_end_hour, min: lunch_end_min)
  Attendance::Event.create!(
    employee: employee,
    timestamp: lunch_end.to_i,
    event_type: :check_in,
    activity_type: :regular,
    skip_period_calculation: true
  )

  # Early departure (16:00 - 16:30)
  checkout_time = date.to_time.change(hour: 16, min: rand(0..30))
  Attendance::Event.create!(
    employee: employee,
    timestamp: checkout_time.to_i,
    event_type: :check_out,
    activity_type: :regular,
    skip_period_calculation: true
  )
end

def create_overtime_day(employee, date)
  # Early check-in
  checkin_time = date.to_time.change(hour: 8, min: rand(30..45))
  Attendance::Event.create!(
    employee: employee,
    timestamp: checkin_time.to_i,
    event_type: :check_in,
    activity_type: :regular,
    skip_period_calculation: true
  )

  # Normal lunch break
  lunch_start = date.to_time.change(hour: 12, min: rand(0..15))
  Attendance::Event.create!(
    employee: employee,
    timestamp: lunch_start.to_i,
    event_type: :check_out,
    activity_type: :lunch,
    skip_period_calculation: true
  )

  lunch_end = date.to_time.change(hour: 13, min: rand(0..15))
  Attendance::Event.create!(
    employee: employee,
    timestamp: lunch_end.to_i,
    event_type: :check_in,
    activity_type: :regular,
    skip_period_calculation: true
  )

  # Overtime check-out (18:00 - 19:00) - handle hour overflow properly
  checkout_minutes = rand(0..60)
  checkout_hour = checkout_minutes >= 60 ? 19 : 18
  checkout_min = checkout_minutes >= 60 ? checkout_minutes - 60 : checkout_minutes
  checkout_time = date.to_time.change(hour: checkout_hour, min: checkout_min)
  Attendance::Event.create!(
    employee: employee,
    timestamp: checkout_time.to_i,
    event_type: :check_out,
    activity_type: :regular,
    skip_period_calculation: true
  )
end

# Create sample attendance events and periods for different scenarios
if defined?(Attendance::Event) && defined?(Attendance::Period)
  puts "Creating sample attendance events and periods..."

  # Clear existing attendance data if needed
  if ENV['CLEAR_ATTENDANCE_DATA'] == 'true'
    puts "Clearing existing attendance data..."
    Attendance::Period.delete_all
    Attendance::Event.delete_all
  end

  # Get employees to create attendance data for (skip first few to avoid admin users)
  employees = Employee.where.not(department: [ :admin ]).limit(10).to_a

  if employees.present?
    puts "Creating attendance data for #{employees.count} employees..."

    # Create different attendance scenarios for the last 30 days
    (1..30).each do |days_ago|
      work_date = Date.today - days_ago.days

      # Skip weekends (Friday and Saturday in Jordan)
      next if work_date.friday? || work_date.saturday?

      employees.each_with_index do |employee, index|
        # Create different scenarios for different employees
        scenario = index % 4

        case scenario
        when 0
          # Normal workday (9:00 - 17:00 with lunch break)
          create_normal_workday(employee, work_date)
        when 1
          # Late arrival
          create_late_arrival(employee, work_date)
        when 2
          # Early departure
          create_early_departure(employee, work_date)
        when 3
          # Overtime day
          create_overtime_day(employee, work_date)
        end

        # Calculate periods for this day
        if defined?(Attendance::PeriodService)
          Attendance::PeriodService.new(employee, work_date).calculate_periods
        end
      end
    end

    puts "✅ Attendance data created successfully!"
  else
    puts "⚠️ No employees found to create attendance data for"
  end
else
  puts "Attendance::Event or Attendance::Period model not defined yet. Skipping attendance creation."
end
