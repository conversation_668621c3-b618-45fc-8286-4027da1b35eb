# Create settings if they don't exist
if defined?(Setting)
  puts "Creating default attendance settings..."
  $default_settings.each do |setting_data|
    Setting.find_or_create_by(namespace: setting_data[:namespace], key: setting_data[:key]) do |setting|
      setting.value = setting_data[:value]
      setting.description = setting_data[:description]
      setting.is_editable = true
    end
  end
  puts "✅ Default attendance settings created!"

  # Create company settings for salary slips
  puts "Creating company settings for salary slips..."
  company_settings = [
    { namespace: 'company', key: 'name', value: 'ATHAR', description: 'Company name' },
    { namespace: 'company', key: 'address', value: '789/1 Sector-2c, 38200 Makkah, Saudi Arabia', description: 'Company address' },
    { namespace: 'company', key: 'phone', value: '966848172194', description: 'Company phone' },
    { namespace: 'company', key: 'email', value: '<EMAIL>', description: 'Company email' },
    { namespace: 'company', key: 'logo_path', value: 'app/assets/images/athar_logo.png', description: 'Company logo path' },
    { namespace: 'company', key: 'industry', value: 'Software Development', description: 'Company industry type' }
  ]

  company_settings.each do |setting_data|
    Setting.find_or_create_by(namespace: setting_data[:namespace], key: setting_data[:key]) do |setting|
      setting.value = setting_data[:value]
      setting.description = setting_data[:description]
      setting.is_editable = true
    end
  end
  puts "✅ Company settings created!"

  # Create payroll settings
  puts "Creating payroll settings..."
  payroll_settings = [
    { namespace: 'payroll', key: 'default_payment_method', value: 'Bank Transfer', description: 'Default payment method for salaries' },
    { namespace: 'payroll', key: 'currency_code', value: 'JOD', description: 'Default currency code' },
    { namespace: 'payroll', key: 'currency_symbol', value: 'JOD', description: 'Currency symbol for display' }
  ]

  payroll_settings.each do |setting_data|
    Setting.find_or_create_by(namespace: setting_data[:namespace], key: setting_data[:key]) do |setting|
      setting.value = setting_data[:value]
      setting.description = setting_data[:description]
      setting.is_editable = true
    end
  end
  puts "✅ Payroll settings created!"
end
