# Create tax configuration based on Jordan's tax laws
if defined?(TaxConfig)
  puts "Creating tax configuration..."

  # Check if we already have tax configs
  if TaxConfig.count > 0
    puts "Tax configurations already exist. Skipping."
  else
    # Create a tax config with Jordan's income tax brackets
    # Note: These are example values and should be updated with the latest tax laws
    TaxConfig.create!(
      name: "Jordan Income Tax 2023",
      effective_date: Date.new(2023, 1, 1),
      config_data: {
        personal_exemption: 9000, # JOD per year
        brackets: [
          {
            min_amount: 0,
            max_amount: 5000,
            rate: 5.0 # 5%
          },
          {
            min_amount: 5000,
            max_amount: 10000,
            rate: 10.0 # 10%
          },
          {
            min_amount: 10000,
            max_amount: 15000,
            rate: 15.0 # 15%
          },
          {
            min_amount: 15000,
            max_amount: 20000,
            rate: 20.0 # 20%
          },
          {
            min_amount: 20000,
            max_amount: nil, # No upper limit
            rate: 25.0 # 25%
          }
        ]
      }
    )

    puts "✅ Tax configuration created!"
  end
else
  puts "TaxConfig model not defined yet. Skipping tax configuration creation."
end
