# Create sample leave balances if the model exists
if defined?(LeaveBalance) && defined?(LeaveType)
  puts "Creating leave balances..."
  Employee.all.each do |employee|
    LeaveType.all.each do |leave_type|
      # Skip creating unpaid leave balances
      next if leave_type.code == "UL"

      LeaveBalance.find_or_create_by!(employee_id: employee.id, leave_type_id: leave_type.id) do |lb|
        lb.year = Date.today.year
        lb.initial_balance = leave_type.days_per_year
        lb.current_balance = leave_type.days_per_year - rand(0..5)
        lb.used = leave_type.days_per_year - lb.current_balance
      end
    end
  end
else
  puts "LeaveBalance or LeaveType model not defined yet. Skipping leave balance creation."
end
