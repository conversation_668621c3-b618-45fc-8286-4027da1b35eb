puts "Creating default attendance exemptions..."

# Default holidays for Jordan
default_exemptions = [
  {
    name: "New Year's Day",
    start_date: Date.new(Date.current.year, 1, 1),
    end_date: Date.new(Date.current.year, 1, 1),
    exemption_type: :holiday,
    description: "New Year's Day holiday"
  },
  {
    name: "Independence Day",
    start_date: Date.new(Date.current.year, 5, 25),
    end_date: Date.new(Date.current.year, 5, 25),
    exemption_type: :holiday,
    description: "Jordan Independence Day"
  },
  {
    name: "Labour Day",
    start_date: Date.new(Date.current.year, 5, 1),
    end_date: Date.new(Date.current.year, 5, 1),
    exemption_type: :holiday,
    description: "International Labour Day"
  },
  {
    name: "Christmas Day",
    start_date: Date.new(Date.current.year, 12, 25),
    end_date: Date.new(Date.current.year, 12, 25),
    exemption_type: :holiday,
    description: "Christmas Day holiday"
  }
]

default_exemptions.each do |exemption_data|
  exemption = Attendance::Exemption.find_or_initialize_by(
    name: exemption_data[:name],
    start_date: exemption_data[:start_date]
  )

  exemption.assign_attributes(exemption_data)

  if exemption.save
    puts "  ✓ Created/Updated exemption: #{exemption.name} (#{exemption.start_date})"
  else
    puts "  ✗ Failed to create exemption: #{exemption.name} - #{exemption.errors.full_messages.join(', ')}"
  end
end

puts "Attendance exemptions setup complete!"
puts "Created #{Attendance::Exemption.count} total exemptions"
