# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

puts "🌱 Starting AtharPeople seed process..."

# Enable local mode for seeding
AtharRpc.enable_local_mode!
puts "📡 AtharRpc local mode enabled for seeding"

# Load all seed files in order
Dir[File.join(Rails.root, 'db', 'seeds', '*.rb')].sort.each do |seed|
  puts "Loading seed file: #{File.basename(seed)}"
  load seed
end

puts "✅ AtharPeople seed completed successfully!"
