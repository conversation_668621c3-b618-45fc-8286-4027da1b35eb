# AUGMENT!! DO NOT REMOVE THE COMMENTS!!!!!

name: athar-people

services:
  app:
    hostname: people-app
#    image: docker.io/athar/people:latest
    build:
      context: ./
      dockerfile: ./development.dockerfile
      args:
        BUNDLE_GEM__FURY__IO: ${BUNDLE_GEM__FURY__IO}
    #command: tail -f /dev/null
#    command: debug
#    command: dev
    env_file:
      - docker-compose.env
#    environment:
#      RAILS_ENV: production
    ports:
      - "1235:1234"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.people.rule=Host(`people.athar.test`)"
      - "traefik.http.routers.people.entrypoints=web,websecure"
#      - "traefik.http.routers.people.service=people"
      - "traefik.http.services.people.loadbalancer.server.port=3002"
      - "traefik.http.routers.people.tls=false"

      # Redirect middleware for attendance devices API
#      - "traefik.http.middlewares.attendance-devices-redirect.redirectregex.regex=^https?://[^/]+/api/attendance/devices(.*)"
#      - "traefik.http.middlewares.attendance-devices-redirect.redirectregex.replacement=http://mock.apidog.com/m1/956658-940469-default/api/attendance/devices$$1"
#      - "traefik.http.middlewares.attendance-devices-redirect.redirectregex.permanent=true"
#
#      # Router for attendance devices redirect
#      - "traefik.http.routers.attendance-devices.rule=Host(`people.athar.test`) && PathPrefix(`/api/attendance/devices`)"
#      - "traefik.http.routers.attendance-devices.entrypoints=web,websecure"
#      - "traefik.http.routers.attendance-devices.middlewares=attendance-devices-redirect"
#      - "traefik.http.routers.attendance-devices.priority=100"
#      - "traefik.http.routers.attendance-devices.tls=false"

      # Health Check Configuration
#      - "traefik.http.services.people.loadbalancer.healthcheck.path=/up"
#      - "traefik.http.services.people.loadbalancer.healthcheck.interval=10s"
#      - "traefik.http.services.people.loadbalancer.healthcheck.timeout=3s"
    networks:
      - athar-network
    depends_on:
      - people-db
      - redis
    volumes:
      - ./config/master.key:/rails/config/master.key
      - ./:/rails
      - /Users/<USER>/workspace/athar/athar-ems/gems/auth-gem:/Users/<USER>/workspace/athar/athar-ems/gems/auth-gem
      - /Users/<USER>/workspace/athar/athar-ems/gems/rpc-gem:/Users/<USER>/workspace/athar/athar-ems/gems/rpc-gem
      - /Users/<USER>/workspace/athar/athar-ems/gems/commons-gem:/Users/<USER>/workspace/athar/athar-ems/gems/commons-gem
#      - /Users/<USER>/workspace/rbzk:/Users/<USER>/workspace/rbzk
#      - /Users/<USER>/workspace/apipie-rails:/Users/<USER>/workspace/apipie-rails
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  sidekiq:
    hostname: people-sidekiq
    build:
      context: ./
      dockerfile: ./development.dockerfile
      args:
        BUNDLE_GEM__FURY__IO: ${BUNDLE_GEM__FURY__IO}
    command: bundle exec sidekiq -C config/sidekiq.yml
    env_file:
      - docker-compose.env
    networks:
      - athar-network
    depends_on:
      - people-db
      - redis
    volumes:
      - ./config/master.key:/rails/config/master.key
      - ./:/rails
      - /Users/<USER>/workspace/athar/athar-ems/gems/auth-gem:/Users/<USER>/workspace/athar/athar-ems/gems/auth-gem
      - /Users/<USER>/workspace/athar/athar-ems/gems/rpc-gem:/Users/<USER>/workspace/athar/athar-ems/gems/rpc-gem
      - /Users/<USER>/workspace/athar/athar-ems/gems/commons-gem:/Users/<USER>/workspace/athar/athar-ems/gems/commons-gem
#      - /Users/<USER>/workspace/rbzk:/Users/<USER>/workspace/rbzk
    labels:
      - "traefik.enable=false"
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.25'

  people-db:
    image: postgres:17
    container_name: people-db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: people_athar_db
    networks:
      - athar-network
    ports:
      - "5434:5432"
    labels:
      - "traefik.enable=false"
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 128M
          cpus: '0.25'

  redis:
    image: redis:7-alpine
    container_name: people-redis
    networks:
      - athar-network
    ports:
      - "6379:6379"
    labels:
      - "traefik.enable=false"
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 64M
          cpus: '0.1'

  chrome-headless:
    image: browserless/chrome:latest
    container_name: people-chrome
    networks:
      - athar-network
    ports:
      - "9222:3000"
    environment:
      - CONCURRENT=10
      - TOKEN=
      - MAX_CONCURRENT_SESSIONS=10
      - PREBOOT_CHROME=true
      - KEEP_ALIVE=true
      - CHROME_REFRESH_TIME=2000
    labels:
      - "traefik.enable=false"
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.25'

networks:
  athar-network:
    external: true
    name: athar-backend_athar-network
