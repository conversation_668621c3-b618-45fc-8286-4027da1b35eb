# frozen_string_literal: true

module TimesheetReportHelper
  # Render the complete timesheet content
  def render_timesheet_content(data)
    <<~HTML
      #{render_timesheet_header(data)}
      #{render_employee_info(data)}
      #{render_attendance_table(data)}
      #{render_summary_section(data)}
      #{render_signatures_section}
    HTML
  end

  # Render timesheet header
  def render_timesheet_header(data)
    <<~HTML
      <div class="timesheet-header">
        <h1>Time sheet</h1>
        <div class="period-info">#{data[:period][:month_year]}</div>
      </div>
    HTML
  end

  # Render employee information section
  def render_employee_info(data)
    employee = data[:employee]
    
    <<~HTML
      <div class="employee-info">
        <div class="info-row">
          <span class="label">Name:</span>
          <span class="value">#{employee[:name]}</span>
        </div>
        <div class="info-row">
          <span class="label">Position:</span>
          <span class="value">#{employee[:position]}</span>
        </div>
        <div class="info-row">
          <span class="label">ID:</span>
          <span class="value">#{employee[:id]}</span>
        </div>
      </div>
    HTML
  end

  # Render the main attendance table
  def render_attendance_table(data)
    <<~HTML
      <table class="attendance-table">
        <thead>
          <tr>
            <th>No.</th>
            <th>Day</th>
            <th>Date</th>
            <th>Arrival time</th>
            <th>Leave time</th>
            <th>Signature</th>
          </tr>
        </thead>
        <tbody>
          #{render_attendance_rows(data[:days])}
        </tbody>
      </table>
    HTML
  end

  # Render attendance table rows
  def render_attendance_rows(days)
    days.map do |day|
      row_class = get_row_class(day)
      
      <<~HTML
        <tr class="#{row_class}">
          <td class="day-number">#{day[:day_number]}</td>
          <td class="day-name">#{day[:day_name]}</td>
          <td class="date">#{day[:formatted_date]}</td>
          <td class="arrival-time">#{day[:arrival_time] || ''}</td>
          <td class="departure-time">#{day[:departure_time] || ''}</td>
          <td class="signature"></td>
        </tr>
      HTML
    end.join
  end

  # Get CSS class for table row based on day status
  def get_row_class(day)
    classes = ['attendance-row']
    
    case day[:status]
    when 'weekend'
      classes << 'weekend-row'
    when 'leave'
      classes << 'leave-row'
    when 'absent'
      classes << 'absent-row'
    when 'present'
      classes << 'present-row'
    end
    
    classes.join(' ')
  end

  # Render summary section
  def render_summary_section(data)
    summary = data[:summary]
    
    <<~HTML
      <div class="summary-section">
        <div class="summary-row">
          <span class="summary-label">N. of actual working days:</span>
          <span class="summary-value">#{summary[:working_days]}</span>
        </div>
        <div class="summary-row">
          <span class="summary-label">N. of Leave or vacation:</span>
          <span class="summary-value">#{summary[:leave_days]}</span>
        </div>
      </div>
    HTML
  end

  # Render signatures section
  def render_signatures_section
    <<~HTML
      <div class="signatures-section">
        <div class="signature-label">Supervisor signatures</div>
        <div class="signature-lines">
          <div class="signature-line"></div>
          <div class="signature-line"></div>
        </div>
      </div>
    HTML
  end

  # Render CSS styles for the timesheet
  def render_timesheet_styles
    <<~CSS
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: Arial, sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #333;
      }

      .timesheet-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }

      .timesheet-header {
        text-align: center;
        margin-bottom: 30px;
      }

      .timesheet-header h1 {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .period-info {
        font-size: 14px;
        color: #666;
      }

      .employee-info {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f9f9f9;
        border: 1px solid #ddd;
      }

      .info-row {
        margin-bottom: 8px;
        display: flex;
      }

      .info-row .label {
        font-weight: bold;
        width: 80px;
        flex-shrink: 0;
      }

      .info-row .value {
        flex: 1;
      }

      .attendance-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
      }

      .attendance-table th,
      .attendance-table td {
        border: 1px solid #333;
        padding: 8px;
        text-align: center;
        vertical-align: middle;
      }

      .attendance-table th {
        background-color: #f0f0f0;
        font-weight: bold;
        font-size: 11px;
      }

      .attendance-table td {
        height: 25px;
        font-size: 10px;
      }

      .day-number {
        width: 8%;
      }

      .day-name {
        width: 12%;
      }

      .date {
        width: 20%;
      }

      .arrival-time,
      .departure-time {
        width: 20%;
      }

      .signature {
        width: 20%;
      }

      /* Row highlighting */
      .weekend-row {
        background-color: #fff2cc !important;
      }

      .leave-row {
        background-color: #ffe6e6 !important;
      }

      .absent-row {
        background-color: #f0f0f0 !important;
      }

      .present-row {
        background-color: #ffffff;
      }

      .summary-section {
        margin: 20px 0;
        padding: 15px;
        border: 1px solid #ddd;
        background-color: #f9f9f9;
      }

      .summary-row {
        margin-bottom: 8px;
        display: flex;
        justify-content: space-between;
      }

      .summary-label {
        font-weight: bold;
      }

      .summary-value {
        font-weight: bold;
        color: #0066cc;
      }

      .signatures-section {
        margin-top: 30px;
        padding: 20px;
        border: 1px solid #ddd;
      }

      .signature-label {
        font-weight: bold;
        margin-bottom: 20px;
      }

      .signature-lines {
        display: flex;
        justify-content: space-around;
      }

      .signature-line {
        width: 200px;
        height: 1px;
        border-bottom: 1px solid #333;
        margin-top: 40px;
      }

      /* Print styles */
      @media print {
        .timesheet-container {
          max-width: none;
          margin: 0;
          padding: 10px;
        }

        .attendance-table {
          font-size: 9px;
        }

        .attendance-table th,
        .attendance-table td {
          padding: 4px;
        }

        body {
          font-size: 11px;
        }
      }

      /* Page break handling */
      @page {
        size: A4;
        margin: 0.5in;
      }

      .attendance-table {
        page-break-inside: avoid;
      }

      .signatures-section {
        page-break-inside: avoid;
      }
    CSS
  end

  # Format time for display
  def format_display_time(time_string)
    return '' unless time_string.present?
    time_string
  end

  # Format date for display
  def format_display_date(date)
    return '' unless date.present?
    date.strftime('%-d-%b-%y')
  end

  # Get status label for display
  def get_status_label(status)
    case status
    when 'present'
      'Present'
    when 'absent'
      'Absent'
    when 'leave'
      'Leave'
    when 'weekend'
      'Weekend'
    else
      status.humanize
    end
  end
end
