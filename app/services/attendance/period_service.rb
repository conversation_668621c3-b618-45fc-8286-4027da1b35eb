module Attendance
  class PeriodService
    attr_reader :employee, :date, :settings, :incomplete_day

    def initialize(employee, date, incomplete_day = false)
      @employee = employee
      @date = date
      @settings = load_settings
      @incomplete_day = incomplete_day
    end

    def calculate_periods
      begin
        # 1. Get all events for the day
        events = ::Attendance::Event.daily_events(employee, date).order(timestamp: :asc)
        Rails.logger.info("Found #{events.size} events for employee #{employee.id} on #{date}")

        # 2. Identify and flag any new potential duplicates
        Attendance::DuplicateDetectionService.identify_duplicates(events, settings[:duplicate_threshold_seconds])

        # 3. Refresh the list of valid events (non-duplicates)
        events = events.where(potential_duplicate: false).order(timestamp: :asc)
        Rails.logger.info("After duplicate detection: #{events.size} valid events")

        # 4. Delete existing periods for this employee and date
        existing_periods = ::Attendance::Period.where(employee: employee, date: date)
        period_count = existing_periods.count
        existing_periods.destroy_all
        Rails.logger.info("Deleted #{period_count} existing periods")

        # 5. Check for approved leave first (V2: Skip auto-leave creation)
        if employee_on_approved_leave?
          Rails.logger.info("V2 System: Employee has approved leave, skipping auto-leave creation")
          return ::Attendance::Period.where(employee: employee, date: date)
        end

        # 6. Handle missing events (first check-in, last check-out)
        events = handle_missing_events(events)
        Rails.logger.info("After handling missing events: #{events.size} events")

        # 7. Calculate work and break periods
        calculate_work_and_break_periods(events)

        # Note: Late and early departure periods are created by handle_missing_events() method above
        # when there are missing check-ins or check-outs. These periods are still used by:
        # - Salary calculations (calculate_late_arrivals_deduction, calculate_early_departures_deduction)
        # - Statistics and reporting (late arrivals calculator, etc.)

        # Return all periods for this employee and date
        periods = ::Attendance::Period.where(employee: employee, date: date).order(start_timestamp: :asc)
        Rails.logger.info("Created #{periods.size} periods")
        periods
      rescue => e
        # Log the error
        Rails.logger.error("Error calculating periods: #{e.class.name}: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))
        raise e
      end
    end

    private

    def load_settings
      # Get settings with error handling
      begin
        # Get time strings from settings
        work_start_time = Setting.get('attendance', 'work_start_time', '09:00')
        work_end_time = Setting.get('attendance', 'work_end_time', '17:00')

        # Parse times and create timestamps in the correct timezone
        start_time_obj = Time.zone.parse(work_start_time)
        end_time_obj = Time.zone.parse(work_end_time)

        # Create timestamps for the specific date in Asia/Amman timezone
        work_start_timestamp = date.in_time_zone.change(hour: start_time_obj.hour, min: start_time_obj.min).to_i
        work_end_timestamp = date.in_time_zone.change(hour: end_time_obj.hour, min: end_time_obj.min).to_i
        duplicate_threshold_seconds = Setting.attendance_duplicate_threshold_seconds
        break_threshold_minutes = Setting.attendance_break_threshold_minutes

        # V2 Attendance system settings
        daily_expected_hours = Setting.attendance_daily_expected_hours
        daily_work_threshold = Setting.attendance_daily_work_threshold
        weekend_days = Setting.attendance_weekend_days

        # Log the settings for debugging
        Rails.logger.info("Loaded settings for attendance periods: " +
                         "work_start_time=#{work_start_time}, " +
                         "work_end_time=#{work_end_time}, " +
                         "work_start_timestamp=#{work_start_timestamp}, " +
                         "work_end_timestamp=#{work_end_timestamp}, " +
                         "duplicate_threshold_seconds=#{duplicate_threshold_seconds}, " +
                         "daily_expected_hours=#{daily_expected_hours}, " +
                         "break_threshold_minutes=#{break_threshold_minutes}, " +
                         "daily_work_threshold=#{daily_work_threshold}, " +
                         "weekend_days=#{weekend_days}")

        # Return the settings hash
        {
          work_start_time: work_start_time,
          work_end_time: work_end_time,
          work_start_timestamp: work_start_timestamp,
          work_end_timestamp: work_end_timestamp,
          duplicate_threshold_seconds: duplicate_threshold_seconds,
          daily_expected_hours: daily_expected_hours,
          break_threshold_minutes: break_threshold_minutes,
          daily_work_threshold: daily_work_threshold,
          weekend_days: weekend_days
        }
      rescue => e
        # Log the error
        Rails.logger.error("Error loading settings: #{e.class.name}: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))

        # Return default settings
        {
          work_start_time: '09:00',
          work_end_time: '17:00',
          work_start_timestamp: date.to_time.change(hour: 9).to_i,
          work_end_timestamp: date.to_time.change(hour: 17).to_i,
          duplicate_threshold_seconds: 60,
          daily_expected_hours: 8.0,
          break_threshold_minutes: 120,
          daily_work_threshold: 5.0,
          weekend_days: Setting.attendance_weekend_days
        }
      end
    end

    def handle_missing_events(events)
      return events if events.empty?

      enhanced_events = events.to_a

      # If first event is a check-out, add a predicted check-in at work start time
      if enhanced_events.first.check_out?
        predicted_check_in = ::Attendance::Event.new(
          employee: employee,
          timestamp: settings[:work_start_timestamp],
          event_type: :check_in,
          activity_type: :regular,
          potential_duplicate: false
        )

        # Add to the beginning of the array
        enhanced_events.unshift(predicted_check_in)

        # Create a late period if the first real event is after work start time
        if events.first.timestamp > settings[:work_start_timestamp]
          create_period(
            ::Attendance::Period::PERIOD_TYPES[:late],
            settings[:work_start_timestamp],
            events.first.timestamp,
            nil,
            true,
            "Predicted late period based on missing check-in"
          )
        end
      end

      # If last event is a check-in, add a predicted check-out at work end time
      if enhanced_events.last.check_in?
        predicted_check_out = ::Attendance::Event.new(
          employee: employee,
          timestamp: settings[:work_end_timestamp],
          event_type: :check_out,
          activity_type: :regular,
          potential_duplicate: false
        )

        # Add to the end of the array
        enhanced_events.push(predicted_check_out)

        # Create an early departure period only if it's not an incomplete day
        # and the last real event is before work end time
        if !incomplete_day && events.last.timestamp < settings[:work_end_timestamp]
          create_period(
            ::Attendance::Period::PERIOD_TYPES[:early_departure],
            events.last.timestamp,
            settings[:work_end_timestamp],
            nil,
            true,
            "Predicted early departure period based on missing check-out"
          )
        end
      end

      enhanced_events
    end

    def calculate_work_and_break_periods(events)
      return if events.size < 2

      # Pair events to create periods
      events.each_cons(2) do |event1, event2|
        if event1.check_in? && event2.check_out?
          # Calculate work period within working hours
          work_start = [ event1.timestamp, settings[:work_start_timestamp] ].max
          work_end = [ event2.timestamp, settings[:work_end_timestamp] ].min

          # Only create work period if there's actual overlap with working hours
          if work_start < work_end
            create_period(
              ::Attendance::Period::PERIOD_TYPES[:work],
              work_start,
              work_end,
              event1.activity_type,
              event1.id.nil? || event2.id.nil?, # is_predicted if either event is predicted
              nil
            )
          end
        elsif event1.check_out? && event2.check_in?
          # Break period
          create_period(
            ::Attendance::Period::PERIOD_TYPES[:break],
            event1.timestamp,
            event2.timestamp,
            event1.activity_type,
            event1.id.nil? || event2.id.nil?, # is_predicted if either event is predicted
            nil
          )
        end
      end
    end



    def create_period(type, start_timestamp, end_timestamp, activity_type = nil, is_predicted = false, notes = nil)
      duration_minutes = ((end_timestamp - start_timestamp) / 60).round

      ::Attendance::Period.create!(
        employee: employee,
        date: date,
        period_type: type,
        start_timestamp: start_timestamp,
        end_timestamp: end_timestamp,
        duration_minutes: duration_minutes,
        activity_type: activity_type,
        is_predicted: is_predicted,
        notes: notes,
        auto_generated: false # Default to manual for existing periods
      )
    end

    # NEW: Leave detection methods
    def employee_on_approved_leave?
      # Check if employee has approved leave for this date
      # This checks the existing Leave model (manual leaves)
      employee.leaves.where(
        'start_date <= ? AND end_date >= ? AND status = ?',
        date, date, Leave.statuses[:approved]
      ).exists?
    end

    def weekend_day?
      settings[:weekend_days].include?(date.wday)
    end

    def calculate_daily_work_hours
      work_periods = ::Attendance::Period.where(
        employee: employee,
        date: date,
        period_type: ::Attendance::Period::PERIOD_TYPES[:work]
      )

      total_minutes = work_periods.sum(:duration_minutes)
      total_minutes / 60.0
    end
  end
end
