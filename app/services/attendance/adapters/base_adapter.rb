module Attendance
  module Adapters
    class BaseAdapter
      attr_reader :device_name, :device_id, :options

      def initialize(options = {})
        @device_name = options[:device_name] || 'Unknown Device'
        @device_id = options[:device_id]
        @options = options
        validate_required_options!
      end

      # Abstract methods that must be implemented by subclasses
      def fetch_data(start_date = Date.yesterday, end_date = Date.today)
        raise NotImplementedError, "#{self.class} must implement #fetch_data"
      end

      def test_connection
        raise NotImplementedError, "#{self.class} must implement #test_connection"
      end

      def get_device_info
        raise NotImplementedError, "#{self.class} must implement #get_device_info"
      end

      # Optional methods with default implementations
      def supports_real_time?
        false
      end

      def supports_clear_data?
        false
      end

      def clear_attendance_logs
        raise NotImplementedError, "Device does not support clearing attendance logs"
      end

      def start_real_time_monitoring
        raise NotImplementedError, "<PERSON><PERSON> does not support real-time monitoring"
      end

      def stop_real_time_monitoring
        raise NotImplementedError, "Devi<PERSON> does not support real-time monitoring"
      end

      # Common utility methods
      def adapter_type
        self.class.name.demodulize.gsub('Adapter', '').underscore
      end

      def log_info(message)
        Rails.logger.info("[#{adapter_type.upcase}] #{device_name}: #{message}")
      end

      def log_error(message, exception = nil)
        Rails.logger.error("[#{adapter_type.upcase}] #{device_name}: #{message}")
        if exception
          Rails.logger.error("Exception: #{exception.class} - #{exception.message}")
          Rails.logger.error(exception.backtrace.join("\n"))
        end
      end

      def log_debug(message)
        Rails.logger.debug("[#{adapter_type.upcase}] #{device_name}: #{message}")
      end

      def log_warn(message)
        Rails.logger.warn("[#{adapter_type.upcase}] #{device_name}: #{message}")
      end

      # Data transformation helpers
      def transform_attendance_record(raw_record)
        {
          employee_code: extract_employee_code(raw_record),
          timestamp: extract_timestamp(raw_record),
          status: extract_status(raw_record),
          location: device_name,
          source_device_id: device_id,
          raw_data: raw_record
        }
      end

      def standardize_timestamp(timestamp)
        case timestamp
        when String
          Time.parse(timestamp)
        when Integer
          Time.at(timestamp)
        when Time, DateTime
          timestamp.to_time
        else
          raise ArgumentError, "Invalid timestamp format: #{timestamp.class}"
        end
      rescue => e
        log_error("Failed to parse timestamp: #{timestamp}", e)
        nil
      end

      # Validation helpers
      def validate_date_range(start_date, end_date)
        start_date = Date.parse(start_date.to_s) unless start_date.is_a?(Date)
        end_date = Date.parse(end_date.to_s) unless end_date.is_a?(Date)

        if start_date > end_date
          raise ArgumentError, "Start date cannot be after end date"
        end

        if start_date > Date.current
          raise ArgumentError, "Start date cannot be in the future"
        end

        [ start_date, end_date ]
      end

      def validate_employee_code(code)
        return nil if code.blank?

        code = code.to_s.strip
        return nil if code.empty?

        code
      end

      # Error handling helpers
      def handle_connection_error(error)
        case error
        when Errno::ECONNREFUSED
          log_error("Connection refused - device may be offline or unreachable")
        when Errno::ETIMEDOUT, Timeout::Error
          log_error("Connection timeout - device may be slow or overloaded")
        when Errno::EHOSTUNREACH
          log_error("Host unreachable - check network connectivity")
        when SocketError
          log_error("Socket error - check IP address and port")
        else
          log_error("Unexpected connection error: #{error.message}", error)
        end
        false
      end

      def with_error_handling
        yield
      rescue => e
        handle_connection_error(e)
        raise e
      end

      # Performance monitoring
      def measure_performance(operation_name)
        start_time = Time.current
        result = yield
        duration = Time.current - start_time

        log_debug("#{operation_name} completed in #{duration.round(3)}s")
        result
      end

      # User Management Interface (optional for adapters)

      # Get all users from the device as raw hashes
      def get_users
        log_warn("User management not supported by #{self.class}")
        []
      end

      # Get all users from the device as a DeviceUserCollection
      def get_device_users
        return ::Attendance::DeviceUserCollection.new unless supports_user_management?

        device_data = build_device_data
        user_hashes = get_users

        device_users = user_hashes.map do |hash|
          ::Attendance::DeviceUser.from_device_hash(hash, device_data)
        end

        # Use built-in bulk loading: new(owner_id, owner_type, items_array)
        ::Attendance::DeviceUserCollection.new(device_id, 'device', device_users)
      end

      # Create a new user on the device
      def create_user(user_data)
        log_warn("User creation not supported by #{self.class}")
        false
      end

      # Update an existing user on the device
      def update_user(user_id, user_data)
        log_warn("User update not supported by #{self.class}")
        false
      end

      # Delete a user from the device
      def delete_user(user_id)
        log_warn("User deletion not supported by #{self.class}")
        false
      end

      # Sync an employee to the device
      def sync_user(employee)
        log_warn("User sync not supported by #{self.class}")
        false
      end

      # Check if adapter supports user management
      def supports_user_management?
        false
      end

      # Command Management Interface

      # Check if adapter supports commands
      def supports_commands?
        false
      end

      # Get list of available commands for this adapter
      def available_commands
        []
      end



      protected

      # Build device data for DeviceUser creation
      def build_device_data
        {
          id: device_id,
          name: device_name,
          adapter_type: adapter_type,
          status: 'active', # Default status, can be overridden by subclasses
          location: device_name
        }
      end

      # Abstract methods for data extraction (to be implemented by subclasses)
      def extract_employee_code(raw_record)
        raise NotImplementedError, "#{self.class} must implement #extract_employee_code"
      end

      def extract_timestamp(raw_record)
        raise NotImplementedError, "#{self.class} must implement #extract_timestamp"
      end

      def extract_status(raw_record)
        raise NotImplementedError, "#{self.class} must implement #extract_status"
      end

      def validate_required_options!
        # Override in subclasses to validate specific required options
      end

      # Common status mapping
      def map_status_to_standard(device_status)
        case device_status.to_s.downcase
        when '0', 'in', 'check_in', 'checkin'
          'check_in'
        when '1', 'out', 'check_out', 'checkout'
          'check_out'
        when '2', 'break_out'
          'break_out'
        when '3', 'break_in'
          'break_in'
        when '4', 'overtime_in'
          'overtime_in'
        when '5', 'overtime_out'
          'overtime_out'
        else
          'undetermined'
        end
      end
    end
  end
end
