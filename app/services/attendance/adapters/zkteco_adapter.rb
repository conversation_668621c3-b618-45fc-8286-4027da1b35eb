require 'rbzk'

module Attendance
  module Adapters
    class ZktecoAdapter < BaseAdapter
      include Concerns::CommandDiscovery
      attr_reader :device_ip, :device_port, :password, :timeout

      def initialize(options = {})
        # Set instance variables first before calling validation
        @device_name = options[:device_name] || 'Unknown Device'
        @device_id = options[:device_id]
        @options = options

        @device_ip = options[:device_ip] || ENV['ZKTECO_DEVICE_IP']
        @device_port = options[:device_port] || ENV['ZKTECO_DEVICE_PORT'] || 4370
        @password = options[:password] || ENV['ZKTECO_DEVICE_PASSWORD'] || 0
        @timeout = options[:timeout] || 10

        # Now validate after everything is set up
        validate_required_options!
      end

      # Fetch attendance data from ZKTeco device
      def fetch_data(start_date = Date.yesterday, end_date = Date.today)
        start_date, end_date = validate_date_range(start_date, end_date)
        log_info("Fetching attendance data for #{start_date} to #{end_date}")

        measure_performance("fetch_data") do
          records = []

          with_connection do |zk_client|
            # Convert dates to timestamps for faster comparison
            start_timestamp = start_date.beginning_of_day.to_i
            end_timestamp = end_date.end_of_day.to_i

            # Get all attendance records
            attendance_logs = zk_client.get_attendance_logs
            log_debug("Retrieved #{attendance_logs.count} total records from device")

            # Optimized filtering using timestamps instead of date objects
            attendance_logs.each do |log|
              log_timestamp = log.timestamp.to_i
              next if log_timestamp < start_timestamp || log_timestamp > end_timestamp

              # Transform ZK attendance log to our standard format
              transformed_record = transform_attendance_record(log)
              records << transformed_record if transformed_record
            end
          end

          log_info("Retrieved #{records.count} attendance records")
          records
        end
      rescue => e
        log_error("Error fetching attendance data", e)
        []
      end

      # Test connection to the device
      def test_connection
        log_info("Testing connection at #{device_ip}:#{device_port}")

        measure_performance("test_connection") do
          with_connection do |zk_client|
            # Try to get basic device info to verify connection
            firmware = zk_client.get_firmware_version
            log_info("Successfully connected. Firmware: #{firmware}")
            true
          end
        end
      rescue => e
        handle_connection_error(e)
      end

      # Capability methods
      def supports_real_time?
        true
      end

      def supports_clear_data?
        true
      end

      # Get device information for diagnostics
      def get_device_info
        with_connection do |zk_client|
          # Read device sizes to get user and record counts
          zk_client.read_sizes

          {
            firmware_version: zk_client.get_firmware_version,
            platform: zk_client.get_platform,
            device_name: zk_client.get_device_name,
            serial_number: zk_client.get_serialnumber,
            users_count: zk_client.instance_variable_get(:@users),
            records_count: zk_client.instance_variable_get(:@records)
          }
        end
      rescue => e
        Rails.logger.error("Error getting device info from #{device_name}: #{e.message}")
        {}
      end

      # Clear attendance records from a device
      def clear_attendance_logs
        with_connection do |zk_client|
          # Note: The rbzk gem doesn't have a clear_attendance method
          # This would need to be implemented if clearing is required
          Rails.logger.warn("Clear attendance logs not implemented for rbzk gem")
          false
        end
      rescue => e
        Rails.logger.error("Error clearing attendance logs from #{device_name}: #{e.message}")
        false
      end

      # User Management Methods

      # Get all users from the device
      def get_users
        log_info("Retrieving users from device")

        # TODO: Remove this mock data when real ZKTeco integration is ready
        if Rails.env.development? || Rails.env.test?
          return mock_device_users
        end

        with_connection do |zk_client|
          users = zk_client.get_users
          log_info("Retrieved #{users.count} users from device")

          # Transform to standard format
          users.map do |user|
            {
              user_id: user.uid.to_s,
              name: user.name,
              privilege: user.privilege,
              password: user.password,
              group_id: user.group_id,
              card_number: user.card
            }
          end
        end
      rescue => e
        log_error("Error retrieving users from device", e)
        []
      end

      # Create a new user on the device
      def create_user(user_data)
        log_info("Creating user on device: #{user_data[:name]}")

        with_connection do |zk_client|
          # Set the user on the device
          result = zk_client.set_user(
            uid: user_data[:user_id],
            name: user_data[:name] || "",
            privilege: user_data[:privilege] || 0,
            password: user_data[:password] || "",
            group_id: user_data[:group_id] || "",
            user_id: "",
            card: user_data[:card_number]
          )

          log_info("User created successfully: #{user_data[:name]} (#{user_data[:user_id]})")
          true
        end
      rescue => e
        log_error("Error creating user on device", e)
        false
      end

      # Update an existing user on the device
      def update_user(user_id, user_data)
        log_info("Updating user #{user_id} on device")

        with_connection do |zk_client|
          # The rbzk gem doesn't have a direct update_user method
          # This would need to be implemented using the low-level commands
          log_warn("User update not yet implemented for rbzk gem")
          false
        end
      rescue => e
        log_error("Error updating user on device", e)
        false
      end

      # Delete a user from the device
      def delete_user(user_id)
        log_info("Deleting user #{user_id} from device")

        with_connection do |zk_client|
          # Delete the user from the device
          result = zk_client.delete_user(uid: user_id)

          log_info("User deleted successfully: #{user_id}")
          true
        end
      rescue => e
        log_error("Error deleting user from device", e)
        false
      end

      # Sync an employee to the device
      def sync_user(employee, device_user_id = nil)
        log_info("Syncing employee #{employee.name} to device")

        # Use provided device_user_id or fallback to employee ID
        user_id = device_user_id || employee.id.to_s

        user_data = {
          user_id: user_id,
          name: employee.name,
          privilege: 0, # Regular user
          password: nil,
          group_id: "1",
          card_number: nil
        }

        create_user(user_data)
      rescue => e
        log_error("Error syncing employee to device", e)
        false
      end

      # Check if the adapter supports user management
      def supports_user_management?
        true
      end

      # Command Interface Implementation

      def supports_commands?
        true
      end

      # Commands are auto-discovered from Attendance::Adapters::Zkteco namespace

      # Execute a block with a connected ZK client
      # Made public so command classes can access it
      def with_connection
        zk_client = create_client
        zk_client.connect

        begin
          yield zk_client
        ensure
          zk_client.disconnect if zk_client.connected?
        end
      end

      protected

      # Override to provide richer device data
      def build_device_data
        {
          id: device_id,
          name: device_name,
          adapter_type: adapter_type,
          status: 'active',
          location: device_name,
          ip_address: device_ip,
          port: device_port
        }
      end

      # Mock data for testing (based on Postman collection examples)
      def mock_device_users
        [
          { user_id: "1", name: "Abd", privilege: 14, password: nil, group_id: "", card_number: 0 },
          { user_id: "2", name: "Anas", privilege: 14, password: nil, group_id: "", card_number: 0 },
          { user_id: "3", name: "Sondos,Abukhdair", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "4", name: "Ata", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "5", name: "Mays", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "6", name: "Rawan", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "7", name: "Jenan", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "8", name: "Farah", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "9", name: "Sabreen", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "10", name: "Saeed", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "11", name: "Gofran", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "12", name: "Dania", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "13", name: "Samia", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "14", name: "Reem", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "15", name: "Walaa", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "16", name: "Raneem", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "17", name: "Ola", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "18", name: "Marah", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "19", name: "SondosHA", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "20", name: "Mo", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "21", name: "Aws", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "22", name: "Samara", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "23", name: "Tasneem", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "24", name: "Esraa", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "25", name: "Saraj", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "26", name: "Alaanasser", privilege: 0, password: nil, group_id: "", card_number: 0 },
          { user_id: "28", name: "KhaledTest28", privilege: 0, password: nil, group_id: nil, card_number: 0 }
        ]
      end

      # Implement base class abstract methods
      def extract_employee_code(zk_log)
        validate_employee_code(zk_log.user_id)
      end

      def extract_timestamp(zk_log)
        standardize_timestamp(zk_log.timestamp)
      end

      def extract_status(zk_log)
        map_status_to_standard(zk_log.status)
      end

      def validate_required_options!
        if device_ip.blank?
          raise ArgumentError, "device_ip is required for ZKTeco adapter. Current options: #{options.inspect}"
        end
        port_int = device_port.to_i
        unless port_int.between?(1, 65535)
          raise ArgumentError, "device_port must be a valid port number. Current port: #{device_port.inspect}"
        end
      end

      private

      # Create a new ZK client instance
      def create_client
        ::RBZK::ZK.new(
          device_ip,
          port: device_port,
          timeout: timeout,
          password: password
        )
      end

      # Override base transform method to handle ZK-specific data
      def transform_attendance_record(zk_log)
        employee_code = extract_employee_code(zk_log)
        timestamp = extract_timestamp(zk_log)

        return nil if employee_code.blank? || timestamp.blank?

        # Get punch type if available, otherwise default to 0
        punch_value = zk_log.respond_to?(:punch) ? zk_log.punch : 0

        {
          employee_code: employee_code,
          timestamp: timestamp,
          status: extract_status(zk_log),
          location: device_name,
          source_device_id: device_id,
          punch_type: punch_value,
          raw_data: {
            user_id: zk_log.user_id,
            timestamp: zk_log.timestamp,
            status: zk_log.status,
            punch: punch_value
          }
        }
      end
    end
  end
end
