require 'csv'

module Attendance
  module Adapters
    class FileImportAdapter < BaseAdapter
      attr_reader :file_path, :file_format

      def initialize(options = {})
        super(options)
        @file_path = options[:file_path]
        @file_format = options[:file_format] || 'csv'
      end

      # Parse attendance data from a file
      def fetch_data(start_date = Date.yesterday, end_date = Date.today)
        start_date, end_date = validate_date_range(start_date, end_date)
        log_info("Processing file: #{file_path} for #{start_date} to #{end_date}")

        return [] unless file_path && File.exist?(file_path)

        measure_performance("fetch_data") do
          records = []

          # Determine file type and parse accordingly
          case File.extname(file_path).downcase
          when '.csv'
            records = parse_csv_file(start_date, end_date)
          when '.xlsx', '.xls'
            records = parse_excel_file(start_date, end_date)
          else
            log_error("Unsupported file type: #{File.extname(file_path)}")
            return []
          end

          log_info("Processed #{records.count} records from file")
          records
        end
      rescue => e
        log_error("Error processing file", e)
        []
      end

      # Test "connection" by checking if file exists and is readable
      def test_connection
        log_info("Testing file access: #{file_path}")

        if file_path.blank?
          log_error("No file path configured")
          return false
        end

        unless File.exist?(file_path)
          log_error("File does not exist: #{file_path}")
          return false
        end

        unless File.readable?(file_path)
          log_error("File is not readable: #{file_path}")
          return false
        end

        log_info("File access test successful")
        true
      rescue => e
        log_error("File access test failed", e)
        false
      end

      # Get file information
      def get_device_info
        return {} unless file_path && File.exist?(file_path)

        file_stat = File.stat(file_path)
        {
          file_path: file_path,
          file_size: file_stat.size,
          file_modified: file_stat.mtime,
          file_format: File.extname(file_path),
          readable: File.readable?(file_path),
          estimated_records: estimate_record_count
        }
      rescue => e
        log_error("Error getting file info", e)
        {}
      end

      # Capability methods
      def supports_real_time?
        false # Files are static
      end

      def supports_user_management?
        false # Cannot manage users in a file
      end

      def supports_clear_data?
        true # Can delete/move the file
      end

      protected

      # Implement BaseAdapter interface methods
      def extract_employee_code(record_data)
        employee_code = record_data[:employee_code] ||
                       record_data[:employee_id] ||
                       record_data[:user_id] ||
                       record_data[:emp_id]
        validate_employee_code(employee_code)
      end

      def extract_timestamp(record_data)
        timestamp_str = record_data[:timestamp] ||
                       record_data[:date_time] ||
                       record_data[:punch_time] ||
                       record_data[:time]
        standardize_timestamp(timestamp_str) if timestamp_str
      end

      def extract_status(record_data)
        status = record_data[:status] ||
                record_data[:event_type] ||
                record_data[:punch_type] ||
                record_data[:action]
        map_status_to_standard(status) if status
      end

      def validate_required_options!
        # File path will be validated when needed
      end

      private

      def parse_csv_file(start_date, end_date)
        records = []
        line_number = 0

        begin
          CSV.foreach(file_path, headers: true) do |row|
            line_number += 1

            # Convert row to hash with symbolized keys
            data = row.to_h.transform_keys { |k| k.to_s.downcase.gsub(/\s+/, '_').to_sym }

            # Transform using base adapter methods
            transformed_record = transform_attendance_record(data)
            next unless transformed_record

            # Filter by date range
            record_date = transformed_record[:timestamp].to_date
            next if record_date < start_date || record_date > end_date

            records << transformed_record
          end
        rescue CSV::MalformedCSVError => e
          log_error("Malformed CSV at line #{line_number}: #{e.message}")
        rescue => e
          log_error("Error parsing CSV file at line #{line_number}", e)
        end

        records
      end

      # Override base transform method to handle file data
      def transform_attendance_record(record_data)
        employee_code = extract_employee_code(record_data)
        timestamp = extract_timestamp(record_data)

        return nil if employee_code.blank? || timestamp.blank?

        {
          employee_code: employee_code,
          timestamp: timestamp,
          status: extract_status(record_data) || 'undetermined',
          location: record_data[:location] || record_data[:terminal] || device_name,
          source_device_id: device_id,
          raw_data: record_data
        }
      end

      def parse_excel_file(start_date, end_date)
        # Placeholder for Excel parsing logic
        # Would require a gem like 'roo' or 'spreadsheet'
        log_warn("Excel parsing not implemented yet")
        []
      end

      def estimate_record_count
        return 0 unless file_path && File.exist?(file_path)

        case File.extname(file_path).downcase
        when '.csv'
          # Quick estimate by counting lines
          line_count = `wc -l < "#{file_path}"`.to_i
          [line_count - 1, 0].max # Subtract header line
        else
          0
        end
      rescue
        0
      end

      # Clear data by moving/archiving the file
      def clear_attendance_logs
        return false unless file_path && File.exist?(file_path)

        # Create archive directory
        archive_dir = File.join(File.dirname(file_path), 'archived')
        FileUtils.mkdir_p(archive_dir)

        # Move file to archive with timestamp
        timestamp = Time.current.strftime('%Y%m%d_%H%M%S')
        filename = File.basename(file_path, '.*')
        extension = File.extname(file_path)
        archived_name = "#{filename}_#{timestamp}#{extension}"
        archived_path = File.join(archive_dir, archived_name)

        FileUtils.mv(file_path, archived_path)
        log_info("File archived to: #{archived_path}")
        true
      rescue => e
        log_error("Failed to archive file", e)
        false
      end
    end
  end
end
