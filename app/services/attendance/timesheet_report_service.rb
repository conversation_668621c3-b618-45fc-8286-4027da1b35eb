# frozen_string_literal: true

module Attendance
  class TimesheetReportService
    attr_reader :employee, :year, :month, :date_range

    def initialize(employee, year, month)
      @employee = employee
      @year = year.to_i
      @month = month.to_i

      validate_parameters!

      @date_range = Date.new(@year, @month, 1)..Date.new(@year, @month, -1)
    end

    # Generate PDF timesheet report
    def generate_pdf
      html_content = generate_html
      raise TimesheetGenerationError, "HTML generation failed" if html_content.blank?

      pdf_content = convert_to_pdf(html_content)
      raise TimesheetGenerationError, "PDF conversion failed" if pdf_content.blank?

      pdf_content
    rescue => e
      Rails.logger.error "Timesheet PDF generation failed for employee #{employee.id}: #{e.message}"
      raise TimesheetGenerationError, "Failed to generate timesheet: #{e.message}"
    end

    # Generate HTML timesheet report
    def generate_html
      data = generate_data
      render_html_template(data)
    rescue => e
      Rails.logger.error "Timesheet HTML generation failed for employee #{employee.id}: #{e.message}"

      # Try fallback HTML generation
      begin
        generate_html_content(data)
      rescue => fallback_error
        Rails.logger.error "Fallback HTML generation also failed: #{fallback_error.message}"
        raise TimesheetGenerationError, "Failed to generate HTML timesheet: #{e.message}"
      end
    end

    # Generate structured data for the timesheet
    def generate_data
      {
        employee: employee_data,
        period: period_data,
        days: gather_monthly_attendance,
        summary: calculate_summary,
        generated_at: Time.current
      }
    end

    private

    # Validate input parameters
    def validate_parameters!
      errors = []

      errors << "Invalid year" unless (2020..2030).include?(year)
      errors << "Invalid month" unless (1..12).include?(month)
      errors << "Employee not found" unless employee.present?

      # Only check future date if year and month are valid
      if (2020..2030).include?(year) && (1..12).include?(month)
        begin
          errors << "Future date not allowed" if Date.new(year, month, 1) > Date.current
        rescue Date::Error
          errors << "Invalid date"
        end
      end

      raise ArgumentError, errors.join(', ') if errors.any?
    end

    # Gather attendance data for all days in the month
    def gather_monthly_attendance
      # Load all events and leaves for the month in single queries
      events_by_date = load_monthly_events
      leaves_by_date = load_monthly_leaves

      date_range.map do |date|
        calculate_daily_attendance(date, events_by_date[date] || [], leaves_by_date[date] || [])
      end
    end

    # Load all attendance events for the month
    def load_monthly_events
      start_timestamp = date_range.begin.beginning_of_day.to_i
      end_timestamp = date_range.end.end_of_day.to_i

      events = Attendance::Event
        .where(employee: employee)
        .where(timestamp: start_timestamp..end_timestamp)
        .order(:timestamp)

      # Group events by date for efficient processing
      events.group_by { |event| Time.zone.at(event.timestamp).to_date }
    end

    # Load all leaves for the month
    def load_monthly_leaves
      leaves = Leave
        .where(employee: employee)
        .where(status: [:approved, :pending])
        .where('start_date <= ? AND end_date >= ?', date_range.end, date_range.begin)

      # Group leaves by date range
      leaves_by_date = {}
      leaves.each do |leave|
        (leave.start_date..leave.end_date).each do |date|
          next unless date_range.include?(date)
          leaves_by_date[date] ||= []
          leaves_by_date[date] << leave
        end
      end
      
      leaves_by_date
    end

    # Calculate attendance data for a specific date
    def calculate_daily_attendance(date, events, leaves)
      {
        date: date,
        day_number: date.day,
        day_name: date.strftime('%a'),
        formatted_date: date.strftime('%-d-%b-%y'),
        arrival_time: get_arrival_time(events),
        departure_time: get_departure_time(events),
        is_weekend: is_weekend?(date),
        is_leave: leaves.any?,
        is_present: events.any?,
        status: calculate_day_status(date, events, leaves),
        events_count: events.count
      }
    end

    # Get first check-in time for the day
    def get_arrival_time(events)
      return nil if events.empty?

      # Find first check-in or first event if no explicit check-ins
      first_checkin = events.find(&:check_in?)
      first_event = first_checkin || events.first
      
      return nil unless first_event

      format_time(first_event.timestamp)
    end

    # Get last check-out time for the day
    def get_departure_time(events)
      return nil if events.empty?

      # Find last check-out or last event if no explicit check-outs
      last_checkout = events.reverse.find(&:check_out?)
      last_event = last_checkout || events.last
      
      return nil unless last_event

      format_time(last_event.timestamp)
    end

    # Check if date is a weekend
    def is_weekend?(date)
      weekend_days = Setting.attendance_weekend_days || [5, 6] # Default: Friday, Saturday
      weekend_days.include?(date.wday)
    end

    # Calculate the status for a day
    def calculate_day_status(date, events, leaves)
      return 'leave' if leaves.any?
      return 'weekend' if is_weekend?(date)
      return 'present' if events.any?
      'absent'
    end

    # Calculate summary statistics
    def calculate_summary
      days_data = gather_monthly_attendance
      
      {
        total_days: days_data.count,
        working_days: days_data.count { |day| day[:status] == 'present' },
        leave_days: days_data.count { |day| day[:status] == 'leave' },
        weekend_days: days_data.count { |day| day[:status] == 'weekend' },
        absent_days: days_data.count { |day| day[:status] == 'absent' }
      }
    end

    # Employee data for the report
    def employee_data
      {
        name: employee.name,
        position: employee.job_title,
        id: employee.employee_id,
        department: employee.department_name
      }
    end

    # Period data for the report
    def period_data
      {
        month_name: Date::MONTHNAMES[month],
        year: year,
        month_year: "#{Date::MONTHNAMES[month]} #{year}",
        month_number: month
      }
    end

    # Format timestamp to time string
    def format_time(timestamp)
      return nil unless timestamp

      Time.zone.at(timestamp).strftime('%H:%M')
    end

    # Convert HTML to PDF using Grover
    def convert_to_pdf(html_content)
      require 'grover'

      grover = Grover.new(html_content, {
        format: 'A4',
        margin: {
          top: '0.5in',
          bottom: '0.5in',
          left: '0.5in',
          right: '0.5in'
        },
        print_background: true,
        prefer_css_page_size: true,
        display_header_footer: false,
        timeout: 30_000,
        wait_until: 'networkidle0'
      })
      
      grover.to_pdf
    end

    # Render HTML template with data
    def render_html_template(data)
      # In test environment or if templates are not available, use fallback
      if Rails.env.test? || !template_exists?
        return generate_html_content(data)
      end

      # Create a controller instance for template rendering
      controller = Api::Attendance::ReportsController.new
      controller.request = ActionDispatch::Request.new({})
      controller.response = ActionDispatch::Response.new

      controller.instance_variable_set(:@data, data)

      controller.render_to_string(
        template: 'timesheet_reports/timesheet',
        layout: false
      )
    rescue => e
      Rails.logger.error "Template rendering failed: #{e.message}"

      # Fallback to direct HTML generation
      generate_html_content(data)
    end

    # Check if template exists
    def template_exists?
      File.exist?(Rails.root.join('app/views/timesheet_reports/timesheet.html.erb'))
    end

    # Fallback HTML generation method
    def generate_html_content(data)
      extend TimesheetReportHelper
      
      <<~HTML
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Timesheet - #{data[:employee][:name]}</title>
          <style>
            #{render_timesheet_styles}
          </style>
        </head>
        <body>
          <div class="timesheet-container">
            #{render_timesheet_content(data)}
          </div>
        </body>
        </html>
      HTML
    end
  end
end
