module Attendance
  class WorkingDaysCalculator
    attr_reader :year, :month

    def initialize(year, month)
      @year = year
      @month = month
    end

    # Calculate expected work hours for the month
    def monthly_expected_hours
      working_days_count * Setting.attendance_daily_expected_hours
    end

    # Calculate working days in the month (excluding weekends)
    def working_days_count
      weekend_days = Setting.attendance_weekend_days

      working_days = 0
      date_range.each do |date|
        working_days += 1 unless weekend_days.include?(date.wday)
      end

      working_days
    end

    # Get total days in the month
    def total_days_count
      date_range.count
    end

    # Get weekend days count
    def weekend_days_count
      total_days_count - working_days_count
    end

    # Class methods for convenience
    def self.monthly_expected_hours(year, month)
      new(year, month).monthly_expected_hours
    end

    def self.working_days_count(year, month)
      new(year, month).working_days_count
    end

    # Summary for reporting
    def summary
      {
        year: year,
        month: month,
        month_name: Date::MONTHNAMES[month],
        total_days: total_days_count,
        working_days: working_days_count,
        weekend_days: weekend_days_count,
        daily_expected_hours: Setting.attendance_daily_expected_hours,
        monthly_expected_hours: monthly_expected_hours
      }
    end

    private

    def date_range
      start_date = Date.new(year, month, 1)
      end_date = start_date.end_of_month
      start_date..end_date
    end
  end
end
