module Attendance
  class DaySummaryService
    attr_reader :employee, :date

    def initialize(employee, date = nil)
      @employee = employee
      @date = date || Date.today
    end

    def calculate_daily_records
      {
        arrival_time: arrival_time,
        departure_time: departure_time,
        minutes_spent_working: minutes_spent_working,
        work_schedule: work_schedule
      }
    end

    def get_periods
      employee.attendance_periods.for_date(date)
                  .order(start_timestamp: :asc)
    end

    private

    def first_check_in
      employee.attendance_events
              .for_date(date)
              .check_ins
              .order(timestamp: :asc)
              .first
    end

    def last_check_out
      employee.attendance_events
              .for_date(date)
              .check_outs
              .order(timestamp: :desc)
              .first
    end

    def arrival_time
      first_check_in&.timestamp_as_time&.strftime("%H:%M:%S")
    end

    def departure_time
      last_check_out&.timestamp_as_time&.strftime("%H:%M:%S")
    end

    def minutes_spent_working
      employee.attendance_periods.for_date(date)
              .where(period_type: Attendance::Period::PERIOD_TYPES[:work])
              .sum(:duration_minutes)
    end

    def work_schedule
      {
        start_time: Setting.attendance_work_start_time,
        end_time: Setting.attendance_work_end_time,
        expected_hours: Setting.attendance_daily_expected_hours
      }
    end
  end
end
