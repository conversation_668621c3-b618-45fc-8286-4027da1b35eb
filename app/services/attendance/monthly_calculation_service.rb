module Attendance
  class MonthlyCalculationService
    attr_reader :employee, :year, :month, :settings

    def initialize(employee, year, month)
      @employee = employee
      @year = year
      @month = month
      @settings = load_settings
    end

    # Main calculation method
    def calculate_monthly_record
      Rails.logger.info("Calculating monthly record for employee #{employee.id}, #{year}-#{month}")

      # Get or create monthly record
      record = Attendance::MonthlyRecord.find_or_initialize_by(
        employee: employee,
        year: year,
        month: month
      )

      # Calculate values dynamically using WorkingDaysCalculator
      expected_hours = Attendance::WorkingDaysCalculator.monthly_expected_hours(year, month)
      actual_hours = calculate_actual_work_hours
      deficit_hours = [ expected_hours - actual_hours, 0 ].max

      # Update record
      record.assign_attributes(
        expected_hours: expected_hours,
        actual_hours: actual_hours,
        deficit_hours: deficit_hours
      )

      record.save!

      Rails.logger.info("Monthly record calculated: expected=#{expected_hours}, actual=#{actual_hours}, deficit=#{deficit_hours}")
      record
    end

    # Calculate actual work hours for the month
    def calculate_actual_work_hours
      start_date = Date.new(year, month, 1)
      end_date = start_date.end_of_month

      # Get all work periods for the month
      work_periods = Attendance::Period.work_periods
                                      .where(employee: employee)
                                      .for_date_range(start_date, end_date)

      total_hours = work_periods.sum { |period| period.duration_hours }

      Rails.logger.info("Found #{work_periods.count} work periods totaling #{total_hours} hours")
      total_hours
    end

    # Calculate working days in the month (excluding weekends)
    def calculate_working_days
      month_start = Date.new(year, month, 1)
      month_end = month_start.end_of_month
      weekend_days = settings[:weekend_days]

      working_days = 0
      (month_start..month_end).each do |date|
        working_days += 1 unless weekend_days.include?(date.wday)
      end

      working_days
    end

    # Get summary with additional metrics
    def monthly_summary
      record = calculate_monthly_record
      working_days = calculate_working_days

      {
        monthly_record: record,
        employee_id: @employee.id,
        employee_name: @employee.name,
        year: @year,
        month: @month,
        month_name: Date::MONTHNAMES[@month],
        expected_hours: record.expected_hours,
        actual_hours: record.actual_hours,
        deficit_hours: record.deficit_hours,
        working_days: working_days,
        working_days_in_month: working_days,
        expected_hours_per_day: working_days > 0 ? (record.expected_hours / working_days).round(2) : 0,
        actual_hours_per_day: working_days > 0 ? (record.actual_hours / working_days).round(2) : 0,
        attendance_percentage: record.attendance_percentage,
        deficit_percentage: record.deficit_percentage,
        has_deficit: record.has_deficit?,
        auto_generated_leave_days: count_auto_generated_leave_days,
        manual_leave_days: count_manual_leave_days,
        auto_generated_leaves: count_auto_generated_leaves,
        manual_leaves: count_manual_leaves,
        total_leave_days: count_total_leave_days
      }
    end

    # Count auto-generated leave days in the month
    def count_auto_generated_leaves
      start_date = Date.new(year, month, 1)
      end_date = start_date.end_of_month

      Attendance::Period.auto_generated_leaves
                       .where(employee: employee)
                       .for_date_range(start_date, end_date)
                       .count
    end

    # Count manual leave days in the month
    def count_manual_leaves
      start_date = Date.new(year, month, 1)
      end_date = start_date.end_of_month

      Attendance::Period.manual_leaves
                       .where(employee: employee)
                       .for_date_range(start_date, end_date)
                       .count
    end

    # Count total leave days (both auto and manual)
    def count_total_leave_days
      start_date = Date.new(year, month, 1)
      end_date = start_date.end_of_month

      Attendance::Period.leave_periods
                       .where(employee: employee)
                       .for_date_range(start_date, end_date)
                       .count
    end

    # Calculate salary deduction based on deficit
    def calculate_salary_deduction(base_salary)
      record = calculate_monthly_record
      record.calculate_salary_deduction(base_salary)
    end

    # Class methods for bulk operations
    def self.calculate_for_employees(employees_or_ids, year, month)
      results = []

      employees_or_ids.each do |employee_or_id|
        employee = employee_or_id.is_a?(Employee) ? employee_or_id : Employee.find(employee_or_id)
        service = new(employee, year, month)
        results << service.monthly_summary
      end

      results
    end

    def self.calculate_for_department(department_id, year, month)
      employee_ids = Employee.where(department_id: department_id).pluck(:id)
      calculate_for_employees(employee_ids, year, month)
    end

    def self.calculate_for_all_employees(year, month)
      employee_ids = Employee.pluck(:id)
      calculate_for_employees(employee_ids, year, month)
    end

    # Recalculate for date range (useful for historical data)
    def self.recalculate_range(employee, start_date, end_date)
      results = []

      current_date = start_date.beginning_of_month
      end_date = end_date.beginning_of_month

      while current_date <= end_date
        service = new(employee, current_date.year, current_date.month)
        results << service.calculate_monthly_record
        current_date = current_date.next_month
      end

      results
    end

    private

    def count_auto_generated_leave_days
      Attendance::Period.where(
        employee: @employee,
        date: start_date..end_date,
        period_type: 'leave',
        auto_generated: true
      ).sum(:duration_minutes) / 60.0 / 8.0 # Convert minutes to days (8 hours = 1 day)
    end

    def count_manual_leave_days
      Attendance::Period.where(
        employee: @employee,
        date: start_date..end_date,
        period_type: 'leave',
        auto_generated: false
      ).sum(:duration_minutes) / 60.0 / 8.0 # Convert minutes to days (8 hours = 1 day)
    end

    def start_date
      @start_date ||= Date.new(@year, @month, 1)
    end

    def end_date
      @end_date ||= start_date.end_of_month
    end

    def load_settings
      {
        weekend_days: Setting.attendance_weekend_days
      }
    end
  end
end
