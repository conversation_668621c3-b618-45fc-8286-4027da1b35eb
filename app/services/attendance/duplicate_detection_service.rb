module Attendance
  class DuplicateDetectionService
    def self.identify_duplicates(events, threshold_seconds = nil)
      return if events.size <= 1

      # Use default threshold if not provided
      threshold_seconds ||= Setting.attendance_duplicate_threshold_seconds

      # Ensure threshold_seconds is an integer
      threshold_seconds = threshold_seconds.to_i

      # Group events by event_type
      events.group_by(&:event_type).each do |event_type, type_events|
        # Sort by timestamp
        sorted_events = type_events.sort_by(&:timestamp)

        # Check for events that are too close together
        sorted_events.each_with_index do |event, index|
          next if index == 0

          previous_event = sorted_events[index - 1]
          time_difference = event.timestamp - previous_event.timestamp

          if time_difference <= threshold_seconds
            # Flag the later event as a potential duplicate
            duplicate_note = "Flagged as potential duplicate (#{time_difference} seconds after event ##{previous_event.id}) on #{Time.current}"

            # Only add the note if it's not already present
            unless event.notes&.include?("Flagged as potential duplicate") && event.notes&.include?("event ##{previous_event.id}")
              updated_notes = event.notes.present? ? "#{event.notes}\n\n#{duplicate_note}" : duplicate_note

              # Skip period calculation callback to avoid infinite loop
              event.skip_period_calculation = true
              event.update(
                potential_duplicate: true,
                notes: updated_notes
              )
            else
              # Just update the potential_duplicate flag if note already exists
              unless event.potential_duplicate?
                event.skip_period_calculation = true
                event.update(potential_duplicate: true)
              end
            end
          end
        end
      end
    end

    # Utility method to clean up duplicate notes from events
    def self.cleanup_duplicate_notes(events = nil)
      events ||= ::Attendance::Event.where("notes LIKE ?", "%Flagged as potential duplicate%")

      events.each do |event|
        next unless event.notes.present?

        # Split notes by lines and remove duplicates
        lines = event.notes.split("\n")
        unique_lines = []
        seen_duplicate_notes = Set.new

        lines.each do |line|
          line = line.strip
          next if line.empty?

          # Check if this is a duplicate detection note
          if line.include?("Flagged as potential duplicate")
            # Extract the event ID from the note to identify unique duplicates
            event_match = line.match(/event #(\d+)/)
            if event_match
              event_id = event_match[1]
              # Only keep the first occurrence of each duplicate note for each event
              unless seen_duplicate_notes.include?(event_id)
                unique_lines << line
                seen_duplicate_notes.add(event_id)
              end
            else
              # If we can't parse the event ID, keep the line
              unique_lines << line
            end
          else
            # Keep non-duplicate notes as is
            unique_lines << line
          end
        end

        # Update the event with cleaned notes
        cleaned_notes = unique_lines.join("\n\n")
        if cleaned_notes != event.notes
          event.skip_period_calculation = true
          event.update(notes: cleaned_notes)
        end
      end
    end
  end
end
