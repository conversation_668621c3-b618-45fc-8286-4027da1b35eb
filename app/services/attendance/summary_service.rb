module Attendance
  class SummaryService
    attr_reader :periods

    def initialize(periods)
      @periods = periods
    end

    def calculate
      work_periods = periods.select { |p| p.period_type == Attendance::Period::PERIOD_TYPES[:work] }
      break_periods = periods.select { |p| p.period_type == Attendance::Period::PERIOD_TYPES[:break] }
      late_period = periods.find { |p| p.period_type == Attendance::Period::PERIOD_TYPES[:late] }
      early_departure = periods.find { |p| p.period_type == Attendance::Period::PERIOD_TYPES[:early_departure] }

      total_work_minutes = work_periods.sum(&:duration_minutes)
      total_break_minutes = break_periods.sum(&:duration_minutes)
      late_minutes = late_period&.duration_minutes || 0
      early_departure_minutes = early_departure&.duration_minutes || 0

      {
        total_work_minutes: total_work_minutes,
        total_break_minutes: total_break_minutes,
        late_minutes: late_minutes,
        early_departure_minutes: early_departure_minutes,
        work_periods_count: work_periods.size,
        break_periods_count: break_periods.size,
        is_late: late_minutes > 0,
        left_early: early_departure_minutes > 0,
        formatted_work_time: format_minutes(total_work_minutes),
        formatted_break_time: format_minutes(total_break_minutes),
        formatted_late_time: format_minutes(late_minutes),
        formatted_early_departure_time: format_minutes(early_departure_minutes)
      }
    end

    private

    def format_minutes(minutes)
      hours = minutes / 60
      remaining_minutes = minutes % 60

      if hours > 0
        "#{hours}h #{remaining_minutes}m"
      else
        "#{minutes}m"
      end
    end
  end
end
