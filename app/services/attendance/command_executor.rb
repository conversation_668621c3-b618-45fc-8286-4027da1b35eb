module Attendance
  class CommandExecutor
    def self.execute(device, command_name, parameters = {}, employee = nil)
      # Create execution record
      execution = CommandExecution.create!(
        device: device,
        command_name: command_name,
        parameters: parameters,
        status: :running,
        executed_by: employee,
        started_at: Time.current
      )

      begin
        # Execute command on adapter
        adapter = device.create_adapter

        # Check if adapter supports commands
        unless adapter.supports_commands?
          result = Attendance::CommandResult.failure("Device does not support commands")
          execution.update!(
            status: :failed,
            result: result.as_json,
            completed_at: Time.current
          )
          return result
        end

        # Execute command using adapter's command discovery
        result = adapter.execute_command(command_name, parameters)

        # Update execution record
        execution.update!(
          status: result.success ? :completed : :failed,
          result: result.as_json,
          completed_at: Time.current
        )

        result
      rescue => e
        result = Attendance::CommandResult.failure(e.message)
        execution.update!(
          status: :failed,
          result: result.as_json,
          completed_at: Time.current
        )

        result
      end
    end

    # Get available commands for a device
    def self.available_commands_for_device(device)
      adapter = device.create_adapter

      return [] unless adapter.supports_commands?

      adapter.available_commands.map do |command_name|
        command_class = adapter.get_command_class(command_name)
        {
          name: command_name,
          display_name: command_class&.command_name&.titleize || command_name.titleize,
          description: command_class&.description || 'Execute device command'
        }
      end
    end

    # Validate command parameters
    def self.validate_command(device, command_name, parameters = {})
      adapter = device.create_adapter

      unless adapter.supports_commands?
        return { valid: false, error: "Device does not support commands" }
      end

      command_class = adapter.get_command_class(command_name)
      unless command_class
        return { valid: false, error: "Command '#{command_name}' not supported by #{device.adapter_type} adapter" }
      end

      command = command_class.new(parameters)
      command.validation_result
    end


  end
end

