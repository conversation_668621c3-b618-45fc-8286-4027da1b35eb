module Attendance
  class DeductionCalculator
    attr_reader :employee, :start_date, :end_date, :calculation

    def initialize(employee, start_date, end_date, calculation = nil)
      @employee = employee
      @start_date = start_date
      @end_date = end_date
      @calculation = calculation
    end

    # Main method to calculate attendance-based deductions
    def calculate_deduction
      return 0 unless Setting.attendance_deductions_enabled?
      return 0 if employee.exempt_from_attendance_deductions?

      total_missing_hours = calculate_total_missing_hours
      unpaid_leave_days = convert_to_unpaid_days(total_missing_hours)

      return 0 if unpaid_leave_days == 0

      calculate_deduction_amount(unpaid_leave_days)
    end

    # Calculate and create calculation detail for attendance deduction
    def calculate_and_track_deduction
      deduction_amount = calculate_deduction

      if deduction_amount > 0 && calculation
        track_deduction_detail(deduction_amount)
      end

      deduction_amount
    end

    # Get detailed breakdown for reporting
    def detailed_breakdown
      total_missing_hours = calculate_total_missing_hours
      unpaid_leave_days = convert_to_unpaid_days(total_missing_hours)
      deduction_amount = calculate_deduction_amount(unpaid_leave_days)

      {
        total_missing_hours: total_missing_hours,
        threshold_hours: threshold_hours,
        unpaid_leave_days: unpaid_leave_days,
        daily_salary_rate: employee.daily_salary_rate,
        deduction_amount: deduction_amount,
        is_exempt: employee.exempt_from_attendance_deductions?,
        is_enabled: Setting.attendance_deductions_enabled?
      }
    end

    private

    def calculate_total_missing_hours
      full_day_absences = 0
      accumulated_missing_hours = 0

      (start_date..end_date).each do |date|
        next unless should_process_date?(date)
        next if has_manual_leave?(date)

        actual_hours = calculate_actual_work_hours(date)
        daily_work_threshold = Setting.attendance_daily_work_threshold.to_f
        expected_hours = Setting.attendance_daily_expected_hours.to_f

        if actual_hours < daily_work_threshold
          # < 5h worked = 1 full day absence (don't count missing hours)
          full_day_absences += 1
        else
          # ≥ 5h worked = accumulate missing hours for 9-hour rule
          missing_hours = expected_hours - actual_hours
          accumulated_missing_hours += missing_hours if missing_hours > 0
        end
      end

      # Convert full day absences to hours (for the existing logic)
      full_day_hours = full_day_absences * Setting.attendance_daily_expected_hours.to_f

      # Return total: full day hours + accumulated hours
      full_day_hours + accumulated_missing_hours
    end

    def should_process_date?(date)
      # Skip weekends if exclusion is enabled
      return false if Setting.attendance_exclude_weekends? && weekend?(date)

      # Skip holidays if exclusion is enabled and exemptions exist
      return false if Setting.attendance_exclude_holidays? && holiday_exemption_exists?(date)

      # Skip if employee wasn't active on this date
      return false unless employee_active_on_date?(date)

      true
    end

    def weekend?(date)
      # Use configurable weekend days (default: Friday=5, Saturday=6)
      Setting.attendance_weekend_days.include?(date.wday)
    end

    def holiday_exemption_exists?(date)
      # Check for attendance exemptions that affect attendance calculations
      Attendance::Exemption.active
                           .affecting_attendance
                           .for_date(date)
                           .exists?
    end

    def employee_active_on_date?(date)
      # Employee must have started before or on this date
      return false if employee.start_date > date

      # Employee must be active (not terminated)
      return false if employee.status != 'active'

      true
    end

    def has_manual_leave?(date)
      # Check if employee has approved leave for this date
      employee.leaves.approved
              .where("start_date <= ? AND end_date >= ?", date, date)
              .exists?
    end

    def calculate_daily_missing_hours(date)
      # Get actual work hours for the date
      actual_hours = calculate_actual_work_hours(date)

      # Get settings
      expected_hours = Setting.attendance_daily_expected_hours.to_f
      daily_work_threshold = Setting.attendance_daily_work_threshold.to_f

      # Apply 5-hour threshold rule
      if actual_hours < daily_work_threshold
        # Full-day absence: < 5 hours worked = 1 full day deduction (8 hours missing)
        expected_hours
      else
        # ≥ 5 hours worked = accumulate missing hours for 9-hour rule
        missing_hours = expected_hours - actual_hours
        [ missing_hours, 0 ].max
      end
    end

    def calculate_actual_work_hours(date)
      # Get work periods for the date
      work_periods = employee.attendance_periods
                            .where(date: date)
                            .where(period_type: 'work')

      # Sum duration in hours
      total_minutes = work_periods.sum(:duration_minutes)
      total_minutes / 60.0
    end

    def convert_to_unpaid_days(total_missing_hours)
      return 0 if total_missing_hours <= threshold_hours

      # Convert excess hours to unpaid leave days
      excess_hours = total_missing_hours - threshold_hours
      daily_hours = Setting.attendance_daily_expected_hours.to_f

      (excess_hours / daily_hours).ceil
    end

    def threshold_hours
      Setting.attendance_accumulated_hours_threshold.to_f
    end

    def calculate_deduction_amount(unpaid_leave_days)
      return 0 if unpaid_leave_days == 0

      daily_rate = employee.daily_salary_rate
      unpaid_leave_days * daily_rate
    end

    def track_deduction_detail(deduction_amount)
      breakdown = detailed_breakdown

      description = "Attendance deduction: #{breakdown[:unpaid_leave_days]} unpaid days " \
                   "(#{breakdown[:total_missing_hours].round(2)}h missing, " \
                   "threshold: #{breakdown[:threshold_hours]}h)"

      calculation.calculation_details.build(
        detail_type: 'deduction',
        category: 'leave_attendance_based',
        amount: deduction_amount,
        description: description
      )
    end

    private
  end
end
