# frozen_string_literal: true

module Statistics
  module Calculators
    class LateArrivalsCalculator
      include BaseCalculator
      include Statistics::Helpers::DateHelpers
      include Statistics::Helpers::EmployeeHelpers
      include Statistics::Helpers::CardHelpers

      def validate_context(context)
        unless context[:employee_id].present?
          raise ArgumentError, "Missing required context: employee_id must be provided for late arrivals calculator"
        end

        context[:employee] = find_employee(context[:employee_id])
        context[:start_date] = parse_date_with_formats(context[:start_date]) || Date.today.beginning_of_month
        context[:end_date] = parse_date_with_formats(context[:end_date]) || Date.today

        if context[:end_date] < context[:start_date]
          raise ArgumentError, "End date cannot be before start date"
        end

        context[:comparison_period] = (context[:comparison_period] || 'previous').to_sym
        context[:comparison_text] = generate_comparison_text(context[:comparison_period])
      rescue ArgumentError => e
        raise ArgumentError, e.message
      rescue => e
        raise ArgumentError, "Invalid date format: #{e.message}"
      end

      def perform_calculation(context)
        employee = context[:employee]
        start_date = context[:start_date]
        end_date = context[:end_date]
        comparison_period = context[:comparison_period]

        current_late_arrivals = count_late_arrivals(employee, start_date, end_date)

        prev_start_date, prev_end_date = calculate_previous_period(start_date, end_date, comparison_period)
        previous_late_arrivals = count_late_arrivals(employee, prev_start_date, prev_end_date)

        comparison_text = context[:comparison_text] || generate_comparison_text(comparison_period)

        create_metric_card(
          card_id,
          'Late Arrivals',
          current_late_arrivals,
          'times',
          current_late_arrivals,
          previous_late_arrivals,
          comparison_text
        )
      end

      private

      def count_late_arrivals(employee, start_date, end_date)
        Attendance::Period.where(employee: employee)
                        .where(date: start_date..end_date)
                        .where(period_type: Attendance::Period::PERIOD_TYPES[:late])
                        .count
      end
    end
  end
end
