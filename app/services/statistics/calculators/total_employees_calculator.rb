# frozen_string_literal: true

module Statistics
  module Calculators
    class TotalEmployeesCalculator
      include BaseCalculator
      include Statistics::Helpers::CardHelpers

      def validate_context(context)
        # This is a system-wide metric, so no specific validation needed
      end

      def perform_calculation(context)
        # Count all employees
        total_employees = Employee.count

        # Get previous month's count for comparison
        previous_month = Date.today.beginning_of_month - 1.day
        previous_employees = Employee.where('created_at <= ?', previous_month).count

        # Create and return the card
        create_metric_card(
          card_id,
          'Total Number of Employees',
          total_employees,
          'employees',
          total_employees,
          previous_employees,
          'compared to last month'
        )
      end
    end
  end
end
