# frozen_string_literal: true

module Statistics
  module Calculators
    class RemainingLeaveBalanceCalculator
      include BaseCalculator
      include Statistics::Helpers::DateHelpers
      include Statistics::Helpers::EmployeeHelpers
      include Statistics::Helpers::CardHelpers

      # Annual leave entitlement in days
      ANNUAL_LEAVE_ENTITLEMENT = 21

      def validate_context(context)
        unless context[:employee_id].present?
          raise ArgumentError, "Missing required context: employee_id must be provided for remaining leave balance calculator"
        end

        context[:employee] = find_employee(context[:employee_id])
        context[:year] = context[:year]&.to_i || Date.current.year
        context[:start_date] = Date.new(context[:year], 1, 1)
        context[:end_date] = Date.new(context[:year], 12, 31)

        context[:comparison_period] = (context[:comparison_period] || 'year').to_sym
        context[:comparison_text] = generate_comparison_text(context[:comparison_period])
      rescue ArgumentError => e
        raise ArgumentError, e.message
      rescue => e
        raise ArgumentError, "Invalid year or date format: #{e.message}"
      end

      def perform_calculation(context)
        employee = context[:employee]
        start_date = context[:start_date]
        end_date = context[:end_date]
        comparison_period = context[:comparison_period]

        current_balance = calculate_remaining_balance(employee, start_date, end_date)

        prev_start_date, prev_end_date = calculate_previous_period(start_date, end_date, comparison_period)
        previous_balance = calculate_remaining_balance(employee, prev_start_date, prev_end_date)

        comparison_text = context[:comparison_text] || generate_comparison_text(comparison_period)

        create_metric_card(
          card_id,
          'Remaining Leave Balance',
          current_balance,
          'days',
          current_balance,
          previous_balance,
          comparison_text
        )
      end

      private

      def calculate_remaining_balance(employee, start_date, end_date)
        # Get approved annual leaves for the period
        used_annual_leaves = Leave.where(employee: employee)
                                  .where("start_date >= ? AND end_date <= ?", start_date, end_date)
                                  .where(status: :approved)
                                  .where(leave_type: :annual)
                                  .sum(&:duration)

        # Calculate remaining balance
        remaining = ANNUAL_LEAVE_ENTITLEMENT - used_annual_leaves
        [remaining, 0].max # Ensure it doesn't go below 0
      end
    end
  end
end
