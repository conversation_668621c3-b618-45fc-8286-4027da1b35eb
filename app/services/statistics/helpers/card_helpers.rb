# frozen_string_literal: true

module Statistics
  module Helpers
    module CardHelpers
      def calculate_percentage_change(current, previous)
        return 0 if previous.zero?

        ((current - previous) / previous.to_f * 100).round(1)
      end

      def determine_trend(current, previous)
        return 'neutral' if previous.zero? || current == previous

        current > previous ? 'up' : 'down'
      end

      def create_metric_card(id, title, value, unit, current, previous, comparison_text = 'compared to previous period')
        percentage_change = calculate_percentage_change(current, previous)
        trend = determine_trend(current, previous)

        Statistics::MetricCard.new(
          id: id,
          title: title,
          value: value.to_s,
          unit: unit,
          comparison_percentage: percentage_change,
          trend: trend,
          comparison_text: comparison_text
        )
      end
    end
  end
end
