# frozen_string_literal: true

module Statistics
  class MetricCardService
    # Get cards based on requested types
    def get_cards(context, card_types = nil)
      # Get all available calculators
      calculators = available_calculators

      # Filter by requested types if specified
      if card_types.present?
        calculators = calculators.select { |c| card_types.include?(c.card_id.to_sym) }
      end

      # Calculate cards using each calculator
      calculators.map { |calculator| calculator.calculate(context) }
    end

    # Get all available calculators
    def available_calculators
      # Find all calculator classes in the Statistics::Calculators namespace
      calculators = []

      # Get all constants in the Statistics::Calculators namespace
      Statistics::Calculators.constants.each do |const_name|
        const = Statistics::Calculators.const_get(const_name)

        # Check if it's a class that includes BaseCalculator
        if const.is_a?(Class) &&
           const.included_modules.include?(Statistics::Calculators::BaseCalculator) &&
           const_name.to_s.end_with?('Calculator')
          calculators << const.new
        end
      end

      calculators
    end

    # Get all available card types
    def available_card_types
      available_calculators.map { |c| c.card_id.to_sym }
    end
  end
end
