module Salary
  module AttendanceIntegration
    # Calculate attendance deductions based on late arrivals and early departures
    def calculate_attendance_deductions(calculation)
      # Get the employee, start date, and end date from the calculation
      employee = calculation.employee
      start_date = calculation.period_start_date
      end_date = calculation.period_end_date
      gross_salary = calculation.gross_salary

      # Calculate deductions for late arrivals and early departures
      late_deduction = calculate_late_arrivals_deduction(start_date, end_date, gross_salary)
      early_deduction = calculate_early_departures_deduction(start_date, end_date, gross_salary)

      # Return the total deduction
      late_deduction + early_deduction
    end

    # Calculate total working hours for the period
    def calculate_total_hours(calculation)
      # Get the employee, start date, and end date from the calculation
      employee = calculation.employee
      start_date = calculation.period_start_date
      end_date = calculation.period_end_date

      # Get all work periods for the employee during the date range
      work_periods = employee.attendance_periods
                            .where(date: start_date..end_date)
                            .where(period_type: 'work')

      # Sum up the duration in minutes
      total_minutes = work_periods.sum(:duration_minutes)

      # Convert to hours (with 2 decimal places)
      (total_minutes / 60.0).round(2)
    end
  end
end
