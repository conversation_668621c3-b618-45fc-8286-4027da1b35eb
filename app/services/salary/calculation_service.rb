module Salary
  class CalculationService
    include Salary::LeaveIntegration
    include Salary::TaxIntegration
    include Salary::SocialSecurityIntegration
    include Salary::MedicalInsuranceIntegration
    include Salary::CalculationTracking
    attr_reader :employee, :start_date, :end_date, :reason, :options, :errors

    def initialize(employee, options = {})
      @employee = employee
      @start_date = options[:start_date]
      @end_date = options[:end_date]
      @reason = options[:reason]
      @options = options
      @errors = []

      # If period is provided, parse it to get start_date and end_date
      if options[:period].present?
        parse_period(options[:period])
      end
    end

    def parse_period(period)
      if period =~ /\A\d{4}-\d{2}\z/
        # Monthly period (YYYY-MM)
        year, month = period.split('-').map(&:to_i)
        @start_date = Date.new(year, month, 1)
        @end_date = @start_date.end_of_month
      elsif period =~ /\A\d{4}-\d{2}-\d{2}_\d{4}-\d{2}-\d{2}\z/
        # Custom period (YYYY-MM-DD_YYYY-MM-DD)
        start_str, end_str = period.split('_')
        @start_date = Date.parse(start_str)
        @end_date = Date.parse(end_str)
      elsif period =~ /\A\d{4}-\d{2}-\d{2}\z/
        # Single day period (YYYY-MM-DD)
        @start_date = Date.parse(period)
        @end_date = @start_date
      else
        @errors << "Invalid period format: #{period}"
      end
    end

    def calculate
      return nil if invalid?

      # Find current salary package for the period
      package = employee.salary_packages.current(@start_date).order(effective_date: :desc).first

      return nil unless package

      # Generate period string based on dates
      if @start_date.day == 1 && @end_date.day == @end_date.end_of_month.day &&
         @start_date.month == @end_date.month && @start_date.year == @end_date.year
        # Full month - use YYYY-MM format
        period_str = "#{@start_date.year}-#{@start_date.month.to_s.rjust(2, '0')}"
      elsif @start_date == @end_date
        # Single day - use YYYY-MM-DD format
        period_str = "#{@start_date.year}-#{@start_date.month.to_s.rjust(2, '0')}-#{@start_date.day.to_s.rjust(2, '0')}"
      else
        # Custom range - use YYYY-MM-DD_YYYY-MM-DD format
        period_str = "#{@start_date.year}-#{@start_date.month.to_s.rjust(2, '0')}-#{@start_date.day.to_s.rjust(2, '0')}_#{@end_date.year}-#{@end_date.month.to_s.rjust(2, '0')}-#{@end_date.day.to_s.rjust(2, '0')}"
      end

      # Create or find existing calculation
      calculation = SalaryCalculation.find_or_initialize_by(
        employee: employee,
        period_start_date: @start_date,
        period_end_date: @end_date
      )

      # Set basic attributes
      calculation.salary_package = package
      calculation.period = period_str

      # Determine if we need to pro-rate the salary
      is_full_month = @start_date.day == 1 && @end_date.day == @end_date.end_of_month.day &&
                      @start_date.month == @end_date.month && @start_date.year == @end_date.year

      if is_full_month
        # Full month - use full salary
        calculation.gross_salary = package.total_package_value
      else
        # Partial period - pro-rate based on working days
        working_days_in_period = calculate_working_days(@start_date, @end_date)
        working_days_in_month = calculate_working_days(@start_date.beginning_of_month, @start_date.end_of_month)

        # Pro-rate based on working days
        pro_rate_factor = working_days_in_period.to_f / working_days_in_month
        calculation.gross_salary = package.total_package_value * pro_rate_factor
      end

      # Calculate enhanced deductions with detailed breakdown
      deductions = calculate_enhanced_deductions(calculation)

      # Add termination-specific calculations if needed
      if @reason == "termination"
        deductions.merge!(calculate_termination_deductions(calculation))
      end

      calculation.deductions = deductions

      # Calculate net salary including leave deductions from calculation_details
      main_deductions = deductions.values.sum(&:to_f)

      # Include both persisted and in-memory calculation_details for leave deductions
      leave_deductions = calculation.calculation_details
                                   .select { |detail|
                                     detail.detail_type == 'deduction' &&
                                     [ 'leave_unpaid', 'leave_attendance_based' ].include?(detail.category)
                                   }
                                   .sum(&:amount)

      total_deductions = main_deductions + leave_deductions
      # Ensure net salary is always positive
      calculation.net_salary = [ calculation.gross_salary - total_deductions, 0.01 ].max

      # Calculate and set total hours
      calculation.total_hours = calculate_total_hours(calculation)

      # Set calculation date and status
      calculation.calculation_date = Time.current
      calculation.status = :draft
      calculation.notes = @reason if @reason.present?

      # Track calculation details
      track_calculation_details(calculation)

      if calculation.save
        calculation
      else
        @errors += calculation.errors.full_messages
        nil
      end
    end

    private

    def invalid?
      @errors = []

      if employee.nil?
        @errors << "Employee is required"
      end

      if @start_date.nil?
        @errors << "Start date is required"
      end

      if @end_date.nil?
        @errors << "End date is required"
      end

      if @start_date && @end_date && @start_date > @end_date
        @errors << "Start date must be before or equal to end date"
      end

      @errors.any?
    end

    def calculate_enhanced_deductions(calculation)
      social_security_breakdown = calculate_social_security_breakdown(calculation.gross_salary, @start_date)

      # Calculate leave deductions separately - this creates calculation_details directly
      # and doesn't need to be included in the main deductions hash
      calculate_leave_deductions(calculation)

      {
        employee_social_security: social_security_breakdown[:employee_social_security] || 0,
        income_tax: 0.0,  # No tax calculations as requested
        medical_insurance: 0.0,  # No medical insurance calculations as requested
        salary_advances: calculate_salary_advances(calculation),
        other_deductions: calculate_other_deductions(calculation)
        # Note: leave deductions are handled through calculation_details, not the main deductions hash
      }
    end

    def calculate_medical_insurance(calculation)
      # Medical insurance calculations disabled as requested
      0.0
    end

    def calculate_salary_advances(calculation)
      # Calculate any salary advances for this employee/period
      # This could come from a separate AdvancePayment model
      # For now, return 0 - can be enhanced later
      0.0
    end

    def calculate_other_deductions(calculation)
      # Calculate other miscellaneous deductions
      # This could include things like loan repayments, equipment costs, etc.
      # For now, return 0 - can be enhanced later
      0.0
    end

    # These methods are now provided by the included concerns:
    # - calculate_social_security from SocialSecurityIntegration
    # - calculate_income_tax from TaxIntegration
    # - calculate_tax_from_brackets from TaxIntegration

    def calculate_working_days(start_date, end_date)
      (start_date..end_date).count { |date| (1..5).include?(date.wday) }
    end

    # This method is now provided by the included concern:
    # - calculate_attendance_deductions from AttendanceIntegration

    # Removed redundant method calculate_attendance_deductions_custom

    def count_approved_leave_days(start_date, end_date)
      # Count days with approved leaves (excluding unpaid leaves which are handled separately)
      employee.leaves
              .approved
              .where.not(leave_type: :unpaid)
              .where("start_date <= ? AND end_date >= ?", end_date, start_date)
              .sum do |leave|
        leave_start = [ leave.start_date, start_date ].max
        leave_end = [ leave.end_date, end_date ].min
        (leave_start..leave_end).count { |date| (1..5).include?(date.wday) }
      end
    end

    def calculate_late_arrivals_deduction(start_date, end_date, gross_salary)
      # Get late periods from attendance_periods
      late_periods = employee.attendance_periods
                             .where(date: start_date..end_date)
                             .where(period_type: 'late')

      # Calculate deduction based on company policy
      # This is a simplified example - actual logic would depend on specific rules
      total_late_minutes = late_periods.sum(:duration_minutes)

      # Example: Deduct 1% of daily rate for every 30 minutes late
      working_days = calculate_working_days(start_date, end_date)
      return 0 if working_days == 0 # Avoid division by zero

      daily_rate = gross_salary / working_days
      (total_late_minutes / 30.0) * (daily_rate * 0.01)
    end

    def calculate_early_departures_deduction(start_date, end_date, gross_salary)
      # Get early departure periods from attendance_periods
      early_departure_periods = employee.attendance_periods
                                        .where(date: start_date..end_date)
                                        .where(period_type: 'early_departure')

      # Calculate deduction based on company policy
      # This is a simplified example - actual logic would depend on specific rules
      total_early_minutes = early_departure_periods.sum(:duration_minutes)

      # Example: Deduct 1% of daily rate for every 30 minutes of early departure
      working_days = calculate_working_days(start_date, end_date)
      return 0 if working_days == 0 # Avoid division by zero

      daily_rate = gross_salary / working_days
      (total_early_minutes / 30.0) * (daily_rate * 0.01)
    end

    # This method is now provided by the included concern:
    # - calculate_leave_deductions from LeaveIntegration

    # Removed redundant method calculate_leave_deductions_custom

    def calculate_termination_deductions(calculation)
      # Calculate any termination-specific deductions
      # This could include things like:
      # - Prorated benefits
      # - Equipment returns
      # - Outstanding advances

      # For now, return an empty hash - this can be expanded based on specific requirements
      {}
    end

    def calculate_total_hours(calculation)
      # Get the employee, start date, and end date from the calculation
      employee = calculation.employee
      start_date = calculation.period_start_date
      end_date = calculation.period_end_date

      # Get all work periods for the employee during the date range
      work_periods = employee.attendance_periods
                            .where(date: start_date..end_date)
                            .where(period_type: 'work')

      # Sum up the duration in minutes
      total_minutes = work_periods.sum(:duration_minutes)

      # Convert to hours (with 2 decimal places)
      (total_minutes / 60.0).round(2)
    end
  end
end
