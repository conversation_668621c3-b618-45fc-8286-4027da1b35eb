module Salary
  class SlipService
    attr_reader :salary_calculation, :config

    def initialize(salary_calculation, options = {})
      @salary_calculation = salary_calculation
      @config = SalarySlipConfig.current
    end

    def generate
      return false unless salary_calculation.paid?

      # Use HTML-to-PDF generation with Grover
      pdf_content = generate_html_pdf

      # Attach PDF to salary calculation
      attach_pdf(pdf_content)
    end

    private

    # HTML-to-PDF generation using Grover
    def generate_html_pdf
      require 'grover'

      # Generate HTML content directly for now (until template rendering is fixed)
      html_content = generate_html_content

      # Fallback: try template rendering if direct generation fails
      if html_content.blank? || html_content.length < 100
        html_content = render_template_content
      end

      # Convert HTML to PDF using Grover (uses global configuration with external Chrome)
      grover = Grover.new(html_content)
      grover.to_pdf
    rescue => e
      Rails.logger.error "Failed to generate HTML PDF: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise e
    end

    def attach_pdf(content)
      # Create a StringIO object instead of a temporary file
      pdf_io = StringIO.new(content)

      # Attach the PDF to the salary calculation
      salary_calculation.salary_slip_pdf.attach(
        io: pdf_io,
        filename: "salary_slip_#{salary_calculation.employee.name.parameterize}_#{salary_calculation.period}.pdf",
        content_type: 'application/pdf'
      )

      true
    rescue => e
      Rails.logger.error "Failed to attach PDF: #{e.message}"
      false
    end

    # Generate HTML content directly (temporary solution)
    def generate_html_content
      employee = salary_calculation.employee

      # Include helper methods
      extend SalarySlipHelper
      @config = config
      @salary_calculation = salary_calculation
      @employee = employee

      <<~HTML
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
          <title>Salary Slip - #{employee.name}</title>
          <style>
            #{render_styles}
          </style>
        </head>
        <body>
          <div class="salary-slip">
            <div class="slip-content">
              #{render_header_section}
              #{render_employee_details_section}
              #{render_salary_table_section}
              #{render_totals_section}
            </div>
            #{render_footer_section}
          </div>
        </body>
        </html>
      HTML
    end

    # Fallback template rendering method
    def render_template_content
      begin
        controller = Api::Finance::SalaryCalculationsController.new
        controller.request = ActionDispatch::Request.new({})
        controller.response = ActionDispatch::Response.new

        controller.instance_variable_set(:@salary_calculation, salary_calculation)
        controller.instance_variable_set(:@employee, salary_calculation.employee)
        controller.instance_variable_set(:@config, config)

        controller.render_to_string(
          template: 'salary_slips/slip',
          layout: false
        )
      rescue => e
        Rails.logger.error "Template rendering failed: #{e.message}"
        ""
      end
    end

    # HTML rendering helper methods
    def render_styles
      <<~CSS
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        @page {
          margin: 0.2in;
        }

        body {
          font-family: "Inter", sans-serif;
          font-size: 14px;
          line-height: 1.5;
          color: #333;
          background: white;
          margin: 0;
          padding: 0;
          min-height: 100vh;
          display: flex;
          flex-direction: column;
        }

        .salary-slip {
          width: 100%;
          max-width: 800px;
          margin: 0 auto;
          background: white;
          padding: 0;
          flex: 1;
          display: flex;
          flex-direction: column;
        }

        /* Header Section */
        .slip-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-end;
          margin-bottom: 24px;
        }

        .header-left {
          display: flex;
          align-items: center;
          gap: 20px;
        }

        .logo-container {
          width: 79px;
          height: 125px;
          gap: 16px;
          display: flex;
          align-items: end;
          justify-content: center;
          flex-shrink: 0;
        }

        .logo-container svg {
          width: 120px;
          height: 120px;
          max-width: 120px;
          max-height: 120px;
        }

        .logo-container img {
          width: 120px;
          height: 120px;
          max-width: 120px;
          max-height: 120px;
          object-fit: contain;
        }

        .company-info {
          display: flex;
          flex-direction: column;
          gap: 6px;
        }

        .company-name {
          font-size: 14px;
          font-weight: 600;
          color: #000;
          letter-spacing: 2px;
        }

        .company-subtitle {
          font-size: 14px;
          color: #667085;
        }

        .company-address {
          font-size: 12px;
          color: #667085;
          font-weight: 400;
          line-height: 1.4;
          display: flex;
          flex-direction: column;
          gap: 6px;
        }

        .header-right {
          text-align: right;
        }

        .total-amount-section {
          text-align: right;
        }

        .total-label {
          font-size: 12px;
          color: #667085;
          font-weight: 500;
          margin-bottom: 4px;
        }

        .total-value {
          font-size: 20px;
          font-weight: bold;
          color: #000;
        }

        .sections-wrapper {
          border: 1px solid #e0e2e7;
          padding: 24px;
          border-radius: 12px;
        }

        /* Employee Details Section */
        .employee-details {
          display: flex;
          justify-content: space-between;
          margin-bottom: 32px;
          gap: 32px;
        }

        .details-left {
          flex: 1;
          background-color: #fafafa;
          padding: 12px;
          border-radius: 8px;
          display: flex;
          flex-direction: column;
          gap: 8px;
          min-width: 200px;
        }

        .details-right {
          flex: 2;
          display: flex;
          align-items: center;
        }

        .detail-item {
          margin-bottom: 8px;
        }

        .detail-label {
          font-size: 11px;
          color: #667085;
          margin-bottom: 2px;
          font-weight: 400;
          line-height: 1.2;
        }

        .detail-value {
          font-size: 13px;
          color: #000;
          font-weight: 600;
          line-height: 1.2;
        }

        .employee-info {
          text-align: left;
        }

        .job-title {
          font-size: 12px;
          color: #667085;
          margin-bottom: 4px;
        }

        .employee-name {
          font-size: 14px;
          font-weight: 600;
          color: #333843;
          margin-bottom: 4px;
          line-height: 1.2;
        }

        .contact-info {
          font-size: 12px;
          color: #667085;
          line-height: 1.3;
        }

        /* Additional Details Inline */
        .additional-details {
          margin: 16px 0;
          padding: 12px;
          background-color: #fafafa;
          border-radius: 6px;
        }

        .details-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
        }

        .details-row:last-child {
          margin-bottom: 0;
        }

        .detail-item-inline {
          display: flex;
          gap: 8px;
          align-items: center;
        }

        .detail-label-inline {
          font-size: 11px;
          color: #667085;
          font-weight: 400;
        }

        .detail-value-inline {
          font-size: 11px;
          color: #333843;
          font-weight: 600;
        }

        .note-section {
          margin-top: 16px;
        }

        .note-label {
          font-size: 12px;
          color: #667085;
          margin-bottom: 2px;
        }

        .note-text {
          font-size: 12px;
          color: #333843;
          font-weight: 500;
          line-height: 1.3;
        }

        /* Salary Table */
        .salary-table-section {
          margin-bottom: 16px;
        }

        .salary-table {
          width: 100%;
          border-collapse: collapse;
          border: 1px solid #f0f1f3;
          border-radius: 8px;
          overflow: hidden;
        }

        .salary-table th {
          background-color: #f8f9fa;
          padding: 16px;
          font-size: 10px;
          font-weight: 500;
          color: #667085;
          text-align: left;
          border-bottom: 1px solid #f0f1f3;
          #line-height: 1.3;
        }

        .salary-table th:not(:last-child) {
          border-right: 1px solid #f0f1f3;
        }

        .salary-table td {
          padding: 8px;
          border-bottom: 1px solid #f0f1f3;
          font-size: 12px;
          font-weight: 500;
          color: #333843;
          #line-height: 1.4;
        }

        .salary-table td:first-child {
          min-width: 300px;
          padding-inline-start: 16px;
        }

        .salary-table td:nth-child(2),
        .salary-table td:nth-child(3) {
          text-align: center;
        }

        .salary-table td:last-child {
          text-align: end;
        }

        .salary-table .description {
          padding-top: 4px;
          margin: 0;
          padding-bottom: 0;
          color: #667085;
        }

        /* Totals Section */
        .totals-section {
          display: flex;
          justify-content: flex-end;
          margin-bottom: 16px;
        }

        .totals-container {
          width: 300px;
        }

        .totals-table {
          width: 100%;
          border-collapse: collapse;
        }

        .totals-table td {
          padding: 4px 0;
          font-size: 12px;
          line-height: 1.2;
        }

        .totals-table .total-label {
          color: #667085;
          text-align: left;
        }

        .totals-table .total-value {
          color: #333843;
          text-align: right;
          font-weight: 500;
        }

        .totals-table .final-total td {
          border-top: 2px solid #f0f1f3;
          padding-top: 6px;
          font-weight: 600;
          font-size: 13px;
          line-height: 1.2;
        }

        .totals-table .empty-row td {
          padding: 2px 0;
        }

        .totals-table .net-pay-row td {
          border-top: 1px solid #f0f1f3;
          border-bottom: 1px solid #f0f1f3;
          padding: 6px 0;
          font-weight: 600;
          font-size: 13px;
          background-color: #f8f9fa;
          line-height: 1.2;
        }

        /* Signature Section */
        .signature-section {
          margin-bottom: 0;
          padding: 8px 0 0 0;
        }

        .signature-container {
          display: flex;
          justify-content: flex-start;
          gap: 40px;
        }

        .signature-item {
          display: flex;
          flex-direction: column;
          gap: 8px;
          min-width: 200px;
        }

        .signature-label {
          font-size: 12px;
          color: #667085;
          font-weight: 500;
        }

        .signature-line {
          border-bottom: 1px solid #333;
          height: 25px;
        }

        /* Content wrapper to push footer down */
        .slip-content {
          flex: 1;
        }

        /* Footer */
        .slip-footer {
          padding-top: 24px;
          margin-top: auto;
        }

        .footer-content {
          text-align: start;
        }

        .terms-title {
          font-size: 12px;
          font-weight: 400;
          color: #667085;
          margin-bottom: 4px;
        }

        .terms-text {
          font-size: 12px;
          font-weight: 500;
          color: #333843;
          line-height: 1.32;
        }
      CSS
    end

    def render_header_section
      # Try to read SVG and convert to optimized format for PDF
      logo_content = render_logo_content

      <<~HTML
        <header class="slip-header">
          <div class="header-left">
            <div class="logo-container">
              #{logo_content}
            </div>
            <div class="company-info">
              <div class="company-name">#{Setting.company_name}</div>
              <div class="company-subtitle">#{Setting.company_tagline}</div>
              <div class="company-address">
                <span>#{Setting.company_address}</span>
                <span>#{Setting.company_phone.national} | #{Setting.company_email}</span>
                <span>#{Date.current.strftime('%d/%m/%Y')}</span>
              </div>
            </div>
          </div>
          <div class="header-right">
            <div class="total-amount-section">
              <div class="total-label">Total Amount</div>
              <div class="total-value">#{format_currency(@salary_calculation.net_salary)}</div>
            </div>
          </div>
        </header>
      HTML
    end

    def render_logo_content
      begin
        require 'base64'

        # Try PNG first, then fallback to SVG
        png_path = Rails.root.join('app', 'assets', 'images', 'athar_logo.png')
        svg_path = Rails.root.join('app', 'assets', 'images', 'athar_logo.svg')

        if File.exist?(png_path)
          # Use PNG with base64 encoding for better PDF compatibility
          png_data = File.read(png_path)
          base64_png = Base64.strict_encode64(png_data)
          %(<img src="data:image/png;base64,#{base64_png}" alt="Company Logo" />)
        elsif File.exist?(svg_path)
          # Fallback to SVG
          svg_content = File.read(svg_path)
          svg_content
        else
          '<div>A</div>'
        end
      rescue => e
        Rails.logger.error "Failed to load logo: #{e.message}"
        '<div>A</div>'
      end
    end

    def render_employee_details_section
      <<~HTML
        <div class="sections-wrapper">
          <section class="employee-details">
            <div class="details-left">
              <div class="detail-item">
                <div class="detail-label">Date</div>
                <div class="detail-value">#{Date.current.strftime('%d/%m/%Y')}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Salary Month</div>
                <div class="detail-value">#{format_salary_month(@salary_calculation.period)}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Employee ID</div>
                <div class="detail-value">#{@employee.employee_id || @employee.id}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Payment Method</div>
                <div class="detail-value">#{@salary_calculation.payment_method || default_payment_method}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Payment Date</div>
                <div class="detail-value">#{format_payment_date(@salary_calculation.payment_date)}</div>
              </div>
            </div>

            <div class="details-right">
              <div class="employee-info">
                <div class="job-title">#{employee_job_title}</div>
                <div class="employee-name">#{@employee.name}</div>
                <div class="contact-info">
                  #{employee_contact_display.map { |line| "<div>#{line}</div>" }.join("\n                  ")}
                </div>

                <!-- Additional Employee Details -->
                <div class="additional-details">
                  <div class="details-row">
                    <div class="detail-item-inline">
                      <span class="detail-label-inline">Staff Function Code:</span>
                      <span class="detail-value-inline">#{@employee.staff_function_code || '0'}</span>
                    </div>
                    <div class="detail-item-inline">
                      <span class="detail-label-inline">Workplace:</span>
                      <span class="detail-value-inline">#{@employee.workplace_code || '0'}</span>
                    </div>
                  </div>
                  <div class="details-row">
                    <div class="detail-item-inline">
                      <span class="detail-label-inline">Industry:</span>
                      <span class="detail-value-inline">#{company_industry}</span>
                    </div>
                    <div class="detail-item-inline">
                      <span class="detail-label-inline">Contract End:</span>
                      <span class="detail-value-inline">#{format_contract_end_date(@employee.contract_end_date)}</span>
                    </div>
                  </div>
                </div>

                <div class="note-section">
                  <div class="note-label">Note</div>
                  <div class="note-text">
                    This salary slip is generated automatically and contains confidential information.
                    Please keep this document for your records.
                  </div>
                </div>
              </div>
            </div>
          </section>
      HTML
    end

    def render_salary_table_section
      <<~HTML
          <section class="salary-table-section">
            <table class="salary-table">
              <thead>
                <tr>
                  <th>ARTICLE</th>
                  <th>AMOUNT</th>
                  <th>DEDUCTIONS</th>
                  <th>FINAL AMOUNT</th>
                </tr>
              </thead>
              <tbody>
                #{render_salary_rows}
              </tbody>
            </table>
          </section>
      HTML
    end

    def render_salary_rows
      rows = []

      # Gross Salary Row
      rows << render_salary_row(
        "Gross Salary",
        "Basic salary amount",
        @salary_calculation.gross_salary,
        0,
        @salary_calculation.gross_salary
      )

      # Employer Social Security contributions (informational)
      if @salary_calculation.employer_social_security > 0
        # Convert period string to Date object for SocialSecurityConfig.current
        period_date = @salary_calculation.period.is_a?(Date) ? @salary_calculation.period : Date.parse("#{@salary_calculation.period}-01")
        config = SocialSecurityConfig.current(period_date)
        employer_rate_text = config ? "#{config.employer_rate}% employer contribution" : "employer contribution"

        rows << render_employer_contribution_row(
          "Employer Social Security",
          employer_rate_text,
          @salary_calculation.employer_social_security
        )
      end

      # Employee Deductions - Only show deductions that have values
      if @salary_calculation.employee_social_security > 0
        # Convert period string to Date object for SocialSecurityConfig.current
        period_date = @salary_calculation.period.is_a?(Date) ? @salary_calculation.period : Date.parse("#{@salary_calculation.period}-01")
        config = SocialSecurityConfig.current(period_date)
        employee_rate_text = config ? "#{config.employee_rate}% employee contribution" : "employee contribution"

        rows << render_deduction_row(
          "Employee Social Security",
          employee_rate_text,
          @salary_calculation.employee_social_security
        )
      end

      if @salary_calculation.income_tax > 0
        rows << render_deduction_row(
          "Income Tax",
          "Income tax deduction",
          @salary_calculation.income_tax
        )
      end

      if @salary_calculation.medical_insurance > 0
        rows << render_deduction_row(
          "Family Med. Ins. Deduction",
          "Medical insurance deduction",
          @salary_calculation.medical_insurance
        )
      end

      if @salary_calculation.salary_advances > 0
        rows << render_deduction_row(
          "Advances on Salary",
          "Salary advances",
          @salary_calculation.salary_advances
        )
      end

      if @salary_calculation.other_deductions > 0
        rows << render_deduction_row(
          "Other",
          "Other deductions",
          @salary_calculation.other_deductions
        )
      end

      # Always show leave deductions (even if zero) for transparency
      rows << render_deduction_row(
        "Leave Deductions",
        "Unpaid leave and auto-absence deductions",
        @salary_calculation.leave_deductions
      )

      rows.join("\n")
    end

    def render_totals_section
      <<~HTML
          <section class="totals-section">
            <div class="totals-container">
              <table class="totals-table">
                <tr>
                  <td class="total-label">Total Deductions</td>
                  <td class="total-value">#{format_currency(@salary_calculation.total_deductions || 0)}</td>
                </tr>
                <tr>
                  <td class="total-label">Total Taxes</td>
                  <td class="total-value">#{format_currency(calculate_total_taxes || 0)}</td>
                </tr>
                <tr>
                  <td class="total-label">Total VAT</td>
                  <td class="total-value">#{format_currency(0)}</td>
                </tr>
                <tr class="empty-row">
                  <td></td>
                  <td></td>
                </tr>
                <tr class="net-pay-row">
                  <td class="total-label">Net to Pay</td>
                  <td class="total-value">#{format_currency(@salary_calculation.net_to_pay || @salary_calculation.net_salary)}</td>
                </tr>
                <tr class="empty-row">
                  <td></td>
                  <td></td>
                </tr>
                <tr class="final-total">
                  <td class="total-label">Total Price</td>
                  <td class="total-value">#{format_currency(@salary_calculation.net_salary)}</td>
                </tr>
              </table>
            </div>
          </section>

          <!-- Signature Section -->
          <section class="signature-section">
            <div class="signature-container">
              <div class="signature-item">
                <div class="signature-label">Signature Employee:</div>
                <div class="signature-line"></div>
              </div>
            </div>
          </section>
        </div>
      HTML
    end

    def render_footer_section
      <<~HTML
        <footer class="slip-footer">
          <div class="footer-content">
            <div class="terms-title">Terms & Conditions</div>
            <div class="terms-text">
              This is a computer-generated document and does not require a signature.
              Please keep this document for your records.
            </div>
          </div>
        </footer>
      HTML
    end

    # Helper methods for new fields
    def format_payment_date(date)
      return Date.current.strftime('%d/%m/%y') unless date
      date.strftime('%d/%m/%y')
    end

    def format_contract_end_date(date)
      return '-' unless date
      date.strftime('%d/%m/%Y')
    end

    def format_salary_month(period)
      return Date.current.strftime('%B, %Y') unless period
      period.strftime('%B, %Y')
    end

    def default_payment_method
      'Transfer'
    end

    def company_industry
      'Software'
    end

    def employee_job_title
      @employee.job_title || @employee.position || 'Employee'
    end

    def employee_contact_display
      @employee.contact_display_lines
    end

    def calculate_total_taxes
      (@salary_calculation.income_tax || 0) + (@salary_calculation.employee_social_security || 0)
    end

    def format_currency(amount)
      return 'JOD 0.00' if amount.nil? || amount == 0
      "JOD #{sprintf('%.2f', amount.abs)}"
    end

    # Helper methods for rendering salary rows
    def render_salary_row(title, description, amount, deduction, final_amount)
      <<~HTML
        <tr>
          <td>
            #{title}<br><span class="description">#{description}</span>
          </td>
          <td>#{format_currency(amount)}</td>
          <td>#{deduction > 0 ? format_currency(deduction) : '-'}</td>
          <td>#{format_currency(final_amount)}</td>
        </tr>
      HTML
    end

    def render_deduction_row(title, description, amount)
      <<~HTML
        <tr>
          <td>
            #{title}<br><span class="description">#{description}</span>
          </td>
          <td>-</td>
          <td>#{format_currency(amount)}</td>
          <td>#{format_currency(-amount)}</td>
        </tr>
      HTML
    end

    def render_employer_contribution_row(title, description, amount)
      <<~HTML
        <tr>
          <td>
            #{title}<br><span class="description">#{description}</span>
          </td>
          <td>#{format_currency(amount)}</td>
          <td>-</td>
          <td>#{format_currency(amount)}</td>
        </tr>
      HTML
    end
  end
end
