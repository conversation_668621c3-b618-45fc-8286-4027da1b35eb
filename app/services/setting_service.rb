class SettingService
  class << self
    # Get a setting with type-aware parsing
    def get(namespace, key, default = nil)
      setting = Setting.find_by(namespace: namespace, key: key)
      setting ? setting.value : default
    rescue => e
      Rails.logger.error("Error getting setting #{namespace}.#{key}: #{e.message}")
      default
    end

    # Set a setting with type-aware parsing
    def set(namespace, key, value, description = nil, is_editable = true, setting_type = :string)
      setting = Setting.find_or_initialize_by(namespace: namespace, key: key)
      setting.setting_type = setting_type
      setting.typed_value = value
      setting.description = description if description
      setting.is_editable = is_editable
      setting.save
    end

    # Bulk update settings
    def bulk_update(settings_hash)
      Setting.transaction do
        settings_hash.each do |logical_key, attributes|
          namespace, key = logical_key.split('/', 2)
          set(namespace, key, attributes[:value], attributes[:description],
              attributes[:is_editable], attributes[:setting_type])
        end
      end
    end

    # Get settings by namespace
    def get_namespace(namespace)
      Setting.where(namespace: namespace).index_by(&:key).transform_values(&:typed_value)
    end

    # Check if setting exists
    def exists?(namespace, key)
      Setting.exists?(namespace: namespace, key: key)
    end

    # Reset setting to default (if defined)
    def reset_to_default(namespace, key)
      setting = Setting.find_by(namespace: namespace, key: key)
      return false unless setting

      default_value = default_for(namespace, key)
      return false unless default_value

      setting.typed_value = default_value
      setting.save
    end

    private

    # Get default value for a setting (could be extended with a defaults registry)
    def default_for(namespace, key)
      # This could be extended to read from a configuration file or registry
      case "#{namespace}/#{key}"
      when 'attendance/work_start_time'
        '09:00'
      when 'attendance/work_end_time'
        '17:00'
      when 'attendance/required_work_minutes'
        480
      when 'attendance/deductions_enabled'
        false
      else
        nil
      end
    end
  end
end
