class MonthlySalaryCalculationJob < ApplicationJob
  queue_as :default

  def perform(period = nil)
    # Calculate for current month by default
    period ||= Date.today.strftime("%Y-%m")
    calculation_date = Date.parse("#{period}-01")

    results = {
      success: [],
      failure: [],
      no_salary_package: []
    }

    Employee.active.each do |employee|
      # Get the approved salary package for the calculation period
      salary_package = current_salary_package_for(employee, calculation_date)

      if salary_package.nil?
        results[:no_salary_package] << {
          employee_id: employee.id,
          error: "No approved salary package found for period #{period}"
        }
        next
      end

      service = Salary::CalculationService.new(employee, period: period)
      calculation = service.calculate

      if calculation
        results[:success] << {
          employee_id: employee.id,
          calculation_id: calculation.id,
          salary_package_id: salary_package.id
        }
      else
        results[:failure] << {
          employee_id: employee.id,
          errors: service.errors
        }
      end
    end

    # Log results with more detail
    Rails.logger.info("Monthly salary calculation completed for #{period}: " \
                        "#{results[:success].count} succeeded, " \
                        "#{results[:failure].count} failed, " \
                        "#{results[:no_salary_package].count} no salary package")

    results
  end

  private

  def current_salary_package_for(employee, date = Date.current)
    employee.salary_packages.current(date).first
  end
end
