# Cleanup job for stale draft salary packages
# Focuses on truly abandoned drafts to prevent accumulation
class CleanupStaleDraftPackagesJob < ApplicationJob
  queue_as :low_priority

  def perform(days_old = 30)
    # Only cleanup truly abandoned drafts, not recently cancelled ones
    # Find orphaned drafts (no creator)
    orphaned_drafts = SalaryPackage.draft
                                   .where("created_at < ?", days_old.days.ago)
                                   .where(created_by_id: nil)

    # Find drafts from inactive employees
    inactive_creator_drafts = SalaryPackage.draft
                                           .joins(:created_by)
                                           .where("salary_packages.created_at < ?", days_old.days.ago)
                                           .where(employees: { status: :inactive })

    # Combine the IDs and get the final collection
    stale_draft_ids = orphaned_drafts.pluck(:id) + inactive_creator_drafts.pluck(:id)
    stale_drafts = SalaryPackage.where(id: stale_draft_ids).includes(:employee, :created_by)

    results = {
      deleted: 0,
      errors: [],
      reasons: []
    }

    stale_drafts.find_each do |package|
      begin
        reason = determine_cleanup_reason(package)
        package.destroy!
        results[:deleted] += 1
        results[:reasons] << reason
        Rails.logger.info("Deleted stale draft package #{package.id}: #{reason}")
      rescue => e
        results[:errors] << {
          package_id: package.id,
          employee_id: package.employee_id,
          error: e.message
        }
        Rails.logger.error("Failed to delete stale draft package #{package.id}: #{e.message}")
      end
    end

    Rails.logger.info("Cleanup completed: #{results[:deleted]} deleted, #{results[:errors].count} errors")
    results
  end

  private

  def determine_cleanup_reason(package)
    if package.created_by_id.nil?
      "Orphaned draft (no creator employee)"
    elsif package.created_by&.inactive?
      "Creator employee is inactive"
    else
      "Stale draft older than threshold"
    end
  end
end
