class TimeUtility
  class << self
    # Convert time value to timestamp for a specific date
    def convert_time_to_timestamp(time_value, date)
      if time_value.is_a?(Time)
        hours, minutes = time_value.hour, time_value.min
      else
        hours, minutes = time_value.to_s.split(":").map(&:to_i)
      end

      date.to_time.change(hour: hours, min: minutes).to_i
    end

    # Convert HH:MM string to Time object for today
    def parse_time_string(time_string, date = Date.current)
      hours, minutes = time_string.split(":").map(&:to_i)
      date.to_time.change(hour: hours, min: minutes)
    end

    # Format time to HH:MM string
    def format_time_to_string(time)
      time.strftime('%H:%M')
    end

    # Check if time string is in valid HH:MM format
    def valid_time_format?(time_string)
      time_string.match?(/^\d{1,2}:\d{2}$/)
    end

    # Convert minutes to HH:MM format
    def minutes_to_time_string(minutes)
      hours = minutes / 60
      mins = minutes % 60
      format('%02d:%02d', hours, mins)
    end

    # Convert HH:MM to total minutes
    def time_string_to_minutes(time_string)
      hours, mins = time_string.split(':').map(&:to_i)
      (hours * 60) + mins
    end

    # Calculate time difference in minutes
    def time_difference_in_minutes(start_time, end_time)
      ((end_time - start_time) / 60).round
    end

    # Check if time is within business hours
    def within_business_hours?(time, start_hour = 9, end_hour = 17)
      time.hour >= start_hour && time.hour < end_hour
    end
  end
end
