module Api
  module Attendance
    class ExemptionsController < ApplicationController
      before_action :authenticate_session!
      before_action :set_attendance_exemption, only: [ :show, :update, :destroy ]

      api! "Lists attendance exemptions"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param_group :pagination_params
      param_group :filter_params
      param_group :sort_params
      param :exemption_type, String, desc: "Filter by exemption type (holiday, company_closure, emergency_closure, system_maintenance)"
      param :year, Integer, desc: "Filter by year"
      param :month, Integer, desc: "Filter by month"
      param :start_date, Date, desc: "Filter by date range (start)"
      param :end_date, Date, desc: "Filter by date range (end)"
      description <<-HTML
        <b>Endpoint Details</b>
        Lists attendance exemptions (holidays, company closures, etc.).
        Supports filtering by exemption type, date ranges, and other criteria.
        Requires permission: <code>:read, :attendance_exemption</code>.
      HTML
      returns code: 200, desc: "List of attendance exemptions"

      def index
        return unless authorize!(:read, :attendance_exemption)

        collection = ::Attendance::Exemption.all
        apply_filters(collection) do |filtered_exemptions|
          records, meta = paginate(filtered_exemptions)
          serialize_response(records, meta: meta)
        end
      end

      api! "Shows a specific attendance exemption"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the attendance exemption"
      description <<-HTML
        <b>Endpoint Details</b>
        Shows details for a specific attendance exemption.
        Requires permission: <code>:read, :attendance_exemption</code>.
      HTML
      returns code: 200, desc: "Attendance exemption details"
      error code: 404, desc: "Attendance exemption not found"

      def show
        return unless authorize!(:read, :attendance_exemption)

        serialize_response(@attendance_exemption)
      end

      api! "Creates a new attendance exemption"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :attendance_exemption, Hash, required: true do
        param :name, String, required: true, desc: "Name of the exemption"
        param :start_date, Date, required: true, desc: "Start date"
        param :end_date, Date, required: true, desc: "End date"
        param :exemption_type, String, required: true, desc: "Type of exemption"
        param :description, String, desc: "Description"
        param :affects_salary, [ true, false ], desc: "Whether this affects salary calculations"
        param :affects_attendance, [ true, false ], desc: "Whether this affects attendance calculations"
      end
      returns code: 201, desc: "Attendance exemption created successfully"

      def create
        return unless authorize!(:create, :attendance_exemption)

        @attendance_exemption = ::Attendance::Exemption.new(exemption_params)

        if @attendance_exemption.save
          trigger_recalculation_if_needed
          serialize_response(@attendance_exemption, status: :created)
        else
          serialize_errors(@attendance_exemption.errors)
        end
      end

      api! "Updates an attendance exemption"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the exemption"
      returns code: 200, desc: "Attendance exemption updated successfully"

      def update
        return unless authorize!(:update, :attendance_exemption)

        if @attendance_exemption.update(exemption_params)
          trigger_recalculation_if_needed
          serialize_response(@attendance_exemption)
        else
          serialize_errors(@attendance_exemption.errors)
        end
      end

      api! "Deletes an attendance exemption"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the exemption"
      returns code: 204, desc: "Attendance exemption deleted successfully"

      def destroy
        return unless authorize!(:destroy, :attendance_exemption)

        if @attendance_exemption.destroy
          trigger_recalculation_if_needed
          head :no_content
        else
          serialize_errors(@attendance_exemption.errors)
        end
      end

      api! "Calendar view of exemptions"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :year, Integer, desc: "Year to display (default: current year)"
      param :month, Integer, desc: "Month to display (optional)"
      param :exemption_type, String, desc: "Filter by exemption type (optional)"
      description <<-HTML
        <b>Endpoint Details</b>
        Returns exemptions in calendar format for the specified year/month.
        Can be filtered by exemption type for specific views.
        Requires permission: <code>:read, :attendance_exemption</code>.
      HTML
      returns code: 200, desc: "Calendar view of exemptions"

      def calendar
        return unless authorize!(:read, :attendance_exemption)

        year = params[:year]&.to_i || Date.current.year
        start_date, end_date = calculate_date_range(year, params[:month])

        exemptions = ::Attendance::Exemption.active.for_date_range(start_date, end_date)

        # Apply exemption type filter if provided
        if params[:exemption_type].present?
          exemptions = exemptions.where(exemption_type: params[:exemption_type])
        end

        exemptions = exemptions.order(:start_date)

        serialize_response(exemptions, meta: {
          year: year,
          month: params[:month],
          exemption_type: params[:exemption_type],
          view_type: 'calendar'
        })
      end

      private

      def set_attendance_exemption
        @attendance_exemption = ::Attendance::Exemption.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        serialize_errors({ detail: "Attendance exemption not found" }, :not_found)
      end

      def exemption_params
        params.require(:attendance_exemption).permit(
          :name, :start_date, :end_date, :exemption_type,
          :description, :is_active, :affects_salary, :affects_attendance
        )
      end

      def calculate_date_range(year, month)
        if month.present?
          month = month.to_i
          start_date = Date.new(year, month, 1)
          end_date = start_date.end_of_month
        else
          start_date = Date.new(year, 1, 1)
          end_date = Date.new(year, 12, 31)
        end

        [ start_date, end_date ]
      end

      def calculate_date_range(year, month)
        if month.present?
          month = month.to_i
          start_date = Date.new(year, month, 1)
          end_date = start_date.end_of_month
        else
          start_date = Date.new(year, 1, 1)
          end_date = Date.new(year, 12, 31)
        end

        [ start_date, end_date ]
      end

      def trigger_recalculation_if_needed
        if @attendance_exemption.affects_attendance?
          # Clear the working days cache
          ::Attendance::WorkingDaysService.clear_cache!

          # Queue background job to recalculate affected periods if worker exists
          if defined?(::Attendance::BatchPeriodCalculationWorker)
            ::Attendance::BatchPeriodCalculationWorker.perform_async(
              @attendance_exemption.start_date.to_s,
              @attendance_exemption.end_date.to_s
            )
          end
        end
      end
    end
  end
end
