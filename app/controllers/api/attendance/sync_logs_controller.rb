module Api
  module Attendance
    class SyncLogsController < ApplicationController
      before_action :authenticate_session!
      before_action :set_attendance_sync_log, only: [:show]
      before_action :set_collection, only: [:index]
      before_action :authorize_read, only: [:index, :show, :statistics, :recent_failures]
      before_action :authorize_cleanup, only: [:cleanup]

      api! "Lists attendance sync logs"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param_group :pagination_params
      param_group :filter_params
      param_group :sort_params
      param :device_id, Integer, desc: "Filter by device ID"
      param :status, String, desc: "Filter by status (pending, running, success, failed, partial)"
      param :sync_type, String, desc: "Filter by sync type (manual, scheduled, real_time, retry)"
      param :start_date, String, desc: "Filter logs from this date (YYYY-MM-DD)"
      param :end_date, String, desc: "Filter logs until this date (YYYY-MM-DD)"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Lists attendance sync logs with filtering options.
        Requires permission: <code>:read, :attendance_sync_log</code>.
      HTML
      )
      returns code: 200, desc: "List of sync logs"

      def index
        apply_filters(@collection) do |filtered_and_sorted|
          records, meta = paginate(filtered_and_sorted)
          serialize_response(records, meta: meta, serializer: ::Attendance::SyncLogSerializer)
        end
      end

      api! "Shows a specific sync log"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the sync log"
      param_group :include_params
      param_group :fields_params
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Shows details for a specific sync log.
        Requires permission: <code>:read, :attendance_sync_log</code>.
      HTML
      )
      returns code: 200, desc: "Sync log details"
      error code: 404, desc: "Sync log not found"

      def show
        serialize_response(@attendance_sync_log, serializer: ::Attendance::SyncLogSerializer)
      end

      api! "Cleans up old sync logs"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :older_than_days, Integer, desc: "Delete logs older than X days (default: 90)"
      param :keep_failed_logs, [true, false], desc: "Keep failed logs even if old (default: true)"
      param :dry_run, [true, false], desc: "Show what would be deleted without actually deleting (default: false)"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Cleans up old sync logs to free up storage space.
        Requires permission: <code>:cleanup, :attendance_sync_log</code>.
      HTML
      )
      returns code: 200, desc: "Cleanup results"

      def cleanup
        older_than_days = params[:older_than_days]&.to_i || 90
        keep_failed_logs = params[:keep_failed_logs] != 'false'
        dry_run = params[:dry_run] == 'true'

        cutoff_date = older_than_days.days.ago

        # Build query for logs to delete
        logs_to_delete = ::Attendance::SyncLog.where('created_at < ?', cutoff_date)
        logs_to_delete = logs_to_delete.where.not(status: :failed) if keep_failed_logs

        if dry_run
          # Just count what would be deleted
          count_by_status = logs_to_delete.group(:status).count
          total_count = logs_to_delete.count

          render json: {
            dry_run: true,
            cutoff_date: cutoff_date.iso8601,
            logs_that_would_be_deleted: total_count,
            breakdown_by_status: count_by_status,
            keep_failed_logs: keep_failed_logs,
            message: "#{total_count} logs would be deleted"
          }
        else
          # Actually delete the logs
          count_by_status = logs_to_delete.group(:status).count
          total_count = logs_to_delete.count

          logs_to_delete.delete_all

          render json: {
            dry_run: false,
            cutoff_date: cutoff_date.iso8601,
            logs_deleted: total_count,
            breakdown_by_status: count_by_status,
            keep_failed_logs: keep_failed_logs,
            message: "#{total_count} logs deleted successfully",
            cleaned_up_at: Time.current.iso8601
          }
        end
      end

      api! "Gets sync logs statistics"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :device_id, Integer, desc: "Filter by device ID"
      param :days, Integer, desc: "Number of days to include in statistics (default: 30)"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Generates statistics for sync logs over a specified period.
        Requires permission: <code>:read, :attendance_sync_log</code>.
      HTML
      )
      returns code: 200, desc: "Sync logs statistics"

      def statistics
        days = params[:days]&.to_i || 30
        start_date = days.days.ago

        sync_logs = ::Attendance::SyncLog.where('created_at >= ?', start_date)
        sync_logs = sync_logs.where(attendance_device_id: params[:device_id]) if params[:device_id].present?

        statistics = {
          period: {
            start_date: start_date.to_date.iso8601,
            end_date: Date.current.iso8601,
            days: days
          },
          total_syncs: sync_logs.count,
          status_breakdown: sync_logs.group(:status).count,
          sync_type_breakdown: sync_logs.group(:sync_type).count,
          success_rate: calculate_success_rate(sync_logs),
          average_duration: calculate_average_duration(sync_logs),
          total_records_processed: sync_logs.sum { |log| log.result_summary['total_processed'] || 0 },
          total_records_imported: sync_logs.sum { |log| log.result_summary['success'] || 0 },
          device_breakdown: sync_logs.joins(:attendance_device)
                                    .group('attendance_devices.name')
                                    .count,
          generated_at: Time.current.iso8601
        }

        render json: statistics
      end

      api! "Gets recent failed sync logs"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :limit, Integer, desc: "Number of recent failures to return (default: 10, max: 50)"
      param :device_id, Integer, desc: "Filter by device ID"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Returns recent failed sync logs for troubleshooting.
        Requires permission: <code>:read, :attendance_sync_log</code>.
      HTML
      )
      returns code: 200, desc: "List of recent failures"

      def recent_failures
        limit = [params[:limit]&.to_i || 10, 50].min

        failed_logs = ::Attendance::SyncLog.includes(:device)
                                      .where(status: :failed)
                                      .order(created_at: :desc)
                                      .limit(limit)

        failed_logs = failed_logs.where(attendance_device_id: params[:device_id]) if params[:device_id].present?

        serialize_response(failed_logs, serializer: ::Attendance::SyncLogSerializer)
      end

      private

      def set_attendance_sync_log
        @attendance_sync_log = ::Attendance::SyncLog.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        serialize_errors({ detail: "Sync log not found" }, :not_found)
      end

      def set_collection
        @collection = ::Attendance::SyncLog.includes(:device)
      end

      def calculate_success_rate(sync_logs)
        total = sync_logs.count
        return 0 if total.zero?

        successful = sync_logs.where(status: [:success, :partial]).count
        ((successful.to_f / total) * 100).round(2)
      end

      def calculate_average_duration(sync_logs)
        completed_logs = sync_logs.where.not(completed_at: nil)
        return 0 if completed_logs.empty?

        total_duration = completed_logs.sum do |log|
          next 0 unless log.started_at && log.completed_at
          (log.completed_at - log.started_at).to_i
        end

        (total_duration.to_f / completed_logs.count).round(2)
      end

      # Authorization methods
      def authorize_read
        authorize!(:read, :attendance_sync_log)
      end

      def authorize_cleanup
        authorize!(:cleanup, :attendance_sync_log)
      end
    end
  end
end
