# frozen_string_literal: true

module Api
  module Attendance
    class ReportsController < ApplicationController
      include AtharAuth::ResourceAuthorization

      before_action :authenticate_session!
      before_action :set_employee
      before_action :authorize_access!
      before_action :validate_date_parameters

      # GET /api/attendance/reports/timesheet
      # Parameters:
      #   - employee_id (required): ID of the employee
      #   - year (required): Year for the report
      #   - month (required): Month for the report (1-12)
      #   - format (optional): 'pdf' or 'html' (default: 'pdf')
      def timesheet
        service = ::Attendance::TimesheetReportService.new(@employee, @year, @month)
        
        case params[:format]&.downcase
        when 'html'
          render_html_preview(service)
        else
          render_pdf_download(service)
        end
      rescue TimesheetGenerationError => e
        Rails.logger.error "Timesheet generation error: #{e.message}"
        serialize_errors({ detail: e.message }, :unprocessable_entity)
      rescue ArgumentError => e
        Rails.logger.error "Invalid timesheet parameters: #{e.message}"
        serialize_errors({ detail: "Invalid parameters: #{e.message}" }, :bad_request)
      rescue => e
        Rails.logger.error "Unexpected error generating timesheet: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
        serialize_errors({ detail: "An unexpected error occurred while generating the timesheet" }, :internal_server_error)
      end

      private

      # Set employee from parameters
      def set_employee
        @employee = Employee.find(params[:employee_id])
      rescue ActiveRecord::RecordNotFound
        serialize_errors({ detail: "Employee not found" }, :not_found)
      end

      # Validate and set date parameters
      def validate_date_parameters
        @year = params[:year]&.to_i
        @month = params[:month]&.to_i

        errors = []
        errors << "Year is required" unless @year.present?
        errors << "Month is required" unless @month.present?
        errors << "Invalid year (must be between 2020-2030)" unless @year && (2020..2030).include?(@year)
        errors << "Invalid month (must be between 1-12)" unless @month && (1..12).include?(@month)

        if errors.any?
          serialize_errors({ detail: errors.join(', ') }, :bad_request)
          return
        end

        # Check if requesting future date
        requested_date = Date.new(@year, @month, 1)
        if requested_date > Date.current.beginning_of_month
          serialize_errors({ detail: "Cannot generate reports for future dates" }, :bad_request)
        end
      end

      # Check if current user can access employee reports
      def authorize_access!
        unless can_access_employee_reports?(@employee)
          render_forbidden("You are not authorized to access this employee's reports")
        end
      end

      # Authorization logic
      def can_access_employee_reports?(employee)
        # Check if user can read attendance data
        return true if can?(:read, :attendance_event) || can?(:manage, :attendance_event)

        # Check if user can read their own attendance data
        return true if can?(:read_own, :attendance_event) && employee.id == current_employee&.id

        false
      end

      # Render HTML preview
      def render_html_preview(service)
        html_content = service.generate_html
        render html: html_content.html_safe
      rescue => e
        Rails.logger.error "HTML preview generation failed: #{e.message}"
        serialize_errors({ detail: "Failed to generate HTML preview" }, :unprocessable_entity)
      end

      # Render PDF download
      def render_pdf_download(service)
        pdf_content = service.generate_pdf
        
        send_data pdf_content,
                  filename: timesheet_filename,
                  type: 'application/pdf',
                  disposition: 'attachment'
      rescue => e
        Rails.logger.error "PDF generation failed: #{e.message}"
        serialize_errors({ detail: "Failed to generate PDF report" }, :unprocessable_entity)
      end

      # Generate filename for the timesheet PDF
      def timesheet_filename
        employee_name = @employee.name.parameterize
        period = "#{@year}-#{@month.to_s.rjust(2, '0')}"
        "timesheet_#{employee_name}_#{period}.pdf"
      end

      # Log timesheet generation for audit purposes
      def log_timesheet_generation
        Rails.logger.info "Timesheet generated for employee #{@employee.id} (#{@employee.name}), period #{@year}-#{@month}, by user #{current_user.id}"
      end

      # Render forbidden error
      def render_forbidden(message)
        serialize_errors({ detail: message }, :forbidden)
      end
    end
  end
end
