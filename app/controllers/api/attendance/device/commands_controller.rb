# frozen_string_literal: true

module Api
  module Attendance
    module Device
      class CommandsController < ApplicationController
        before_action :authenticate_session!
        before_action :set_device
        before_action :authorize_read, only: [ :index, :history ]
        before_action :authorize_execute, only: [ :execute ]
        before_action :check_device_command_support, only: [ :index, :execute ]

        api! "Lists available commands for a device"
        header "Authorization", "Scoped session token as Bearer token", required: true
        param :device_id, Integer, required: true, desc: "ID of the attendance device"
        description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Returns a list of commands available for the specified device.
        Commands vary by device type and adapter capabilities.
        Requires permission: <code>:read, :attendance_device</code>.
        HTML
        )
        returns code: 200, desc: "List of available commands"
        error code: 404, desc: "Device not found"

        def index
          commands = ::Attendance::DeviceCommand.for_device(@device)
          serialize_response(commands, serializer: ::Attendance::DeviceCommandSerializer)
        end

        api! "Executes a command on a device"
        header "Authorization", "Scoped session token as Bearer token", required: true
        param :device_id, Integer, required: true, desc: "ID of the attendance device"
        param :command, Hash, required: true, desc: "Command execution data" do
          param :id, String, required: true, desc: "Command name to execute (e.g., 'test_voice', 'unlock')"
          param :parameters, Hash, required: false, desc: "Command-specific parameters"
        end
        description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Executes a command on the specified device.
        Commands are executed synchronously and return immediate results.
        All command executions are logged for audit purposes.
        Requires permission: <code>:execute_commands, :attendance_device</code>.
        HTML
        )
        returns code: 200, desc: "Command execution result"
        error code: 400, desc: "Invalid command or parameters"
        error code: 404, desc: "Device not found"

        def execute
          command_data = command_params
          command_name = command_data[:id]

          # Validate command
          validation = ::Attendance::DeviceCommand.validate_for_device(
            @device,
            command_name,
            command_data[:parameters] || {}
          )

          unless validation[:valid]
            serialize_errors({ detail: validation[:error] }, :bad_request)
            return
          end

          # Execute command
          result = ::Attendance::DeviceCommand.execute_for_device(
            @device,
            command_name,
            command_data[:parameters] || {},
            current_employee
          )

          status_code = result.success ? :created : :unprocessable_entity
          render json: result.as_json, status: status_code
        end

        api! "Gets command execution history for a device"
        header "Authorization", "Scoped session token as Bearer token", required: true
        param :device_id, Integer, required: true, desc: "ID of the attendance device"
        param_group :pagination_params
        param_group :filter_params
        param_group :sort_params
        param_group :include_params
        param_group :fields_params
        description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Returns the command execution history for the specified device.
        Supports filtering, sorting, and pagination following JSON:API standards.
        Requires permission: <code>:read, :attendance_device</code>.

        <b>Filtering Examples:</b>
        <code>GET /api/attendance/devices/1/commands/history?filter[command_name]=test_voice</code>
        <code>GET /api/attendance/devices/1/commands/history?filter[status]=completed</code>

        <b>Sorting Examples:</b>
        <code>GET /api/attendance/devices/1/commands/history?sort=-started_at</code>
        <code>GET /api/attendance/devices/1/commands/history?sort=command_name,started_at</code>
        HTML
        )
        returns code: 200, desc: "Command execution history"
        error code: 404, desc: "Device not found"

        def history
          # Set up collection for filtering and sorting
          @collection = @device.command_executions.includes(:executed_by, :device)

          apply_filters(@collection) do |filtered_and_sorted|
            records, meta = paginate(filtered_and_sorted)

            serialize_response(records, serializer: ::Attendance::CommandExecutionSerializer, meta: meta)
          end
        end

        private

        def set_device
          @device = ::Attendance::Device.find(params[:device_id])
        rescue ActiveRecord::RecordNotFound
          serialize_errors({ detail: "Attendance device not found" }, :not_found)
        end

        def check_device_command_support
          adapter = @device.create_adapter

          unless adapter.supports_commands?
            serialize_errors({
                               detail: "This device does not support commands",
                               device_type: @device.adapter_type
                             }, :unprocessable_entity)
          end
        end

        def command_params
          params.require(:command).permit(:id, parameters: {})
        end

        # Authorization methods following API standards
        def authorize_read
          unless can?(:read, :attendance_device)
            render_forbidden("You don't have permission to view device commands")
            false
          end
        end

        def authorize_execute
          unless can?(:execute_commands, :attendance_device)
            render_forbidden("You don't have permission to execute device commands")
            false
          end
        end

        def render_forbidden(message)
          serialize_errors({ detail: message }, :forbidden)
        end

        # Filtering and sorting configuration
        def filterable_fields
          %w[command_name status started_at completed_at]
        end

        def sortable_fields
          %w[command_name status started_at completed_at duration]
        end

        def default_sort
          '-started_at' # Most recent first
        end

        def searchable_fields
          %w[command_name]
        end
      end
    end
  end
end
