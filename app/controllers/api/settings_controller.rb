module Api
  class SettingsController < ApplicationController
    before_action :authenticate_session!
    before_action :set_setting_by_logical_key, only: [ :show, :update ]

    api! "Lists settings"
    header "Authorization", "Scoped session token as Bearer token", required: true
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Lists settings wchith support for filtering, sorting, and pagination.
      Use <code>filter[namespace_eq]=attendance</code> to filter by namespace.
      Requires permission: <code>:read, :setting</code>.
    HTML
    )
    returns code: 200, desc: "List of settings"

    def index
      return unless authorize!(:read, :setting)

      apply_filters(Setting.all) do |filtered_settings|
        records, meta = paginate(filtered_settings)
        serialize_response(records, meta: meta)
      end
    end

    api! "Shows a specific setting"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :namespace, String, required: true, desc: "Setting namespace"
    param :key, String, required: true, desc: "Setting key"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Shows details for a specific setting by namespace and key.
      Requires permission: <code>:read, :setting</code>.
    HTML
    )
    returns code: 200, desc: "Setting details"

    def show
      return unless authorize!(:read, :setting)
      serialize_response(@setting)
    end

    api! "Updates a setting"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :namespace, String, required: true, desc: "Setting namespace"
    param :key, String, required: true, desc: "Setting key"
    param :setting, Hash, required: true, desc: "Setting attributes" do
      # param :value, Object, desc: "Setting value in native type (string, integer, boolean, etc.)"
      param :description, String, desc: "Setting description"
    end
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Updates an existing setting by namespace and key.
      Requires permission: <code>:update, :setting</code>.
      Note: Only editable settings can be updated.
      Note: setting_type and is_editable cannot be changed via update for security reasons.
    HTML
    )
    returns code: 200, desc: "Setting updated successfully"

    def update
      return unless authorize!(:update, :setting)

      unless @setting.is_editable
        return serialize_errors({ detail: "This setting cannot be edited" }, :unprocessable_entity)
      end

      # DEBUG: Log what we're trying to update
      Rails.logger.info "=== DEBUGGING UPDATE ==="
      Rails.logger.info "Raw params: #{params.inspect}"
      Rails.logger.info "Update params: #{setting_update_params.inspect}"
      Rails.logger.info "Setting before: #{@setting.attributes.inspect}"
      Rails.logger.info "========================="

      if @setting.update(setting_update_params)
        trigger_attendance_recalculation if attendance_setting?
        serialize_response(@setting)
      else
        Rails.logger.error "Update failed: #{@setting.errors.full_messages}"
        serialize_errors(@setting.errors)
      end
    end

    private

    def set_setting_by_logical_key
      @setting = Setting.find_by_logical_key_safe(params[:namespace], params[:key])
      unless @setting
        serialize_errors({ detail: "Setting not found" }, :not_found)
        false
      end
    end

    def setting_update_params
      # Only allow value and description to be updated
      # setting_type and is_editable cannot be changed for security
      params.require(:setting).permit(:value, :description)
    end

    def attendance_setting?
      params[:namespace] == 'attendance' &&
        %w[work_start_time work_end_time duplicate_threshold_seconds].include?(params[:key])
    end

    def trigger_attendance_recalculation
      Attendance::BatchPeriodCalculationWorker.perform_async(
        30.days.ago.to_date.to_s,
        Date.today.to_s
      )
    end
  end
end
