module Api
  module Employees
    class UserRolesController < ApplicationController
      before_action :authenticate_session!
      before_action :set_employee
      before_action :set_user_role, only: [ :show, :destroy, :set_default ]
      before_action :authorize_read, only: [ :index, :show ]
      before_action :authorize_update, only: [ :create, :destroy, :set_default ]

      api! "Lists all user roles for an employee"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, required: true, desc: "ID of the employee"
      param_group :pagination_params
      param_group :filter_params
      param_group :sort_params
      param_group :include_params
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Lists all user roles assigned to the specified employee.
        Supports filtering by role_id, project_id, and is_default.
        Supports sorting with sort=field or sort=-field (descending).
        Supports pagination with page[number] and page[size].
        Requires permission: <code>:read, :employee</code>.
      HTML
      )
      returns code: 200, desc: "List of user roles"

      def index
        apply_filters(@employee.user_roles_list) do |filtered_and_sorted|
          records, meta = paginate(filtered_and_sorted)
          serialize_response(records, meta: meta)
        end
      end

      api! "Shows a specific user role for an employee"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, required: true, desc: "ID of the employee"
      param :id, String, required: true, desc: "ID of the user role"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Shows details for a specific user role assigned to the employee.
        Requires permission: <code>:read, :employee</code>.
      HTML
      )
      returns code: 200, desc: "User role details"
      error code: 404, desc: "User role not found"

      def show
        if @user_role
          serialize_response(@user_role, serializer: ::Employees::UserRoleSerializer)
        else
          serialize_errors({ detail: "User role not found" }, :not_found)
        end
      end

      api! "Assigns a user role to an employee"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, required: true, desc: "ID of the employee"
      param :user_role, Hash, required: true, desc: "User role details" do
        param :role_id, String, required: true, desc: "ID of the role to assign"
        param :project_id, String, desc: "ID of the project (required for non-global roles)"
        param :is_default, :boolean, desc: "Whether this role should be the default role"
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Assigns a new user role to the employee.
        For project-specific roles, a project_id must be provided.
        For global roles, project_id is not required.
        Requires permission: <code>:update, :employee</code>.
      HTML
      )
      returns code: 201, desc: "User role assigned successfully"
      error code: 422, desc: "Validation errors"

      def create
        # Initialize user_roles_list if it's nil
        @employee.user_roles_list

        # Check if this will be the first role for the employee
        is_first_role = @employee.user_roles_list.empty?

        # Add the new role
        @employee.add_role(user_role_params)

        # If this is the first role and no default is explicitly set, make it default
        if is_first_role && @employee.user_roles_list.any?
          new_role = @employee.user_roles_list.last
          unless new_role.is_default == true
            new_role.is_default = true
            Rails.logger.info "Auto-assigned first role as default for employee: #{@employee.email}"
          end
        elsif @employee.user_roles_list.any?
          # Check for multiple defaults and fix if needed
          default_roles = @employee.user_roles_list.select { |role| role.is_default == true }
          if default_roles.size > 1
            # Multiple defaults found, keep only the first one
            first_default_found = false
            @employee.user_roles_list.each do |role|
              if role.is_default == true
                if first_default_found
                  role.is_default = false
                else
                  first_default_found = true
                end
              end
            end
            Rails.logger.info "Fixed multiple default roles for employee: #{@employee.email}"
          end
        end

        if @employee.save
          # Find the newly added role to return it
          new_role = @employee.user_roles_list.last
          serialize_response(new_role, status: :created, serializer: ::Employees::UserRoleSerializer)
        else
          serialize_errors(@employee.errors)
        end
      end

      api! "Removes a user role from an employee"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, required: true, desc: "ID of the employee"
      param :id, String, required: true, desc: "ID of the user role"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Removes a user role from the employee.
        Requires permission: <code>:update, :employee</code>.
      HTML
      )
      returns code: 204, desc: "User role removed successfully"
      error code: 404, desc: "User role not found"

      def destroy
        if @user_role.nil?
          return serialize_errors({ detail: "User role not found" }, :not_found)
        end

        # Get the current roles
        current_roles = @employee.user_roles_list.to_a

        # Remove the role
        updated_roles = current_roles.reject { |r| r == @user_role }
        @employee.user_roles_list = updated_roles

        if @employee.save
          head :no_content
        else
          serialize_errors(@employee.errors)
        end
      end

      api! "Sets a user role as the default role for an employee"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, required: true, desc: "ID of the employee"
      param :id, String, required: true, desc: "ID of the user role to set as default"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Sets the specified user role as the default role for the employee.
        This will unset any previously default role.
        Requires permission: <code>:update, :employee</code>.
      HTML
      )
      returns code: 200, desc: "Default role updated successfully"
      error code: 404, desc: "User role not found"

      def set_default
        if @user_role.nil?
          return serialize_errors({ detail: "User role not found" }, :not_found)
        end

        if @employee.set_default_role(@user_role.id)
          if @employee.save
            serialize_response(@user_role)
          else
            serialize_errors(@employee.errors)
          end
        else
          serialize_errors({ detail: "Failed to set default role" }, :unprocessable_entity)
        end
      end

      private

      def set_employee
        @employee = Employee.find(params[:employee_id])
      rescue ActiveRecord::RecordNotFound
        serialize_errors({ detail: "Employee not found" }, :not_found)
        false
      end

      def set_user_role
        # Return early if user_roles_list is nil
        if @employee.user_roles_list.nil?
          @user_role = nil
          return
        end

        # Get the role ID from the params
        role_id = params[:id]

        # Find the role by ID
        @user_role = @employee.user_roles_list.find { |r| r.id.to_s == role_id.to_s }
      end

      def user_role_params
        params.require(:user_role).permit(:role_id, :project_id, :is_default)
      end

      def authorize_read
        unless can?(:read, :employee)
          render_forbidden("You don't have permission to view employee user roles")
          false
        end
      end

      def authorize_update
        unless can?(:update, :employee)
          render_forbidden("You don't have permission to modify employee user roles")
          false
        end
      end

      def render_forbidden(message)
        serialize_errors({ detail: message }, :forbidden)
      end
    end
  end
end
