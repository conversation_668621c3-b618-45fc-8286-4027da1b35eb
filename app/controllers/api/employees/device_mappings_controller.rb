# frozen_string_literal: true

module Api
  module Employees
    class DeviceMappingsController < Athar::Commons::Api::BaseController
      include Athar::Commons::Api::Concerns::Serializable
      include Athar::Commons::Api::Concerns::Paginatable
      include Athar::Commons::Api::Concerns::FilterableSortable

      before_action :authenticate_session!
      before_action :set_employee
      before_action :load_device_mappings_collection, only: [:index]
      before_action :set_mapping, only: [:show, :update, :destroy]
      before_action :authorize_read, only: [:index, :show]
      before_action :authorize_create, only: [:create]
      before_action :authorize_update, only: [:update]
      before_action :authorize_destroy, only: [:destroy]

      api! "Lists device mappings for a specific employee"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, required: true, desc: "Employee ID"
      param_group :pagination_params
      param_group :filter_params
      param_group :sort_params
      param_group :include_params
      param_group :fields_params
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Returns a list of device mappings for the specified employee.
        Shows which devices the employee is mapped to and their device user IDs.
        Supports filtering, sorting, and pagination.
        Requires permission: <code>:read, :employee</code>.
      HTML
      )
      returns code: 200, desc: "List of employee device mappings"
      error code: 404, desc: "Employee not found"

      def index
        apply_filters(@collection) do |filtered_and_sorted|
          records, meta = paginate(filtered_and_sorted)

          serialize_response(records, serializer: ::Attendance::EmployeeDeviceMappingSerializer, meta: meta)
        end
      end

      api! "Shows a specific device mapping for an employee"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, required: true, desc: "Employee ID"
      param :id, Integer, required: true, desc: "Mapping ID"
      param_group :include_params
      param_group :fields_params
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Shows details for a specific device mapping for the employee.
        Requires permission: <code>:read, :employee</code>.
      HTML
      )
      returns code: 200, desc: "Device mapping details"
      error code: 404, desc: "Mapping not found"

      def show
        serialize_response(@mapping, serializer: ::Attendance::EmployeeDeviceMappingSerializer)
      end

      api! "Creates a new device mapping for an employee"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, required: true, desc: "Employee ID"
      param :employee_device_mapping, Hash, required: true, desc: "Mapping attributes" do
        param :attendance_device_id, Integer, required: true, desc: "Device ID"
        param :device_user_id, String, required: true, desc: "Device user ID"
        param :notes, String, desc: "Optional notes"
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Creates a new device mapping for the specified employee.
        Maps the employee to a specific device user ID on the given device.
        Requires permission: <code>:create, :employee</code>.
      HTML
      )
      returns code: 201, desc: "Mapping created successfully"
      error code: 422, desc: "Validation errors"

      def create
        @mapping = @employee.employee_device_mappings.build(mapping_params)

        if @mapping.save
          serialize_response(@mapping, status: :created, serializer: ::Attendance::EmployeeDeviceMappingSerializer)
        else
          serialize_errors(@mapping.errors)
        end
      end

      api! "Updates a device mapping for an employee"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, required: true, desc: "Employee ID"
      param :id, Integer, required: true, desc: "Mapping ID"
      param :employee_device_mapping, Hash, required: true, desc: "Mapping attributes" do
        param :device_user_id, String, desc: "Device user ID"
        param :notes, String, desc: "Optional notes"
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Updates an existing device mapping for the employee.
        Note: Device cannot be changed - create a new mapping instead.
        Requires permission: <code>:update, :employee</code>.
      HTML
      )
      returns code: 200, desc: "Mapping updated successfully"
      error code: 422, desc: "Validation errors"

      def update
        if @mapping.update(update_mapping_params)
          serialize_response(@mapping, serializer: ::Attendance::EmployeeDeviceMappingSerializer)
        else
          serialize_errors(@mapping.errors)
        end
      end

      api! "Deletes a device mapping for an employee"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, required: true, desc: "Employee ID"
      param :id, Integer, required: true, desc: "Mapping ID"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Deletes a device mapping for the specified employee.
        This will remove the association between the employee and the device.
        Requires permission: <code>:destroy, :employee</code>.
      HTML
      )
      returns code: 204, desc: "Mapping deleted successfully"

      def destroy
        @mapping.destroy!
        head :no_content
      end

      private

      def set_employee
        @employee = Employee.find(params[:employee_id])
      rescue ActiveRecord::RecordNotFound
        serialize_errors({ detail: "Employee not found" }, :not_found)
      end

      def load_device_mappings_collection
        @collection = @employee.employee_device_mappings.with_device_data
      end

      def set_mapping
        @mapping = @employee.employee_device_mappings.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        serialize_errors({ detail: "Mapping not found" }, :not_found)
      end

      def mapping_params
        params.require(:employee_device_mapping).permit(:attendance_device_id, :device_user_id, :notes)
      end

      def update_mapping_params
        params.require(:employee_device_mapping).permit(:device_user_id, :notes)
      end

      # Authorization methods
      def authorize_read
        authorize!(:read, :employee)
      end

      def authorize_create
        authorize!(:create, :employee)
      end

      def authorize_update
        authorize!(:update, :employee)
      end

      def authorize_destroy
        authorize!(:destroy, :employee)
      end

      # Filtering and sorting configuration
      def filterable_fields
        %w[attendance_device_id device_user_id notes created_at updated_at]
      end

      def sortable_fields
        %w[attendance_device_id device_user_id created_at updated_at]
      end

      def default_sort
        'created_at'
      end

      def searchable_fields
        %w[device_user_id notes]
      end
    end
  end
end
