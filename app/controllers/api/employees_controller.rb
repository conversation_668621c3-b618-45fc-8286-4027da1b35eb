module Api
  class EmployeesController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!

    authorize_resources

    api! "Lists all employees"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param_group :pagination_params
    param_group :filter_params
    param_group :sort_params
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns a list of all employees.
      Supports filtering, sorting, and pagination.
      Requires permission: <code>:read, :employee</code>.
    HTML
    )
    returns code: 200, desc: "List of employees"

    def index
      apply_filters(@employees) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records.with_user_data, meta: meta)
      end
    end

    api! "Retrieves a specific employee"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the employee"
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Fetches detailed information about a specific employee by ID.
      Requires permission: <code>:read, :employee</code>.
    HTML
    )
    returns code: 200, desc: "Employee details"

    def show
      serialize_response(@employee, params: { current_employee: current_employee })
    end

    api! "Creates a new employee"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :employee, Hash, required: true, desc: "Employee details" do
      param :name, String, required: true, desc: "Full name of the employee"
      param :email, String, required: true, desc: "Email address of the employee"
      param :avatar, ActionDispatch::Http::UploadedFile, desc: "Employee profile picture"
      param :department, String, required: true, desc: "Department (admin, hr, finance, operations, it, programs, procurement)"
      param :start_date, Date, required: true, desc: "Employment start date"
      param :phone, String, required: true, desc: "Phone number"
      param :user_roles_list, Array, validator: EnhancedArrayValidator, desc: "Array of user roles to assign to the employee. Each role should include role_id and project_id (optional for global roles)."
      param "attachments[]", Array, of: ActionDispatch::Http::UploadedFile, desc: "Files to attach to the employee"
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a new employee record.
      Supports uploading an avatar image for the employee.
      Supports attaching multiple files to the employee.
      Supports assigning user roles to the employee.
      Requires permission: <code>:create, :employee</code>.
    HTML
    )
    returns code: 201, desc: "Employee created successfully"
    error code: 422, desc: "Validation errors"

    def create
      @employee.assign_attributes(create_params)

      # Ensure first user role is set as default if no default is explicitly set
      ensure_first_role_is_default(@employee)

      if @employee.save
        serialize_response(@employee, status: :created, include: [ "user_roles" ])
      else
        serialize_errors(@employee.errors)
      end
    end

    api :PUT, "/employees/:id", "Updates an existing employee"
    api :PATCH, "/employees/:id", "Partially updates an employee"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the employee"
    param :employee, Hash, required: true, desc: "Updated employee fields" do
      param :name, String, desc: "Full name of the employee"
      param :email, String, desc: "Email address of the employee"
      param :avatar, ActionDispatch::Http::UploadedFile, desc: "Employee profile picture"
      param :department, String, desc: "Department (admin, hr, finance, operations, it, programs, procurement)"
      param :start_date, Date, desc: "Employment start date"
      param :phone, String, desc: "Phone number"
      param :user_roles_list, Array, validator: EnhancedArrayValidator, desc: "Array of user roles to assign to the employee. Each role should include role_id and project_id (optional for global roles)."
      param "attachments[]", Array, of: ActionDispatch::Http::UploadedFile, desc: "Files to attach to the employee"
      # Note: Password cannot be updated through this endpoint
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Updates an existing employee's information.
      Supports uploading a new avatar image for the employee.
      Supports attaching multiple files to the employee.
      Supports updating user roles for the employee.
      At least one field should be provided in the employee parameter.
      Requires permission: <code>:update, :employee</code>.
    HTML
    )
    returns code: 200, desc: "Employee updated successfully"
    error code: 422, desc: "Validation errors"

    def update
      # Apply update params first
      @employee.assign_attributes(update_params)

      # Ensure first user role is set as default if no default is explicitly set
      ensure_first_role_is_default(@employee)

      if @employee.save
        serialize_response(@employee.reload)
      else
        serialize_errors(@employee.errors)
      end
    end

    api! "Deletes an employee"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the employee"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Deletes an employee by ID.
      Requires permission: <code>:destroy, :employee</code>.
    HTML
    )
    returns code: 204, desc: "Employee deleted successfully"

    def destroy
      @employee.destroy!
      head :no_content
    end

    private

    def create_params
      params.require(:employee).permit(
        :name,
        :email,
        :password,
        :avatar,
        :department,
        :start_date,
        :phone,
        user_roles_list: [ :role_id, :project_id, :is_default ],
        attachments: []
      )
    end

    def update_params
      params.fetch(:employee, {}).permit(
        :name,
        :email,
        :avatar,
        :department,
        :start_date,
        :phone,
        user_roles_list: [ :role_id, :project_id, :is_default ],
        attachments: []
      )
    end

    def ensure_first_role_is_default(employee)
      return unless employee.user_roles_list&.any?

      # Check if any role is already marked as default
      default_roles = employee.user_roles_list.select { |role| role.is_default == true }

      if default_roles.empty?
        # No default is set, make the first role default
        employee.user_roles_list.first.is_default = true
        Rails.logger.info "Auto-assigned first role as default for employee: #{employee.email}"
      elsif default_roles.size > 1
        # Multiple defaults found, keep only the first one and unset the rest
        employee.user_roles_list.each_with_index do |role, index|
          if role.is_default == true
            role.is_default = (index == employee.user_roles_list.find_index { |r| r.is_default == true })
          end
        end
        Rails.logger.info "Fixed multiple default roles for employee: #{employee.email}, kept first default"
      end
    end
  end
end
