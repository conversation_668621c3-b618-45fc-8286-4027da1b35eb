module Api
  module Finance
    class SalaryPackagesController < ApplicationController
      before_action :authenticate_session!
      before_action :apply_permission_filters, only: [ :index, :show, :update, :submit, :cancel ]
      before_action :set_collection, only: [ :index, :show, :update, :submit, :cancel ]
      before_action :set_salary_package, only: [ :show, :update, :submit, :cancel ]
      before_action :set_employee, only: [ :create ]
      before_action :authorize_index, only: [ :index ]
      before_action :authorize_create, only: [ :create ]
      before_action :authorize_update, only: [ :update ]
      before_action :authorize_submit, only: [ :submit ]
      before_action :authorize_cancel, only: [ :cancel ]
      before_action :authorize_show, only: [ :show ]

      api! "Lists salary packages"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, desc: "Filter by employee ID"
      param_group :pagination_params
      param_group :filter_params
      param_group :sort_params
      param_group :include_params
      description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Lists salary packages based on the user's permissions:
      — HR and Management with <code>:read, :salary_package</code> or <code>:manage_others, :salary_package</code> can see all salary packages
      — Employees with <code>:read_own, :salary_package</code> can only see their own salary packages

      Supports filtering by employee_id, base_salary, and effective_date.
      Supports sorting with sort=field or sort=-field (descending).
      Supports pagination with page[number] and page[size].
      HTML
      )
      returns code: 200, desc: "List of salary packages"

      def index
        apply_filters(@collection) do |filtered_and_sorted|
          records, meta = paginate(filtered_and_sorted)
          serialize_response(records, meta: meta)
        end
      end

      api! "Shows a specific salary package"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the salary package"
      param_group :include_params
      description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Shows details for a specific salary package including approval status and workflow information.
      Requires <code>:read, :salary_package</code> permission or <code>:read_own, :salary_package</code> for own packages.
      HTML
      )
      returns code: 200, desc: "Salary package details"
      error code: 404, desc: "Salary package not found"

      def show
        serialize_response(@salary_package)
      end

      api! "Creates a new salary package"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :salary_package, Hash, required: true, desc: "Salary package attributes" do
        param :employee_id, Integer, required: true, desc: "ID of the employee"
        param :base_salary, Float, required: true, desc: "Base salary amount"
        param :transportation_allowance, Float, required: false, desc: "Transportation allowance amount"
        param :other_allowances, Float, required: false, desc: "Other allowances amount"
        param :effective_date, Date, required: true, desc: "Date when the salary package becomes effective"
        param :notes, String, required: false, desc: "Additional notes"
      end
      description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a new salary package for an employee in draft status.

      Required permissions:
      — HR roles: <code>:create_others, :salary_package</code> or <code>:manage_others, :salary_package</code>
      — Financial managers: <code>:create, :salary_package</code>
      — Employees: Cannot create salary packages (contact HR)

      The salary package is created in draft status and must be submitted for approval using the submit endpoint.

      Business rules:
      - Users cannot create salary packages for themselves
      - Only one draft package per employee is allowed at a time
      - Draft packages can be edited before submission

      Use the <code>PATCH /api/finance/salary_packages/:id/submit</code> endpoint to submit the package for approval.
      HTML
      )
      returns code: 201, desc: "Salary package created successfully"
      error code: 422, desc: "Validation errors"

      def create
        begin
          SalaryPackage.transaction do
            # Create salary package as draft
            @salary_package = @employee.salary_packages.build(salary_package_params)
            @salary_package.status = :draft
            @salary_package.created_by = current_employee
            @salary_package.actor_user_id = current_user.id

            if @salary_package.save
              serialize_response(@salary_package, status: :created)
            else
              serialize_errors({ detail: @salary_package.errors.full_messages.join(", ") })
              raise ActiveRecord::Rollback
            end
          end
        rescue ActiveRecord::RecordInvalid => e
          serialize_errors({ detail: e.record.errors.full_messages.join(", ") })
        rescue => e
          Rails.logger.error("Error creating salary package: #{e.message}")
          serialize_errors({ detail: "Failed to create salary package: #{e.message}" }, :internal_server_error)
        end
      end

      api! "Updates a salary package"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the salary package"
      param :salary_package, Hash, required: true, desc: "Salary package attributes" do
        param :base_salary, Float, required: false, desc: "Base salary amount"
        param :transportation_allowance, Float, required: false, desc: "Transportation allowance amount"
        param :other_allowances, Float, required: false, desc: "Other allowances amount"
        param :effective_date, Date, required: false, desc: "Date when the salary package becomes effective"
        param :notes, String, required: false, desc: "Additional notes"
      end
      description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Updates a salary package in draft or rejected status.
      For submitting packages for approval, use the submit endpoint.

      Required permissions:
      - <code>:update, :salary_package</code>
      HTML
      )
      returns code: 200, desc: "Updated salary package"
      error code: 422, desc: "Validation errors"

      def update
        # Check if package is editable
        unless @salary_package.editable?
          return serialize_errors({ detail: "Package is not editable in current status" }, :unprocessable_entity)
        end

        # Check creator-specific editability
        unless @salary_package.editable_by?(current_employee) || can?(:manage, :salary_package)
          return serialize_errors({ detail: "You can only edit your own draft packages" }, :forbidden)
        end

        # Set the actor_user_id for approval tracking
        @salary_package.actor_user_id = current_user.id

        if @salary_package.update(salary_package_params)
          serialize_response(@salary_package)
        else
          serialize_errors({ detail: @salary_package.errors.full_messages.join(", ") }, :unprocessable_entity)
        end
      end

      api! "Submit a salary package for approval"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the salary package"
      description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Submits a salary package for approval workflow.
      Changes status from 'draft' to 'pending_approval' and triggers approval process.
      Requires permission: <code>:submit, :salary_package</code>.
      HTML
      )
      returns code: 200, desc: "Salary package submitted successfully"
      error code: 422, desc: "Validation errors"

      def submit
        # Set the actor_user_id for approval tracking
        @salary_package.actor_user_id = current_user.id

        @salary_package.submit!
        serialize_response(@salary_package)
      rescue ActiveRecord::RecordInvalid => e
        serialize_errors({ detail: e.message }, :unprocessable_entity)
      end

      api! "Cancels a draft salary package"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the salary package to cancel"
      param :reason, String, desc: "Optional reason for cancellation"
      description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Cancels a draft salary package. Only the creator of the package can cancel it.
      Admins with manage permissions can cancel any draft package.

      Requirements:
      - Package must be in draft status
      - User must be the creator of the package OR have manage permissions
      - Package cannot be cancelled if it's already submitted or approved

      Returns the updated package with cancelled status and audit information.
      HTML
      )
      returns code: 200, desc: "Package cancelled successfully"
      error code: 403, desc: "Not authorized to cancel this package"
      error code: 422, desc: "Package cannot be cancelled in its current state"

      def cancel
        # Check if package can be cancelled
        unless @salary_package.cancellable?
          return serialize_errors({ detail: "Package cannot be cancelled in its current state" }, :forbidden)
        end

        # Check authorization - only creator or admin can cancel
        unless @salary_package.cancellable_by?(current_employee) || can?(:manage, :salary_package)
          return serialize_errors({ detail: "You can only cancel your own draft packages" }, :forbidden)
        end

        reason = params[:reason] || "Manually cancelled by #{current_employee&.name || 'user'}"

        if @salary_package.cancel_draft!(reason)
          serialize_response(@salary_package)
        else
          serialize_errors({ detail: "Failed to cancel package" }, :unprocessable_entity)
        end
      end

      private

      def apply_permission_filters
        # Apply creator-specific permission filtering based on new business rules
        if can?(:manage, :salary_package)
          # Admin: can see everything including all drafts (creator-specific visibility)
          @collection = SalaryPackage.all
        elsif can?(:manage_others, :salary_package)
          # Level 2a - HR: See all approved + all pending + own drafts + own cancelled (for others only - validation prevents self-packages)
          @collection = SalaryPackage.where(
            "(status IN (?) OR (status IN (?) AND created_by_id = ?))",
            [ SalaryPackage.statuses[:approved], SalaryPackage.statuses[:rejected], SalaryPackage.statuses[:pending_approval] ],
            [ SalaryPackage.statuses[:draft], SalaryPackage.statuses[:cancelled] ],
            current_employee&.id # created_by = current user (self-approval prevented by validation)
          )
        elsif can?(:read, :salary_package)
          # Level 2b - Financial: See all approved + all pending (but no drafts - they cannot create)
          @collection = SalaryPackage.where(
            status: [ SalaryPackage.statuses[:approved], SalaryPackage.statuses[:rejected], SalaryPackage.statuses[:pending_approval] ]
          )
        elsif can?(:read_own, :salary_package)
          # Employees: only see own approved packages (no drafts/pending)
          @collection = SalaryPackage.visible_to_employee.where(employee_id: current_employee.id)
        else
          # No access
          @collection = SalaryPackage.none
        end
      end

      def set_collection
        # Only set collection if not already set by apply_permission_filters
        @collection ||= SalaryPackage.all.includes(:employee, :approval_request)
      end

      def set_salary_package
        @salary_package = SalaryPackage.find(params[:id])

        # Check if the package is in the filtered collection (permission check)
        # Exception: Allow access to own drafts for editing purposes (even if not in list view)
        unless @collection.exists?(@salary_package.id) ||
               (@salary_package.draft? && @salary_package.created_by_id == current_employee&.id)
          render_forbidden("You don't have permission to view this salary package")
        end
      rescue ActiveRecord::RecordNotFound
        render_not_found_error
      end

      def render_not_found_error
        serialize_errors({ detail: "Resource not found" }, status: :not_found)
      end

      def set_employee
        @employee = Employee.find(params[:salary_package][:employee_id])
      end

      def salary_package_params
        params.require(:salary_package).permit(
          :transportation_allowance,
          :adjustment_reason,
          :other_allowances,
          :effective_date,
          :base_salary,
          :employee_id,
          :notes
        )
      end

      def own_resource?
        @salary_package&.employee&.id == current_employee.id
      end

      def authorize_create
        return if can?(:create, :salary_package) || can?(:create_others, :salary_package) || can?(:manage_others, :salary_package) || can?(:manage, :salary_package)
        render_forbidden("You don't have permission to create salary packages")
      end

      def authorize_index
        return if can?(:read, :salary_package) || can?(:read_own, :salary_package) || can?(:manage, :salary_package) || can?(:manage_others, :salary_package)
        render_forbidden("You don't have permission to view salary packages")
      end

      def authorize_show
        return if can?(:read, :salary_package) || can?(:manage, :salary_package) || can?(:manage_others, :salary_package)
        return if can?(:read_own, :salary_package) && own_resource?
        render_forbidden("You don't have permission to view this salary package")
      end

      def authorize_update
        # Admin users can update any package
        return if can?(:manage, :salary_package)

        # Check if package is editable (business logic)
        unless @salary_package.editable?
          return serialize_errors({ detail: "This salary package is not editable in its current state" }, :unprocessable_entity)
        end

        # For draft packages, only creator can edit (creator-specific logic)
        unless @salary_package.created_by_id == current_employee&.id
          render_forbidden("You can only edit salary packages you created")
        end
      end

      def authorize_submit
        # Submit is covered by create/manage permissions since it's part of the creation workflow
        return if can?(:create, :salary_package) || can?(:create_others, :salary_package) || can?(:manage_others, :salary_package) || can?(:manage, :salary_package)
        render_forbidden("You don't have permission to submit this salary package")
      end

      def authorize_cancel
        # Admin users can cancel any package
        return if can?(:manage, :salary_package)

        # For draft packages, only creator can cancel (creator-specific logic)
        unless @salary_package.cancellable_by?(current_employee)
          render_forbidden("You can only cancel your own draft packages")
        end
      end

      def render_forbidden(message)
        serialize_errors({ detail: message }, :forbidden)
      end
    end
  end
end
