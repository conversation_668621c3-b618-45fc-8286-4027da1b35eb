module Api
  module Finance
    class SalaryCalculationsController < ApplicationController
      include Salary<PERSON>lipHelper

      before_action :authenticate_session!
      before_action :apply_permission_filters, only: [ :index ]
      before_action :set_collection, only: [ :index ]
      before_action :set_salary_calculation, only: [ :show, :download_slip, :preview_slip, :regenerate_slip, :update ]
      before_action :authorize_index, only: [ :index ]
      before_action :authorize_show, only: [ :show ]
      before_action :authorize_download_slip, only: [ :download_slip, :preview_slip ]
      before_action :authorize_show, only: [ :regenerate_slip ]
      before_action :authorize_calculate, only: [ :calculate_period, :calculate_custom ]
      before_action :authorize_update, only: [ :update ]
      before_action :authorize_submit, only: [ :submit ]

      api! "Lists salary calculations"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param_group :pagination_params
      param_group :filter_params
      param_group :sort_params
      param_group :include_params
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Lists salary calculations based on permissions:
        — Users with <code>:read, :salary_calculation</code> permission can see all salary calculations
        — Users with <code>:read_own, :salary_calculation</code> permission can only see their own salary calculations

        Supports filtering by period, status, and payment_date.
        Supports sorting with sort=field or sort=-field (descending).
        Supports pagination with page[number] and page[size].
      HTML
      )
      returns code: 200, desc: "List of salary calculations"

      def index
        apply_filters(@collection) do |filtered_collection|
          records, meta = paginate(filtered_collection)
          serialize_response(records, meta: meta)
        end
      end

      api! "Shows a specific salary calculation"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the salary calculation"
      param :include_details, [ true, false ], desc: "Include detailed breakdown of the calculation"
      param_group :include_params
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Shows details for a specific salary calculation including gross salary, deductions, and net salary.
        Requires <code>:read, :salary_calculation</code> permission.

        When <code>include_details=true</code> is specified, the response will include a detailed breakdown
        of how the salary was calculated, including base salary, additions, and itemized deductions.
      HTML
      )
      returns code: 200, desc: "Salary calculation details"
      error code: 404, desc: "Salary calculation not found"

      def show
        # Check if details are requested
        include_details = params[:include_details].present? && params[:include_details] == 'true'

        serialize_response(@salary_calculation, include_details: include_details)
      end

      api! "Downloads a salary slip PDF"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the salary calculation"
      param :regenerate, [ true, false ], required: false, desc: "Force regenerate the PDF if it exists"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Downloads the PDF salary slip for a specific salary calculation.
        Requires <code>:read, :salary_calculation</code> permission.

        Note: Salary slips are only available for paid salary calculations.
        Use regenerate=true to force regeneration of existing PDFs.
      HTML
      )
      returns code: 302, desc: "Redirect to the PDF file"
      returns code: 200, desc: "PDF file content (if direct download)"
      error code: 404, desc: "Salary slip not found"
      error code: 422, desc: "Salary calculation not in paid status"

      def download_slip
        unless @salary_calculation.paid?
          return serialize_errors({ detail: "Salary slip is only available for paid calculations" }, :unprocessable_entity)
        end

        # Check if regeneration is requested or PDF doesn't exist
        if params[:regenerate] == 'true' || !@salary_calculation.salary_slip_pdf.attached?
          generate_salary_slip
        end

        if @salary_calculation.salary_slip_pdf.attached?
          # Option 1: Redirect to blob URL (recommended for web)
          if request.format.html?
            redirect_to rails_blob_url(@salary_calculation.salary_slip_pdf, disposition: 'inline')
          else
            # Option 2: Send file data directly (for API clients)
            send_data @salary_calculation.salary_slip_pdf.download,
                      filename: salary_slip_filename,
                      type: 'application/pdf',
                      disposition: 'inline'
          end
        else
          serialize_errors({ detail: "Failed to generate salary slip" }, :internal_server_error)
        end
      end

      api! "Preview salary slip as PDF (for development/testing)"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the salary calculation"
      description "Returns PDF content for preview purposes"
      returns code: 200, desc: "PDF content"

      def preview_slip
        unless @salary_calculation.paid?
          return serialize_errors({ detail: "Salary slip is only available for paid calculations" }, :unprocessable_entity)
        end

        # Generate PDF content using updated SlipService
        slip_service = Salary::SlipService.new(@salary_calculation)

        # Check if HTML preview is requested
        if params[:format] == 'html' || request.format.html?
          preview_html_slip
        else
          # Use the new Grover HTML-to-PDF generation
          pdf_content = slip_service.send(:generate_html_pdf)
          send_data pdf_content,
                    filename: salary_slip_filename,
                    type: 'application/pdf',
                    disposition: 'inline'
        end
      end

      api! "Regenerate salary slip PDF"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the salary calculation"
      description "Forces regeneration of the salary slip PDF"
      returns code: 200, desc: "Success message"
      error code: 422, desc: "Salary calculation not in paid status"

      def regenerate_slip
        unless @salary_calculation.paid?
          return serialize_errors({ detail: "Salary slip is only available for paid calculations" }, :unprocessable_entity)
        end

        if generate_salary_slip
          render json: { message: "Salary slip regenerated successfully" }, status: :ok
        else
          serialize_errors({ detail: "Failed to regenerate salary slip" }, :internal_server_error)
        end
      end

      def calculate_period
        period = params[:period]

        unless period =~ /\A\d{4}-\d{2}\z/
          return serialize_errors({ detail: "Period must be in format YYYY-MM" })
        end

        # Parse the period to get start_date and end_date
        year, month = period.split('-').map(&:to_i)
        start_date = Date.new(year, month, 1)
        end_date = start_date.end_of_month

        results = {
          success: [],
          failure: []
        }

        Employee.active.each do |employee|
          service = Salary::CalculationService.new(employee, {
            start_date: start_date,
            end_date: end_date
          })
          calculation = service.calculate

          if calculation
            results[:success] << { employee_id: employee.id, calculation_id: calculation.id }
          else
            results[:failure] << { employee_id: employee.id, errors: service.errors }
          end
        end

        render json: results
      end

      api! "Calculates salary for a custom period"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, required: true, desc: "ID of the employee"
      param :start_date, Date, required: true, desc: "Start date of the calculation period (YYYY-MM-DD)"
      param :end_date, Date, required: true, desc: "End date of the calculation period (YYYY-MM-DD)"
      param :reason, String, desc: "Reason for custom calculation (e.g., 'termination')"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Calculates salary for a custom date range, useful for:
        - Employee termination
        - Mid-month adjustments
        - Partial period calculations

        This endpoint creates a day-specific salary calculation that pro-rates
        the salary based on the number of working days in the specified period.

        Requires permission: <code>:calculate, :salary_calculation</code>
      HTML
      )
      returns code: 200, desc: "Calculation result"
      error code: 422, desc: "Validation errors"

      # POST /api/finance/salary_calculations/calculate_custom
      def calculate_custom
        # Validate parameters
        employee_id = params[:employee_id]
        start_date = params[:start_date]
        end_date = params[:end_date]
        reason = params[:reason]

        begin
          start_date = Date.parse(start_date) if start_date.is_a?(String)
          end_date = Date.parse(end_date) if end_date.is_a?(String)
        rescue ArgumentError
          return serialize_errors({ detail: "Invalid date format. Use YYYY-MM-DD." })
        end

        # Find employee
        employee = Employee.find_by(id: employee_id)
        unless employee
          return serialize_errors({ detail: "Employee not found" }, :not_found)
        end

        # Calculate custom period salary
        service = Salary::CalculationService.new(
          employee,
          {
            start_date: start_date,
            end_date: end_date,
            reason: reason
          }
        )

        calculation = service.calculate

        if calculation
          serialize_response(calculation)
        else
          serialize_errors({ detail: service.errors.join(", ") })
        end
      end

      api! "Updates a salary calculation"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the salary calculation"
      param :gross_salary, :decimal, desc: "Gross salary amount"
      param :net_salary, :decimal, desc: "Net salary amount"
      param :calculation_date, Date, desc: "Date when calculation was performed"
      param :payment_date, Date, desc: "Date when payment was made"
      param :notes, String, desc: "Additional notes"
      param :total_hours, :decimal, desc: "Total hours worked"
      param :deductions, Hash, desc: "Deductions breakdown (JSON object)"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Updates a salary calculation in draft status.
        For submitting calculations for approval, use the submit endpoint.

        Required permissions:
        - <code>:update, :salary_calculation</code>
      HTML
      )
      returns code: 200, desc: "Updated salary calculation"
      error code: 422, desc: "Validation errors"

      def update
        # Set the actor_user_id for approval tracking
        @salary_calculation.actor_user_id = current_user.id

        if @salary_calculation.update(salary_calculation_params)
          serialize_response(@salary_calculation)
        else
          serialize_errors({ detail: @salary_calculation.errors.full_messages.join(", ") })
        end
      end

      api! "Submit a salary calculation for approval"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the salary calculation"
      description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Submits a salary calculation for approval workflow.
      Changes status from 'draft' to 'submitted' and triggers approval process.
      Requires permission: <code>:submit, :salary_calculation</code>.
      HTML
      )
      returns code: 200, desc: "Salary calculation submitted successfully"
      error code: 422, desc: "Validation errors"

      def submit
        # Set the actor_user_id for approval tracking
        @salary_calculation.actor_user_id = current_user.id

        if @salary_calculation.update(status: 'submitted')
          serialize_response(@salary_calculation)
        else
          serialize_errors({ detail: @salary_calculation.errors.full_messages.join(", ") })
        end
      end

      private

      def set_collection
        @collection = SalaryCalculation.all
      end

      def set_salary_calculation
        @salary_calculation = SalaryCalculation.find(params[:id])
      end

      def authorize_index
        # Already handled in set_collection
        return if can?(:read, :salary_calculation) || can?(:read_own, :salary_calculation)

        render_forbidden("You don't have permission to view salary calculations")
      end

      def authorize_show
        return if can?(:read, :salary_calculation)

        # Check if user has permission to view their own record
        if own_record? && can?(:read_own, :salary_calculation)
          return
        end

        render_forbidden("You don't have permission to view this salary calculation")
      end

      def authorize_download_slip
        authorize_show
      end

      def authorize_calculate
        return if can?(:calculate, :salary_calculation)

        render_forbidden("You don't have permission to calculate salaries")
      end

      def authorize_update
        return if can?(:update, :salary_calculation)

        render_forbidden("You don't have permission to update this salary calculation")
      end

      def authorize_submit
        return if can?(:submit, :salary_calculation)

        render_forbidden("You don't have permission to submit this salary calculation")
      end

      def apply_permission_filters
        # Apply employee filter based on permissions by modifying params
        if can?(:read, :salary_calculation)
          # Full access - no filter needed
        elsif can?(:read_own, :salary_calculation)
          # Restrict to own records by adding employee_id filter to params
          params[:filter] ||= {}
          params[:filter][:employee_id_eq] = current_employee.id
        end
      end

      def own_record?
        @salary_calculation.employee_id == current_employee.id
      end

      def salary_calculation_params
        params.require(:salary_calculation).permit(
          :period,
          :gross_salary,
          :net_salary,
          :status,
          :calculation_date,
          :payment_date,
          :notes,
          :total_hours,
          deductions: {}
        )
      end

      def generate_salary_slip
        begin
          # Remove existing PDF if present
          @salary_calculation.salary_slip_pdf.purge if @salary_calculation.salary_slip_pdf.attached?

          # Generate new PDF
          result = Salary::SlipService.new(@salary_calculation).generate

          if result
            Rails.logger.info "Salary slip generated successfully for calculation #{@salary_calculation.id}"
            true
          else
            Rails.logger.error "Failed to generate salary slip for calculation #{@salary_calculation.id}"
            false
          end
        rescue => e
          Rails.logger.error "Error generating salary slip for calculation #{@salary_calculation.id}: #{e.message}"
          false
        end
      end

      def preview_html_slip
        # Use the new HTML generation method from SlipService
        slip_service = Salary::SlipService.new(@salary_calculation)
        html_content = slip_service.send(:generate_html_content)

        render html: html_content.html_safe
      end

      def salary_slip_filename
        "salary_slip_#{@salary_calculation.employee.name.parameterize}_#{@salary_calculation.period}.pdf"
      end
    end
  end
end
