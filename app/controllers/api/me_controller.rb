# frozen_string_literal: true

module Api
  class MeController < ApplicationController

    before_action :authenticate_session!

    api! "Returns current employee information"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns the current authenticated user's employee information.
      Includes both user data from the core service and employee-specific data.

      <b>Data Included:</b>
      <ul>
        <li><strong>User Data:</strong> Name, email, permissions, roles, avatar (from core service)</li>
        <li><strong>Employee Data:</strong> Department, start date, phone, status (from people service)</li>
        <li><strong>Relationships:</strong> User roles, salary package information</li>
      </ul>

      <b>Authentication:</b>
      Requires a valid session token. No special permissions required - users can always view their own information.

      <b>Use Case:</b>
      This endpoint is designed for use within the AtharPeople system where complete employee context is needed.
      For cross-system navigation data, use the core service's /api/me endpoint instead.
    HTML
    )
    returns code: 200, desc: "Current employee information"
    error code: 404, desc: "Employee record not found for current user"
    error code: 401, desc: "Authentication required"

    def show
      unless current_employee
        serialize_errors({ detail: "Employee record not found for current user" }, :not_found)
        return
      end

      serialize_response(current_employee)
    end
  end
end
