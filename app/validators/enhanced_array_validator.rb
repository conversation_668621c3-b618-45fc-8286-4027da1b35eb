module Apipie
  module Validator
    class EnhancedArrayValidator < BaseValidator
      def initialize(param_description, argument, options = {}, block = nil)
        super(param_description, argument, options, block)
        @type = argument
        @items_validator = nil
        if @type.is_a?(Hash) && @type.keys.size == 1 && @type.values.first.is_a?(Hash)
          @items_validator = Apipie::Validator::HashValidator.new(param_description, @type.values.first)
        end
      end

      def validate(value)
        return true if value.nil? && param_description.required == false

        # Convert hash with numeric keys to array if needed
        if value.is_a?(Hash) && value.keys.all? { |k| k.to_s =~ /\A\d+\z/ }
          value = value.sort_by { |k, _| k.to_i }.map { |_, v| v }
        end

        # Now validate as a normal array
        return false unless value.is_a?(Array) || value.is_a?(String)

        if value.is_a?(String)
          value = value.split(',').map(&:strip)
        end

        if @items_validator
          value.each do |item|
            return false unless @items_validator.valid?(item)
          end
        end

        true
      end

      def self.build(param_description, argument, options, block)
        if argument == Array || argument.is_a?(Array)
          self.new(param_description, argument, options, block)
        end
      end

      def description
        "Must be an array or a comma-separated string. Values must be of type #{@type}."
      end
    end
  end
end

# Register the validator
Apipie::Validator.register(Apipie::Validator::EnhancedArrayValidator)
