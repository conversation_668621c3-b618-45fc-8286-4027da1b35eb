# app/models/leave.rb
class Leave < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable
  include Athar::Commons::Models::Concerns::ActsAsApprovable

  scope :active_status, -> { where(status: [ :pending, :approved ]) }

  include Leave::ValidationConcern
  include Leave::ApprovalConcern

  belongs_to :employee
  has_many_attached :documents

  enum :leave_type, {
    annual: 0,
    sick: 1,
    marriage: 2,
    maternity: 3,
    paternity: 4,
    unpaid: 5
  }

  enum :leave_duration, {
    full_day: 0,
    half_day_morning: 1,
    half_day_afternoon: 2
  }

  enum :status, {
    pending: 0,
    approved: 1,
    rejected: 2,
    withdrawn: 3
  }, default: :pending

  # Scope to get active leaves (pending or approved)
  scope :active, -> { where(status: [ :pending, :approved ]) }

  # Required for the serializer
  def  document_ids
    documents.pluck(:id)
  end
end
