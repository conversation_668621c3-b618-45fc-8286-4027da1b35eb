class Setting < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable
  include Setting::TypeResolver
  include Setting::TypeHandling
  include Setting::AttendanceSettings
  include Setting::CompanySettings

  # Setting type enumeration
  enum :setting_type, {
    string: 0,      # Raw text - no parsing
    integer: 1,     # Parse to Integer, validate numeric
    boolean: 2,     # Parse "true"/"false" to boolean
    decimal: 3,     # Parse to BigDecimal, validate decimal format
    time: 4,        # Parse "HH:MM" format, validate time
    json: 5,        # Parse JSON string to Hash/Array
    email: 6,       # Validate email format, return as string
    phone: 7,       # Validate phone format, return as string
    url: 8,         # Validate URL format, return as string
    currency: 9,    # Parse to BigDecimal with currency validation
    array: 10,      # Parse comma-separated to Array
    date: 11,       # Parse to Date object
    datetime: 12    # Parse to DateTime object
  }, default: :string

  validates :namespace, presence: true
  validates :key, presence: true, uniqueness: { scope: :namespace }
  validates :setting_type, presence: true
  validate :validate_value_format

  # Scopes for easy querying
  scope :by_namespace, ->(namespace) { where(namespace: namespace) }
  scope :editable, -> { where(is_editable: true) }
  scope :non_editable, -> { where(is_editable: false) }

  # Logical key for easy identification
  public

  def logical_key
    "#{namespace}/#{key}"
  end

  # Delegate class methods to service for cleaner separation
  def self.get(namespace, key, default = nil)
    SettingService.get(namespace, key, default)
  end

  def self.set(namespace, key, value, description = nil, is_editable = true, setting_type = :string)
    SettingService.set(namespace, key, value, description, is_editable, setting_type)
  end

  # Utility methods for timestamp conversion (delegates to TimeUtility)
  def self.attendance_work_start_timestamp(date)
    time_value = attendance_work_start_time
    TimeUtility.convert_time_to_timestamp(time_value, date)
  end

  def self.attendance_work_end_timestamp(date)
    time_value = attendance_work_end_time
    TimeUtility.convert_time_to_timestamp(time_value, date)
  end

  # Get all settings for a namespace
  def self.for_namespace(namespace)
    where(namespace: namespace)
  end

  # Logical key helper methods
  def logical_key
    "#{namespace}/#{key}"
  end

  def self.find_by_logical_key(namespace, key)
    find_by!(namespace: namespace, key: key)
  end

  def self.find_by_logical_key_safe(namespace, key)
    find_by(namespace: namespace, key: key)
  end

  private

  # Validation for value format based on type
  def validate_value_format
    handler = self.class.handler_for(setting_type)
    return unless handler.respond_to?(:validate_format)

    errors.add(:value, handler.validate_format(value)) unless handler.validate_format(value).nil?
  end

  # Get default value for type when parsing fails
  def default_value_for_type
    handler = self.class.handler_for(setting_type)
    handler.default_value
  end
end
