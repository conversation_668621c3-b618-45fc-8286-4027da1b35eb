# Full-Text Search Implementation

This application implements full-text search using PostgreSQL's built-in full-text search capabilities. The implementation uses the following components:

## Components

1. **pg_search gem**: Provides a DSL for defining search scopes in ActiveRecord models.
2. **scenic gem**: Manages database views in a structured way.
3. **PostgreSQL extensions**: Uses `pg_trgm` for trigram similarity search and `unaccent` for accent-insensitive search.
4. **Materialized view**: Stores pre-computed search data for efficient querying.
5. **Backing table**: Stores denormalized data from both local models and remote services.

## Database Structure

The full-text search implementation uses the following database objects:

1. **employee_search_views_data**: A backing table that stores denormalized data from employees and their associated users.
2. **employee_search_views**: A SQL view (managed by Scenic) that reads from the backing table.
3. **employee_search_data**: A materialized view that adds a tsvector column for efficient searching.

## How It Works

1. When an employee is created or updated, the `update_search_data` callback is triggered.
2. This callback calls `EmployeeSearchData.update_for_employee(self)` to update the search data.
3. The `update_for_employee` method updates the backing table and refreshes the materialized view.
4. When a search is performed, the `search_employees` method is called on the Employee model.
5. This method uses the `full_text_search` scope on the EmployeeSearchData model to search the materialized view.
6. The search results are then mapped back to Employee records.

## API Usage

To use the full-text search, make a request like this:

```
GET /api/employees?filter[search]=john
```

This will search for employees with "john" in their name, email, or department. You can combine this with other filters:

```
GET /api/employees?filter[search]=john&filter[department_eq]=hr
```

## Maintenance

The materialized view needs to be refreshed periodically to reflect changes in the data. This can be done using the following rake tasks:

```
# Refresh all search data (backing table and materialized view)
rails search:refresh

# Refresh just the materialized view
rails search:refresh_view
```

Consider setting up a periodic job to refresh the materialized view, especially if user data changes frequently.

## Implementation Details

The implementation is designed to handle data from both local models and remote services. The backing table approach allows us to store data from remote sources (like user data from a gRPC service) and make it searchable using PostgreSQL's full-text search capabilities.

### Data Type Handling

The implementation includes robust handling of different data types:

- **Enum fields**: Stored as strings in the search table for better searchability
- **NULL values**: Properly handled in SQL queries
- **Empty strings**: Treated as NULL for integer fields
- **Numeric strings**: Converted to proper numeric values when appropriate

This ensures that the search functionality works correctly with all types of data, even when the data comes from remote services with different data representations.

The materialized view approach provides efficient searching by pre-computing the search vectors and storing them in a dedicated table with appropriate indexes.

## Testing

The implementation includes tests that verify the full-text search functionality. These tests create test data, insert it into the backing table, refresh the materialized view, and then perform searches to verify that the expected results are returned.
