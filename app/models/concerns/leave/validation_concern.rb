module Leave::ValidationConcern
  extend ActiveSupport::Concern

  included do
    validates :start_date, :end_date, :leave_type, :leave_duration, presence: true

    validates_date :start_date,
                   on_or_after: lambda { Date.current - 90.days },
                   message: "cannot be more than 3 months in the past",
                   if: :validate_dates?

    validates_date :end_date,
                   on_or_after: :start_date,
                   message: "must be on or after the start date",
                   if: :validate_dates?

    validates :start_date, :end_date, overlap: {
      scope: "employee_id",
      query_options: { active_status: nil },
      exclude_edges: false,
      exclude_self: true,
      message_title: "Leave period",
      message_content: "overlaps with another pending or approved leave"
    }, if: :validate_overlap?

    validate :validate_duration
    validate :validate_half_day_constraints
    validate :validate_not_withdrawn_freeze
    validate :validate_withdrawal_status, if: :withdrawal_requested?
  end

  def duration
    days = (end_date - start_date).to_f + 1
    days = 0.5 if days == 1 && half_day?
    days
  end

  def with_deduction?
    leave_type == "unpaid"
  end

  def half_day?
    half_day_morning? || half_day_afternoon?
  end

  def validate_overlap?
    # For new records, always validate overlap
    return true if new_record?

    # For existing records, only validate if the status is pending or approved
    # This ensures withdrawn leaves don't interfere with validation
    pending? || approved?
  end

  def validate_dates?
    return true if new_record?
    return false if status_changed? && withdrawn?
    pending? || approved?
  end

  def validate_not_withdrawn_freeze
    return if new_record?

    if status_was == "withdrawn" && changed?
      errors.add(:base, "Withdrawn leaves cannot be modified")
    end
  end

  def can_be_withdrawn?
    # Check if the status is valid for withdrawal
    return false unless status_was.in?(["pending", "approved"])

    # Check if the leave is in the past
    # Cannot withdraw leaves that have already started or ended
    return false if start_date < Date.current

    # All checks passed
    true
  end

  def withdrawal_requested?
    status_changed? && withdrawn? && status_was != 'withdrawn'
  end

  def validate_withdrawal_status
    unless can_be_withdrawn?
      if start_date < Date.current
        errors.add(:status, "Leave cannot be withdrawn because it has already started or is in the past")
      else
        errors.add(:status, "Leave cannot be withdrawn in its current state")
      end
    end
  end

  private

  def working_days
    days = (start_date..end_date).count { |d| !d.friday? && !d.saturday? }
    days = 0.5 if days == 1 && half_day?
    days
  end

  def validate_duration
    allowed_days = {
      annual: 14,
      sick: 14,
      marriage: 3,
      maternity: 70,
      paternity: 3,
      unpaid: nil
    }

    return unless leave_type && allowed_days[leave_type.to_sym]

    max_allowed = allowed_days[leave_type.to_sym]
    if max_allowed && working_days > max_allowed
      errors.add(:base, "#{leave_type.titleize} leave cannot exceed #{max_allowed} working days")
    end
  end

  def validate_half_day_constraints
    if half_day? && start_date != end_date
      errors.add(:leave_duration, "Half-day leaves can only be applied to same-day leaves")
    end

    if half_day? && %i[marriage maternity paternity].include?(leave_type.to_sym)
      errors.add(:leave_duration, "#{leave_type.titleize} leave cannot be taken as half-day")
    end
  end
end
