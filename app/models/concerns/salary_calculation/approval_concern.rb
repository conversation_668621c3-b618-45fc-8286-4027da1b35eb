module SalaryCalculation::ApprovalConcern
  extend ActiveSupport::Concern

  included do
    attr_accessor :actor_user_id

    before_save :submit_for_approval_if_submitted

    acts_as_approvable

    def approval_action
      "approve_salary_calculation"
    end

    def system_name
      "people"
    end

    def on_approval_status_change(new_status, previous_status)
      Rails.logger.info("Salary calculation callback: status changed from #{previous_status} to #{new_status}")

      case new_status
      when "approved"
        if actor_user_id.present?
          approver_employee = Employee.find_by(user_id: actor_user_id)
          self.approved_by_id = approver_employee&.id
        end
        update!(status: :approved)
      when "rejected"
        update!(status: :rejected)
      when "canceled"
        update!(status: :draft)
      end
    end

    def update_remote_status
      begin
        approval_request&.cancel!(actor_user_id)
        true
      rescue StandardError => e
        Rails.logger.error("Failed to update remote status: #{e.message}")
        errors.add(:base, "Failed to update remote status: #{e.message}")
        false
      end
    end

    # Prepare context for approval workflow
    def approval_context
      {
        employee_name: employee.name,
        period: period,
        gross_salary: gross_salary.to_s,
        net_salary: net_salary.to_s,
        priority: determine_priority
      }
    end

    # Determine priority based on salary amount
    def determine_priority
      if gross_salary > 5000
        "high"
      elsif gross_salary > 2000
        "medium"
      else
        "low"
      end
    end

    private

    def submit_for_approval_if_submitted
      # Check if status is changing from draft to submitted
      if status_changed? && status_was == "draft" && status == "submitted" && !approval_request
        begin
          result = submit_for_approval(employee.user_id, nil, approval_context)

          unless result
            error_message = approval_error || "Failed to create approval request"
            errors.add(:base, error_message)
            return false
          end
        rescue => e
          Rails.logger.error("Error submitting for approval: #{e.message}")
          errors.add(:base, "Error submitting for approval: #{e.message}")
          return false
        end
      end
      true
    end
  end
end
