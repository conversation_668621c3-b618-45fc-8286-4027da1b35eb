# frozen_string_literal: true

module SalaryCalculation::ComputedFields
  extend ActiveSupport::Concern

  # Employee Social Security (from database configuration)
  def employee_social_security
    deductions['employee_social_security']&.to_f || 0
  end

  # Income Tax
  def income_tax
    deductions['income_tax']&.to_f || 0
  end

  # Medical Insurance
  def medical_insurance
    deductions['medical_insurance']&.to_f || 0
  end

  # Salary Advances
  def salary_advances
    deductions['salary_advances']&.to_f || 0
  end

  # Other Deductions
  def other_deductions
    deductions['other_deductions']&.to_f || 0
  end

  # Leave Deductions (unpaid leave + attendance-based deductions)
  def leave_deductions
    categories = [ 'leave_unpaid' ]

    # Include attendance-based deductions if enabled and employee is not exempt
    if Setting.attendance_deductions_enabled? && !employee.exempt_from_attendance_deductions
      categories += [ 'leave_attendance_based' ]  # V2 uses this category
    end

    calculation_details
      .where(detail_type: 'deduction', category: categories)
      .sum(:amount)
  end

  # Legacy methods for backward compatibility
  def leaves_to_pay
    leave_deductions
  end

  def time_based_deductions
    leave_deductions
  end

  # Employer Social Security contributions (informational)
  def employer_social_security
    # Convert period string to Date object for SocialSecurityConfig.current
    period_date = period.is_a?(Date) ? period : Date.parse("#{period}-01")
    config = SocialSecurityConfig.current(period_date)
    return 0 unless config

    contributory_salary = config.max_salary ? [ gross_salary, config.max_salary ].min : gross_salary
    (contributory_salary * (config.employer_rate / 100.0)).round(2)
  end

  # Net to Pay (net salary after all deductions)
  def net_to_pay
    net_salary
  end

  # Payment Method (default or from salary package)
  def payment_method
    'Transfer'
  end

  # Total Deductions (override existing method for consistency)
  def total_deductions
    deductions.values.sum(&:to_f) + leaves_to_pay
  end

  # Helper method to get all deduction breakdown
  def deduction_breakdown
    {
      employee_social_security: employee_social_security,
      income_tax: income_tax,
      medical_insurance: medical_insurance,
      salary_advances: salary_advances,
      other_deductions: other_deductions,
      time_based_deductions: time_based_deductions
    }
  end

  # Helper method to get all employer contribution breakdown
  def employer_contribution_breakdown
    {
      employer_social_security: employer_social_security
    }
  end

  private

  # Check if attendance deductions are enabled
  def attendance_deductions_enabled?
    Setting.attendance_deductions_enabled?
  end

  # Check if employee is exempt from attendance deductions
  def employee_exempt_from_attendance_deductions?
    employee.exempt_from_attendance_deductions
  end
end
