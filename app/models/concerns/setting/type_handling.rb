module Setting::TypeHandling
  extend ActiveSupport::Concern

  # Override value getter to return parsed value by default
  def value
    handler = self.class.handler_for(setting_type)
    handler.parse(self[:value], self)
  end

  # Override value setter to normalize input
  def value=(input)
    handler = self.class.handler_for(setting_type)
    self[:value] = handler.normalize(input, self)
  end

  # Access to the raw string value stored in database
  def raw_value
    self[:value]
  end

  # Set the raw string value in database
  def raw_value=(input)
    self[:value] = input.to_s
  end

  # Backward compatibility - remove these eventually
  alias_method :typed_value, :value
  alias_method :typed_value=, :value=
end
