module Setting::TypeResolver
  extend ActiveSupport::Concern

  included do
    # Dynamic type handler discovery
    class_attribute :type_handlers, default: {}
  end

  class_methods do
    # Register a type handler
    def register_type_handler(type, handler_class)
      type_handlers[type.to_sym] = handler_class
    end

    # Get handler for a type (lazy loading)
    def handler_for(type)
      ensure_handlers_loaded
      type_handlers[type.to_sym] || type_handlers[:string]
    end

    private

    def ensure_handlers_loaded
      return if type_handlers.any?
      register_default_type_handlers
    end

    def register_default_type_handlers
      # Fully dynamic discovery - no hardcoded types!
      return unless defined?(Setting::TypeHandlers)

      # Auto-discover type handlers from filesystem
      discover_and_load_type_handlers

      # Auto-register handlers for all enum types
      Setting::TypeHandlers.constants.each do |const_name|
        handler_class = Setting::TypeHandlers.const_get(const_name)
        next unless handler_class.respond_to?(:handles_type?)

        # Use actual enum values - fully dynamic!
        setting_types.each_key do |type|
          if handler_class.handles_type?(type)
            register_type_handler(type, handler_class)
          end
        end
      end
    end

    def discover_and_load_type_handlers
      # Auto-discover and load handler files from filesystem
      handler_dir = Rails.root.join('app', 'models', 'setting', 'type_handlers')
      return unless Dir.exist?(handler_dir)

      Dir.glob("#{handler_dir}/*_handler.rb").each do |file|
        # Extract class name from filename
        class_name = File.basename(file, '.rb').camelize

        # Auto-load the handler class
        begin
          "Setting::TypeHandlers::#{class_name}".constantize
        rescue NameError
          # If not loaded, require it
          require file
        end
      end
    end
  end

  # Type-aware value getter using dynamic resolution
  def typed_value
    handler = self.class.handler_for(setting_type)
    handler.parse(value, self)
  rescue => e
    Rails.logger.error("Error parsing typed value for #{logical_key}: #{e.message}")
    handler.default_value
  end

  # Type-aware value setter using dynamic resolution
  def typed_value=(input)
    handler = self.class.handler_for(setting_type)
    self.value = handler.normalize(input, self)
  end

  # Get validation rules for current type
  def validation_rules_for_type
    handler = self.class.handler_for(setting_type)
    handler.validation_rules
  end

  # Get display name for current type
  def type_display_name
    handler = self.class.handler_for(setting_type)
    handler.display_name
  end
end
