module Attendance::Event::ValidationConcern
  extend ActiveSupport::Concern

  included do
    validates :timestamp, presence: true

    # Validate that timestamp (stored as integer) is not in the future
    validates :timestamp, numericality: {
      less_than_or_equal_to: -> { Time.current.to_i },
      message: "cannot be in the future"
    }

    # Note: We don't prevent duplicate events since they come from attendance machines
    # and rejecting them could cause inconsistency. Instead, we handle duplicates
    # during the resolution process in ResolutionConcern.
  end
end
