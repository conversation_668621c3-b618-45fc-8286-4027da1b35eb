module Attendance::Event::StatisticsConcern
  extend ActiveSupport::Concern

  module ClassMethods
    def calculate_daily_duration(employee, date)
      events = daily_events(employee, date).order(timestamp: :asc)

      # If we have undetermined events, try to infer check-in/check-out pairs
      if events.undetermined.exists?
        return calculate_duration_with_undetermined_events(events)
      end

      # Standard calculation for determined events
      total_minutes = 0
      current_check_in = nil

      events.each do |event|
        if event.check_in?
          # Store the check-in time
          current_check_in = event.timestamp
        elsif event.check_out? && current_check_in.present?
          # Calculate duration for this pair (timestamps are integers)
          duration_minutes = ((event.timestamp - current_check_in) / 60).round
          total_minutes += duration_minutes
          current_check_in = nil
        end
      end

      # If there's an unpaired check-in at the end of the day
      if current_check_in.present?
        # Use end of workday or current time as the implicit check-out
        implicit_checkout = [ date.end_of_day.to_i, Time.current.to_i ].min
        duration_minutes = ((implicit_checkout - current_check_in) / 60).round
        total_minutes += duration_minutes
      end

      total_minutes
    end

    # Handle undetermined events by inferring check-in/check-out pairs
    def calculate_duration_with_undetermined_events(events)
      total_minutes = 0

      # Sort events by timestamp
      sorted_events = events.sort_by(&:timestamp)

      # Group events into pairs (even index = in, odd index = out)
      # This assumes alternating entry/exit pattern
      current_timestamp = nil

      sorted_events.each_with_index do |event, index|
        if index.even?
          # Even indices (0, 2, 4...) are treated as check-ins
          current_timestamp = event.timestamp
        else
          # Odd indices (1, 3, 5...) are treated as check-outs
          if current_timestamp.present?
            duration_minutes = ((event.timestamp - current_timestamp) / 60).round
            total_minutes += duration_minutes
            current_timestamp = nil
          end
        end
      end

      # If there's an unpaired event at the end (odd number of events)
      if current_timestamp.present?
        date = Time.at(current_timestamp).to_date # Using Time.at directly since we don't have an event object here
        implicit_checkout = [ date.end_of_day.to_i, Time.current.to_i ].min
        duration_minutes = ((implicit_checkout - current_timestamp) / 60).round
        total_minutes += duration_minutes
      end

      total_minutes
    end

    def daily_status(employee, date)
      events = daily_events(employee, date)

      return 'absent' if events.empty?

      # Check if there are any undetermined events
      if events.undetermined.exists?
        'incomplete'
      else
        # Calculate total duration to determine status
        total_minutes = calculate_daily_duration(employee, date)
        minutes_required = 8 * 60 # 8 hours in minutes

        if total_minutes >= minutes_required
          'present'
        elsif total_minutes >= (minutes_required / 2)
          'half_day'
        else
          'partial'
        end
      end
    end
  end
end
