module Attendance::Event::ResolutionConcern
  extend ActiveSupport::Concern

  # Instance methods for inferring event types
  def infer_event_type_by_time
    return event_type unless undetermined?

    # Get the hour of the day (0-23)
    hour = timestamp_as_time.hour

    # Morning hours are likely check-ins
    if hour >= 7 && hour <= 11
      'check_in'
      # Evening hours are likely check-outs
    elsif hour >= 16 && hour <= 20
      'check_out'
      # Mid-day could be either, so return undetermined
    else
      'undetermined'
    end
  end

  def infer_event_type_by_sequence
    return event_type unless undetermined?

    # Get all events for this employee on this day, ordered by timestamp
    day_events = employee.attendance_events.for_date(timestamp_date).order(timestamp: :asc)

    # Find the index of the current event
    current_index = day_events.index { |e| e.id == id }
    return 'check_in' if current_index.nil? # Shouldn't happen, but just in case

    # If this is the first event of the day, it's likely a check-in
    return 'check_in' if current_index == 0

    # Get the previous event
    previous_event = day_events[current_index - 1]

    # If previous event was a check-in or undetermined treated as check-in, this is likely a check-out
    if previous_event.check_in? ||
       (previous_event.undetermined? && previous_event.infer_event_type_by_time == 'check_in')
      'check_out'
      # If previous event was a check-out or undetermined treated as check-out, this is likely a check-in
    elsif previous_event.check_out? ||
          (previous_event.undetermined? && previous_event.infer_event_type_by_time == 'check_out')
      'check_in'
      # If we can't determine based on sequence, return undetermined
    else
      'undetermined'
    end
  end

  # Class methods for resolving undetermined events
  module ClassMethods
    def resolve_undetermined_events(employee, date)
      events = daily_events(employee, date).order(timestamp: :asc)
      undetermined_events = events.select(&:undetermined?)

      return if undetermined_events.empty?

      # If all events are undetermined, assume alternating pattern starting with check-in
      if events.all?(&:undetermined?)
        events.each_with_index do |event, index|
          new_type = index.even? ? :check_in : :check_out
          event.update(
            event_type: new_type,
            notes: "#{event.notes}\n\nAutomatically resolved to #{new_type} by system on #{Time.current}."
          )
        end
        return
      end

      # If we have some determined events, use them as anchors
      determined_events = events.reject(&:undetermined?)

      # Process each undetermined event
      undetermined_events.each do |event|
        # Try sequence-based inference first
        inferred_type = event.infer_event_type_by_sequence

        # If sequence-based doesn't work, try time-based
        if inferred_type == 'undetermined'
          inferred_type = event.infer_event_type_by_time
        end

        # If we still can't determine, use pattern recognition
        if inferred_type == 'undetermined'
          # Find the nearest determined events before and after
          previous_determined = determined_events.select { |e| e.timestamp < event.timestamp }.last
          next_determined = determined_events.select { |e| e.timestamp > event.timestamp }.first

          # Infer based on surrounding events
          if previous_determined && next_determined
            # If surrounded by same type, this is likely the opposite
            if previous_determined.event_type == next_determined.event_type
              inferred_type = previous_determined.check_in? ? 'check_out' : 'check_in'
            end
          elsif previous_determined
            # If only previous event exists, this is likely the opposite
            inferred_type = previous_determined.check_in? ? 'check_out' : 'check_in'
          elsif next_determined
            # If only next event exists, this is likely the opposite
            inferred_type = next_determined.check_in? ? 'check_out' : 'check_in'
          end
        end

        # Update the event if we were able to infer a type
        if inferred_type != 'undetermined'
          event.update(
            event_type: inferred_type,
            notes: "#{event.notes}\n\nAutomatically resolved to #{inferred_type} by system on #{Time.current}."
          )
        end
      end

      # Update the summary
      ::Attendance::Summary.recalculate_for(employee, date)
    end
  end
end
