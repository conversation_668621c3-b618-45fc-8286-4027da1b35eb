module Salary
  module TaxIntegration
    extend ActiveSupport::Concern
    
    def calculate_income_tax(gross_salary, date)
      config = TaxConfig.current(date)
      return 0 unless config

      # Calculate monthly tax based on config
      annual_salary = gross_salary * 12
      annual_tax = calculate_tax_from_brackets(annual_salary, config.config_data)

      # Return monthly portion
      annual_tax / 12
    end
    
    def calculate_tax_from_brackets(annual_income, config_data)
      # Get tax brackets from config
      brackets = config_data['brackets'] || []
      return 0 if brackets.empty?

      # Get personal exemption from config
      personal_exemption = config_data['personal_exemption'] || 0

      # Apply personal exemption
      taxable_income = [annual_income - personal_exemption, 0].max

      # Calculate tax using brackets
      total_tax = 0
      remaining_income = taxable_income

      brackets.sort_by { |b| b['min_amount'].to_f }.each do |bracket|
        min_amount = bracket['min_amount'].to_f
        max_amount = bracket['max_amount']&.to_f
        rate = bracket['rate'].to_f / 100.0

        if max_amount.nil?
          # This is the highest bracket
          total_tax += remaining_income * rate
          break
        elsif remaining_income <= 0
          break
        else
          # Calculate taxable amount in this bracket
          taxable_in_bracket = [remaining_income, max_amount - min_amount].min
          total_tax += taxable_in_bracket * rate
          remaining_income -= taxable_in_bracket
        end
      end

      total_tax
    end
  end
end
