module Salary
  module CalculationTracking
    extend ActiveSupport::Concern

    def track_calculation_details(calculation)
      # Clear existing details if recalculating
      calculation.calculation_details.destroy_all if calculation.persisted?

      # Track base salary
      track_base_salary(calculation)

      # Track deductions
      track_deductions(calculation)
    end

    private

    def track_base_salary(calculation)
      calculation.calculation_details.build(
        detail_type: 'base',
        category: 'base_salary',
        amount: calculation.gross_salary,
        description: "Base salary for period #{calculation.period_start_date} to #{calculation.period_end_date}"
      )
    end

    def track_deductions(calculation)
      calculation.deductions.each do |category, amount|
        next if amount.to_f.zero?

        description = case category.to_s
                      when 'social_security'
                        config = SocialSecurityConfig.current(calculation.period_start_date)
                        max_salary_text = config&.max_salary ? " (capped at #{config.max_salary})" : ""
                        "Social security contribution at #{config&.employee_rate || 0}% rate#{max_salary_text}"
                      when 'income_tax'
                        "Income tax based on tax brackets for #{calculation.period_start_date.year}"
                      when 'attendance'
                        "Attendance deductions for missed days, late arrivals, and early departures"
                      when 'leave'
                        "Unpaid leave deductions"
                      else
                        "Other deduction: #{category}"
                      end

        calculation.calculation_details.build(
          detail_type: 'deduction',
          category: category.to_s,
          amount: amount.to_f,
          description: description
        )
      end
    end
  end
end
