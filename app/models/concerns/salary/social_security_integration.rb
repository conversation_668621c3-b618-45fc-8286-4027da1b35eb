module Salary
  module SocialSecurityIntegration
    extend ActiveSupport::Concern

    def calculate_social_security_breakdown(gross_salary, date)
      config = SocialSecurityConfig.current(date)
      return {} unless config

      contributory_salary = config.max_salary ? [ gross_salary, config.max_salary ].min : gross_salary

      {
        employee_social_security: contributory_salary * (config.employee_rate / 100.0),
        employer_social_security: contributory_salary * (config.employer_rate / 100.0)
      }
    end

    # Keep existing methods for backward compatibility
    def calculate_social_security(gross_salary, date)
      calculate_social_security_breakdown(gross_salary, date)[:employee_social_security] || 0
    end

    def calculate_employer_contribution(gross_salary, date)
      breakdown = calculate_social_security_breakdown(gross_salary, date)
      breakdown[:employer_social_security] || 0
    end
  end
end
