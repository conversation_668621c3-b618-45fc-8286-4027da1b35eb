# ActiveRpc

ActiveRpc is a module that provides a clean DSL for integrating gRPC services with ActiveRecord models. It automatically creates methods to access attributes from gRPC responses, making them feel like native model attributes.

## Usage

Include the `ActiveRpc` module in your model and use the `active_rpc` method to configure the gRPC integration:

```ruby
class Employee < ApplicationRecord
  include ActiveRpc

  # Configure the gRPC integration
  active_rpc :core, :User, [:name, :email], foreign_key: :user_id

  # The rest of your model code goes here...
end
```

This will create the following methods:

- `employee.name` - Returns the name from the gRPC response
- `employee.email` - Returns the email from the gRPC response
- `employee.user_data` - Returns the entire gRPC response
- `employee.reload_user` - Forces a reload of the user data from the gRPC service

## Configuration Options

The `active_rpc` method accepts the following parameters:

- `service` - The name of the gRPC service (e.g., `:core`)
- `resource` - The name of the resource (e.g., `:User`)
- `attributes` - An array of attributes to delegate (e.g., `[:name, :email]`)
- `options` - A hash of options:
  - `foreign_key` - The foreign key to use (default: `:"#{resource.to_s.underscore}_id"`)
  - `method_name` - The method name to use for accessing the gRPC response (default: `:"#{resource.to_s.underscore}_data"`)
  - `reload_method` - The method name to use for reloading the data (default: `:"reload_#{resource.to_s.underscore}"`)
  - `cache_expires_in` - The cache expiration time (default: `30.minutes`)

## Multiple Integrations

You can configure multiple gRPC integrations in the same model:

```ruby
class Order < ApplicationRecord
  include ActiveRpc

  # Configure the user integration
  active_rpc :core, :User, [:name, :email], foreign_key: :user_id

  # Configure the payment integration
  active_rpc :payments, :Transaction, [:amount, :status, :date], 
             foreign_key: :transaction_id,
             method_name: :transaction_details,
             reload_method: :refresh_transaction
end
```

## How It Works

The `active_rpc` method dynamically defines methods to access attributes from the gRPC response. It also adds callbacks to automatically load the data when needed and clear the cache when the foreign key changes.

The gRPC response is stored in an instance variable and is lazily loaded when an attribute is accessed. This ensures that the gRPC service is only called when necessary.

## Example

```ruby
# Get an employee
employee = Employee.find(1)

# Access user attributes (fetched from core service)
employee.name       # => "John Doe"
employee.email      # => "<EMAIL>"

# Get the entire gRPC response
response = employee.user_data

# Force reload user data from core service
employee.reload_user
```
