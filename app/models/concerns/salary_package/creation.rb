module SalaryPackage::Creation
  extend ActiveSupport::Concern

  class_methods do
    # Creates a new salary package with proper handling of existing packages
    # @param employee [Employee] the employee for whom to create the package
    # @param attributes [Hash] the attributes for the new package
    # @return [Array] [package, success] the created/updated package and a success flag
    def create_with_workflow(employee, attributes)
      # Step 1: Initialize draft package
      draft_package = employee.salary_packages.new(attributes)
      return [draft_package, false] unless draft_package.valid?

      # Step 2: Handle special case (same effective date)
      existing_package = find_existing_package_for_update(employee, draft_package.effective_date)
      if existing_package
        if existing_package.salary_calculations.empty?
          return update_existing_package(existing_package, attributes)
        else
          draft_package.errors.add(:effective_date, "A salary package with this effective date already exists and has been used for salary calculations")
          return [draft_package, false]
        end
      end

      # Step 3: Find any packages that would be active on the new package's effective date
      # and set their end_date to the day before the new package starts
      active_packages_on_effective_date = employee.salary_packages
        .where("effective_date <= ? AND (end_date IS NULL OR end_date >= ?)",
               draft_package.effective_date, draft_package.effective_date)
        .to_a

      # Set previous_package_id if there's an overlapping package
      if active_packages_on_effective_date.any?
        # Use the most recent active package as the previous package
        most_recent_package = active_packages_on_effective_date.max_by(&:effective_date)
        attributes = attributes.merge(previous_package_id: most_recent_package.id)

        # Set end_date for all overlapping packages
        active_packages_on_effective_date.each do |existing_package|
          # Skip if it's the same package (shouldn't happen, but just in case)
          next if existing_package.id == draft_package.id

          # Set end_date to the day before the new package starts
          transition_date = draft_package.effective_date - 1

          # Set end date and handle errors
          unless existing_package.set_end_date!(transition_date)
            existing_package.errors.each do |error|
              draft_package.errors.add(
                error.attribute,
                "Error updating existing package: #{error.message}"
              )
            end
            return [draft_package, false]
          end
        end
      end

      # Step 4: Save the new package
      begin
        package = employee.salary_packages.create!(attributes)
        [package, true]
      rescue ActiveRecord::RecordInvalid => e
        [e.record, false]
      end
    end

    private

    # Find an existing package with the same effective date that could be updated instead of creating a new one
    def find_existing_package_for_update(employee, effective_date)
      package_with_same_date = employee.salary_packages.where(effective_date: effective_date).first
      package_with_same_date
    end

    # Update an existing package instead of creating a new one
    def update_existing_package(existing_package, new_attributes)
      update_successful = existing_package.update(new_attributes)
      if update_successful
        [existing_package, true]
      else
        [existing_package, false]
      end
    end
  end
end
