module SalaryPackage::ApprovalConcern
  extend ActiveSupport::Concern

  included do
    attr_accessor :actor_user_id

    before_save :submit_if_pending

    acts_as_approvable

    def approval_action
      "approve_salary_package"
    end

    def system_name
      "people"
    end

    def on_approval_status_change(new_status, previous_status)
      Rails.logger.info("Salary package callback: status changed from #{previous_status} to #{new_status}")

      case new_status
      when "approved"
        approve_and_activate
      when "rejected"
        update!(status: :rejected)
      when "canceled"
        update!(status: :draft)
      end
    end

    def update_remote_status
      begin
        approval_request&.cancel!(actor_user_id)
        true
      rescue StandardError => e
        Rails.logger.error("Failed to update remote status: #{e.message}")
        errors.add(:base, "Failed to update remote status: #{e.message}")
        false
      end
    end

    # Prepare context for approval workflow
    def approval_context
      {
        employee_name: employee.name,
        base_salary: base_salary.to_s,
        total_package: total_package_value.to_s,
        effective_date: effective_date.to_s,
        creator_user_id: actor_user_id,
        target_user_id: employee.user_id
      }
    end

    # Instance method to handle package approval and activation
    def approve_and_activate
      self.class.transaction do
        # Update status to approved
        update!(status: :approved)
      end
    rescue ActiveRecord::RecordInvalid => e
      Rails.logger.error "SalaryPackage#approve_and_activate: Validation failed for package #{id}: #{e.message}"
      Rails.logger.error "SalaryPackage#approve_and_activate: Package details - employee: #{employee_id}, effective_date: #{effective_date}, status: #{status}"
      raise e
    rescue => e
      Rails.logger.error "SalaryPackage#approve_and_activate: Unexpected error for package #{id}: #{e.message}"
      Rails.logger.error "SalaryPackage#approve_and_activate: Backtrace: #{e.backtrace.first(5)}"
      raise e
    end

    private

    def submit_if_pending
      # Check if status is changing from draft to pending_approval
      if status_changed? && status_was == "draft" && status == "pending_approval" && !approval_request
        Rails.logger.info("Attempting to create approval workflow for salary package #{id}")

        begin
          result = submit_for_approval(actor_user_id, nil, approval_context)

          unless result
            error_message = approval_error || "Failed to create approval request"
            Rails.logger.error("Approval workflow creation failed: #{error_message}")
            errors.add(:base, error_message)
            throw :abort
          end

          Rails.logger.info("Approval workflow created successfully for salary package #{id}")
        rescue => e
          Rails.logger.error("Error submitting for approval: #{e.message}")
          Rails.logger.error("Backtrace: #{e.backtrace.first(5).join('\n')}")
          errors.add(:base, "Error submitting for approval: #{e.message}")
          throw :abort
        end
      end
    end
  end
end
