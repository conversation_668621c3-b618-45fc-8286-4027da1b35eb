module Employee::SearchConcern
  extend ActiveSupport::Concern

  included do
    include PgSearch::Model

    # Basic full-text search configuration for fallback when materialized view is not available
    pg_search_scope :full_text_search,
                    against: :department,
                    using: {
                      tsearch: { prefix: true },
                      trigram: { threshold: 0.1 }
                    }

    # Callback to update search data when employee is saved
    after_save :update_search_data
    after_create :update_search_data
  end

  # Method to search employees with their associated user data
  module ClassMethods
    def search_employees(query)
      # Check if we have the search data materialized view
      if defined?(EmployeeSearchData) && ActiveRecord::Base.connection.execute("SELECT to_regclass('employee_search_data')").first['to_regclass'].present?
        # Use the search data materialized view for full-text search
        search_results = EmployeeSearchData.full_text_search(query)

        # Get the employee IDs from the search results
        employee_ids = search_results.pluck(:employee_id)

        # Return the employees with those IDs
        Employee.where(id: employee_ids)
      else
        # Fall back to searching only local fields
        full_text_search(query)
      end
    end
  end

  def update_search_data
    # Check if EmployeeSearchData is defined
    if defined?(EmployeeSearchData)
      # Update the search data for this employee
      EmployeeSearchData.update_for_employee(self)
    end
  end
end
