# frozen_string_literal: true

# Concern for ActiveRpc configuration and RPC filtering scopes
module Employee::ActiveRpcConcern
  extend ActiveSupport::Concern

  # Constants
  DEFAULT_PASSWORD = "Password@123".freeze

  included do
    # Configure the gRPC integration based on actual Core service structure
    active_rpc :core, :User, foreign_key: :user_id do
      attribute :name, :string, default: "Local User"
      attribute :email, :string, default: "<EMAIL>"
      attribute :password, :string, default: DEFAULT_PASSWORD
      attribute :user_roles_list, :user_role_collection, default: []
      attribute :avatar_attributes, :avatar_attributes_type, default: {}

      # Define query configuration based on actual gRPC fields
      query_config(
        searchable: [ :name, :email, :status ],
        filterable: [ :name, :email, :status ],
        sortable: [ :name, :email, :status ],
        includable: [ :user_roles, :avatar_attributes ]
      )

      # Define available scopes
      scope :with_role, "Filter users by role name"
      scope :with_permission, "Filter users by permission name"
      scope :in_project, "Filter users by project"
      scope :in_projects, "Filter users by multiple projects"
    end

    # RPC field filtering scopes for Ransack integration
    # These scopes allow <PERSON><PERSON><PERSON> to filter on RPC attributes (name, email) using filter_rpc
    scope :name_eq, ->(value) { with_user_data.filter_rpc(name_eq: value) }
    scope :name_cont, ->(value) { with_user_data.filter_rpc(name_cont: value) }
    scope :name_start, ->(value) { with_user_data.filter_rpc(name_start: value) }
    scope :name_end, ->(value) { with_user_data.filter_rpc(name_end: value) }
    scope :name_in, ->(values) { with_user_data.filter_rpc(name_in: values) }

    scope :email_eq, ->(value) { with_user_data.filter_rpc(email_eq: value) }
    scope :email_cont, ->(value) { with_user_data.filter_rpc(email_cont: value) }
    scope :email_start, ->(value) { with_user_data.filter_rpc(email_start: value) }
    scope :email_end, ->(value) { with_user_data.filter_rpc(email_end: value) }
    scope :email_in, ->(values) { with_user_data.filter_rpc(email_in: values) }
  end

  module ClassMethods
    # Allow RPC filtering scopes to be used by Ransack
    def ransackable_scopes(auth_object = nil)
      %w[
        name_eq name_cont name_start name_end name_in
        email_eq email_cont email_start email_end email_in
      ]
    end

    def ransackable_attributes(auth_object = nil)
      (super(auth_object) + %w[name email]).uniq
    end
  end
end
