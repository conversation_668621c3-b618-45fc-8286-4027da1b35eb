module Employee::DepartmentConcern
  extend ActiveSupport::Concern

  included do
    # Constants
    const_set :DEPARTMENT_LABELS, {
      admin: "Administration",
      hr: "Human Resources",
      finance: "Finance",
      operations: "Operations",
      it: "Information Technology",
      programs: "Programs",
      procurement: "Procurement"
    }.freeze

    # Enums
    enum :department, {
      admin: 0,
      hr: 1,
      finance: 2,
      operations: 3,
      it: 4,
      programs: 5,
      procurement: 6
    }

    # Validations
    validates :department, presence: true

    # Scopes
    scope :by_department, ->(dept) { where(department: dept) }
  end

  # Methods
  def department_name
    self.class::DEPARTMENT_LABELS[department.to_sym] if department.present?
  end
end
