# frozen_string_literal: true

module Employee::ProfileFields
  extend ActiveSupport::Concern

  # Employee ID (formatted from existing id)
  def employee_id
    id.to_s.rjust(6, '0') # Format: 000001, 000002, etc.
  end

  # Job Title (from User model via gRPC or default)
  def job_title
    # Try to get from User model first, fallback to default
    begin
      # This would come from User model via gRPC in the future
      # For now, we'll use a default based on department or role
      case department&.to_s&.downcase
      when 'it'
        'Software Engineer'
      when 'hr'
        'HR Specialist'
      when 'finance'
        'Financial Analyst'
      when 'operations'
        'Operations Specialist'
      when 'admin'
        'Administrator'
      when 'programs'
        'Program Manager'
      when 'procurement'
        'Procurement Specialist'
      else
        'Employee'
      end
    rescue
      'Employee'
    end
  end

  # Position (alias for job_title)
  def position
    job_title
  end

  # Staff Function Code (based on department)
  def staff_function_code
    case department&.to_s&.downcase
    when 'it'
      'IT'
    when 'hr'
      'HR'
    when 'finance'
      'FIN'
    when 'operations'
      'OPS'
    when 'admin'
      'ADM'
    when 'programs'
      'PRG'
    when 'procurement'
      'PRC'
    else
      '000'
    end
  end

  # Workplace Code (default implementation)
  def workplace_code
    # This could be enhanced later with actual workplace data
    # For now, use a default based on some logic
    'HQ001' # Headquarters - Office 001
  end

  # Contract End Date (from current salary package)
  def contract_end_date
    salary_package&.end_date
  end

  # Helper method to get full employee profile
  def profile_summary
    {
      employee_id: employee_id,
      name: name,
      email: email,
      phone: phone,
      job_title: job_title,
      department: department,
      staff_function_code: staff_function_code,
      workplace_code: workplace_code,
      start_date: start_date,
      contract_end_date: contract_end_date,
      status: status
    }
  end

  # Contact information for display (no address)
  def contact_display_lines
    lines = []

    contact_line = []
    contact_line << phone if phone.present?
    contact_line << email if email.present?
    lines << contact_line.join(' | ') if contact_line.any?

    lines << Date.current.strftime('%d/%m/%Y')
    lines
  end
end
