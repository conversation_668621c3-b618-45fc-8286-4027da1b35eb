class SalaryPackage < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable
  include Athar::Commons::Models::Concerns::ActsAsApprovable
  include SalaryPackage::ApprovalConcern

  belongs_to :employee
  belongs_to :created_by, class_name: 'Employee'
  has_many :salary_calculations

  # Status enum for approval workflow (including cancelled status)
  enum :status, { draft: 0, pending_approval: 1, approved: 2, rejected: 3, cancelled: 4 }

  # Standard Rails validations
  validates :base_salary, presence: true, numericality: { greater_than: 0 }
  validates :effective_date, presence: true
  validates_date :end_date, on_or_after: :effective_date, message: "cannot be before effective date", allow_blank: true
  validate :no_duplicate_effective_date_with_calculations
  validate :no_active_package_with_calculations_on_create, on: :create
  validate :no_active_package_with_calculations_on_approval, if: :will_save_change_to_status_to_approved?
  validate :no_overlapping_approved_packages_on_same_date
  validate :only_one_draft_package_per_employee_per_creator
  validate :no_drafts_when_pending_exists
  validate :no_multiple_pending_packages
  validate :cannot_create_for_self, on: :create

  # Standard Rails callbacks
  before_save :set_previous_package_id, if: :new_record?
  after_update :handle_overlapping_packages, if: :saved_change_to_status_and_approved?
  after_update :end_previous_packages_if_approved, if: :saved_change_to_status_and_approved?
  after_update :cancel_competing_drafts, if: :saved_change_to_status_and_submitted?

  # Enhanced scopes with approval awareness (using enum scopes)
  scope :current, ->(date = Date.current) { approved.where("effective_date <= ? AND (end_date IS NULL OR end_date >= ?)", date, date) }
  scope :future, ->(date = Date.current) { approved.where("effective_date > ?", date) }
  scope :past, ->(date = Date.current) { approved.where("end_date < ?", date) }
  scope :historical, -> { approved.order(effective_date: :desc) }

  # Scope for employee visibility - only show approved packages to employees
  scope :visible_to_employee, -> { approved }

  # Administrative scopes (includes all statuses for admin purposes)
  scope :all_statuses_current, ->(date = Date.current) { where("effective_date <= ? AND (end_date IS NULL OR end_date >= ?)", date, date) }
  scope :all_statuses_future, ->(date = Date.current) { where("effective_date > ?", date) }
  scope :all_statuses_past, ->(date = Date.current) { where("end_date < ?", date) }

  # Creator-specific scopes
  scope :created_by_employee, ->(employee_id) { where(created_by_id: employee_id) }
  scope :draft_by_creator, ->(employee_id) { draft.where(created_by_id: employee_id) }
  scope :cancelled, -> { where(status: :cancelled) }
  scope :auto_cancelled, -> { cancelled.where("cancellation_reason LIKE ?", "%Superseded by package%") }
  scope :manually_cancelled, -> { cancelled.where.not("cancellation_reason LIKE ?", "%Superseded by package%") }

  # Methods
  def total_package_value
    base_salary + transportation_allowance.to_f + other_allowances.to_f
  end

  # Only approved packages are considered active
  def active?(date = Date.current)
    approved? && effective_date <= date && (end_date.nil? || end_date >= date)
  end

  # Check if package can be edited
  def editable?
    draft? || rejected?
  end

  # Check if package can be submitted for approval
  def submittable?
    draft? || rejected?
  end

  # Submit for approval (public interface)
  def submit!
    unless submittable?
      errors.add(:base, "Package is not submittable in current status")
      raise ActiveRecord::RecordInvalid.new(self)
    end

    self.status = :pending_approval
    self.submitted_at = Time.current

    # Use save instead of save! to allow callbacks to halt the operation
    unless save
      # If save failed, raise an error with the validation messages
      raise ActiveRecord::RecordInvalid.new(self)
    end
  end

  # Sets an end date for the salary package
  # Used when creating a new package to end the previous one
  # @param end_date [Date] the date when the package ends, defaults to current date
  # @return [Boolean] true if the package was successfully updated, false otherwise
  # @raise [ActiveRecord::RecordInvalid] if the record is invalid
  def set_end_date!(end_date = Date.current)
    if end_date < effective_date
      errors.add(:end_date, "cannot be earlier than effective date")
      return false
    end

    update!(end_date: end_date)
  rescue ActiveRecord::RecordInvalid => e
    errors.add(:base, e.message)
    false
  end

  # Creator-specific business logic methods
  def editable_by?(current_employee)
    return false unless editable?
    return true if created_by_id == current_employee&.id
    false
  end

  def visible_to?(current_employee, user_permissions = {})
    # Approved packages are visible to everyone with read permissions
    return true if approved?

    # Draft/pending packages only visible to creator
    return true if created_by_id == current_employee&.id

    # Admin users can see all packages
    return true if user_permissions[:manage_all] || user_permissions[:read_all]

    # Users with approval permissions can see pending packages
    return true if pending_approval? && user_permissions[:can_approve_salary_packages]

    false
  end

  def cancellable_by?(current_employee)
    return false unless cancellable?
    return true if created_by_id == current_employee&.id
    false
  end

  def cancellable?
    draft?
  end

  def cancel_draft!(reason = nil)
    return false unless cancellable?

    update!(
      status: :cancelled,
      cancelled_at: Time.current,
      cancellation_reason: reason || "Manually cancelled by user"
    )
    true
  rescue StandardError => e
    Rails.logger.error("Failed to cancel draft package #{id}: #{e.message}")
    false
  end

  def creator_name
    created_by&.name || 'Unknown'
  end

  def creator_department
    created_by&.department&.humanize || 'Unknown'
  end

  def created_by_employee_in_same_department?
    return false unless created_by && employee
    created_by.department == employee.department
  end

  private

  # Validation methods
  def will_save_change_to_status_to_approved?
    will_save_change_to_status? && status == 'approved'
  end

  # Validation: Prevent creating new packages when there's an active package with salary calculations
  def no_active_package_with_calculations_on_create
    return unless employee_id.present?

    active_packages_with_calculations = employee.salary_packages
      .approved
      .joins(:salary_calculations)
      .where(salary_calculations: { status: %w[pending approved] })
      .where('end_date IS NULL OR end_date >= ?', Date.current)

    if active_packages_with_calculations.exists?
      package_ids = active_packages_with_calculations.pluck(:id)
      errors.add(:base, "Cannot create new salary package. Employee has active approved packages with salary calculations (Package IDs: #{package_ids.join(', ')}). These packages are currently in use for payroll and cannot be modified.")
    end
  end

  # Validation: Prevent approving packages when there's an active package with salary calculations
  def no_active_package_with_calculations_on_approval
    return unless employee_id.present?

    active_packages_with_calculations = employee.salary_packages
      .approved
      .joins(:salary_calculations)
      .where(salary_calculations: { status: %w[pending approved] })
      .where('end_date IS NULL OR end_date >= ?', Date.current)
      .where.not(id: id) # Exclude current package

    if active_packages_with_calculations.exists?
      package_ids = active_packages_with_calculations.pluck(:id)
      errors.add(:base, "Cannot approve salary package. Employee has active approved packages with salary calculations (Package IDs: #{package_ids.join(', ')}). These packages are currently in use for payroll and cannot be modified.")
    end
  end

  def no_duplicate_effective_date_with_calculations
    return unless effective_date && employee_id

    existing_package = employee.salary_packages
                                   .where(effective_date: effective_date)
                                   .where.not(id: id)
                                   .joins(:salary_calculations)
                                   .first

    if existing_package
      errors.add(:effective_date, "A salary package with this effective date already exists and has been used for salary calculations")
    end
  end

  def no_overlapping_approved_packages_on_same_date
    return unless effective_date && approved? && employee_id

    overlapping_packages = SalaryPackage.where(employee_id: employee_id)
                                       .approved
                                       .where(effective_date: effective_date)
                                       .where.not(id: id)

    if overlapping_packages.exists?
      errors.add(:effective_date, "Cannot have multiple approved packages with the same effective date")
    end
  end

  def only_one_draft_package_per_employee_per_creator
    return unless draft? && employee_id && created_by_id

    existing_draft = SalaryPackage.where(
      employee_id: employee_id,
      created_by_id: created_by_id,
      status: :draft
    ).where.not(id: id).first

    if existing_draft
      errors.add(:base, "You can only have one draft salary package per employee at a time")
    end
  end

  def cannot_create_for_self
    return unless actor_user_id && employee&.user_id

    if actor_user_id.to_s == employee.user_id.to_s
      errors.add(:base, "Cannot create salary packages for yourself")
    end
  end

  def no_drafts_when_pending_exists
    return unless draft? && employee_id

    pending_exists = SalaryPackage.where(
      employee_id: employee_id,
      status: :pending_approval
    ).exists?

    if pending_exists
      errors.add(:employee_id, "already has a package pending approval")
    end
  end

  def no_multiple_pending_packages
    return unless pending_approval? && employee_id

    existing_pending = SalaryPackage.where(
      employee_id: employee_id,
      status: :pending_approval
    ).where.not(id: id).exists?

    if existing_pending
      errors.add(:employee_id, "already has a package pending approval")
    end
  end

  def set_previous_package_id
    return if previous_package_id.present?
    return unless employee # Guard clause to prevent errors if employee is not set

    most_recent_package = employee.salary_packages
                                 .approved
                                 .where("effective_date < ?", effective_date)
                                 .order(effective_date: :desc)
                                 .first

    self.previous_package_id = most_recent_package&.id
  end

  # Handle overlapping packages only when package becomes approved
  def handle_overlapping_packages
    SalaryPackage.transaction do
      # Find packages that overlap with this newly approved package
      overlapping_packages = employee.salary_packages
        .approved
        .where("effective_date <= ? AND (end_date IS NULL OR end_date >= ?)",
               effective_date, effective_date)
        .where.not(id: id)

      overlapping_packages.each do |existing_package|
        if existing_package.effective_date == effective_date
          # Special case: Same effective date - cancel the previous package
          # Business logic: New package completely replaces the old one
          Rails.logger.info "SalaryPackage#handle_overlapping_packages: Same effective date detected, cancelling previous package #{existing_package.id}"
          existing_package.update!(status: :cancelled)
        else
          # Normal case: Set end_date to day before new package's effective date
          transition_date = effective_date - 1.day
          Rails.logger.info "SalaryPackage#handle_overlapping_packages: Setting end_date=#{transition_date} on package #{existing_package.id}"
          existing_package.set_end_date!(transition_date)
        end
      end
    end
  end

  def saved_change_to_status_and_approved?
    saved_change_to_status? && approved?
  end

  # Automatically end previous approved packages when a new one is approved
  # This ensures the 'current' scope always returns exactly one package per employee
  def end_previous_packages_if_approved
    return unless employee_id.present?

    # Find all other approved packages for the same employee that don't have an end_date
    previous_packages = SalaryPackage.where(employee_id: employee_id)
                                   .approved
                                   .where(end_date: nil)
                                   .where.not(id: id)

    # Set their end_date to one day before this package's effective_date
    if previous_packages.exists? && effective_date.present?
      end_date_for_previous = effective_date - 1.day

      previous_packages.update_all(
        end_date: end_date_for_previous,
        updated_at: Time.current
      )

      Rails.logger.info("Auto-ended #{previous_packages.count} previous packages for employee #{employee_id}")
    end
  end

  def saved_change_to_status_and_submitted?
    saved_change_to_status? && pending_approval?
  end

  def cancel_competing_drafts
    # Find all other drafts for the same employee
    competing_drafts = SalaryPackage.where(
      employee_id: employee_id,
      status: :draft
    ).where.not(id: id)

    return unless competing_drafts.exists?

    # Cancel them with audit information
    competing_drafts.update_all(
      status: SalaryPackage.statuses[:cancelled],
      cancelled_at: Time.current,
      cancellation_reason: "Superseded by package ##{id} submitted by #{created_by&.name || 'Unknown'}",
      updated_at: Time.current
    )

    Rails.logger.info(
      "Auto-cancelled #{competing_drafts.count} competing drafts for employee #{employee_id} " \
      "when package #{id} was submitted by employee #{created_by_id}"
    )
  end
end
