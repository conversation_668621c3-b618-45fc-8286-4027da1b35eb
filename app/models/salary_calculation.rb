class SalaryCalculation < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable
  include Athar::Commons::Models::Concerns::ActsAsApprovable

  belongs_to :employee
  belongs_to :salary_package
  belongs_to :approved_by, class_name: 'Employee', optional: true

  has_one_attached :salary_slip_pdf

  has_many :calculation_details, class_name: 'SalaryCalculation::Detail', dependent: :destroy

  enum :status, { draft: 0, submitted: 1, approved: 2, paid: 3, rejected: 4 }

  include SalaryCalculation::ApprovalConcern
  include SalaryCalculation::ComputedFields

  validates :period_start_date, :period_end_date, presence: true
  validates :period, presence: true, format: {
    with: /\A(\d{4}-\d{2}(-\d{2})?)(_\d{4}-\d{2}-\d{2})?\z/,
    message: "must be in format YYYY-MM or YYYY-MM-DD_YYYY-MM-DD"
  }
  validates :gross_salary, presence: true, numericality: { greater_than: 0 }
  validates :net_salary, presence: true, numericality: { greater_than: 0 }
  validates :employee_id, uniqueness: {
    scope: [ :period_start_date, :period_end_date ],
    message: "already has a salary calculation for this period"
  }

  # Ensure calculations only use approved salary packages
  validate :salary_package_must_be_approved

  # Generate period string from dates if not provided
  before_validation :set_period_from_dates

  # Scopes
  scope :for_period, ->(period) { where(period: period) }
  scope :for_employee, ->(employee_id) { where(employee_id: employee_id) }
  scope :by_status, ->(status) { where(status: status) }

  # Generate salary slip after payment
  after_save :generate_salary_slip, if: -> { saved_change_to_status? && paid? }

  private

  def set_period_from_dates
    return if period.present? || period_start_date.blank? || period_end_date.blank?

    # If it's a full month (from 1st to last day)
    if period_start_date.day == 1 && period_end_date.day == period_end_date.end_of_month.day &&
       period_start_date.month == period_end_date.month &&
       period_start_date.year == period_end_date.year
      # Use YYYY-MM format
      self.period = "#{period_start_date.year}-#{period_start_date.month.to_s.rjust(2, '0')}"
    else
      # Use YYYY-MM-DD_YYYY-MM-DD format for custom periods
      self.period = "#{period_start_date.year}-#{period_start_date.month.to_s.rjust(2, '0')}-#{period_start_date.day.to_s.rjust(2, '0')}_#{period_end_date.year}-#{period_end_date.month.to_s.rjust(2, '0')}-#{period_end_date.day.to_s.rjust(2, '0')}"
    end
  end

  def generate_salary_slip
    # Generate PDF and attach to record
    Salary::SlipService.new(self).generate
  end

  def salary_package_must_be_approved
    if salary_package && !salary_package.approved?
      errors.add(:salary_package, "must be approved before calculations can be created")
    end
  end
end
