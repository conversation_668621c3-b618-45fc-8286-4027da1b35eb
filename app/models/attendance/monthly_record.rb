module Attendance
  class MonthlyRecord < ApplicationRecord
    include Athar::Commons::Models::Concerns::Ransackable

    # Use the table name created by migration
    self.table_name = 'attendance_monthly_records'

    belongs_to :employee

    validates :year, presence: true, numericality: { greater_than: 2020, less_than: 2100 }
    validates :month, presence: true, numericality: { in: 1..12 }
    validates :expected_hours, presence: true, numericality: { greater_than: 0 }
    validates :actual_hours, presence: true, numericality: { greater_than_or_equal_to: 0 }
    validates :deficit_hours, presence: true, numericality: { greater_than_or_equal_to: 0 }
    validates :year, uniqueness: { scope: [ :employee_id, :month ] }

    # Scopes
    scope :for_year, ->(year) { where(year: year) }
    scope :for_month, ->(month) { where(month: month) }
    scope :for_year_month, ->(year, month) { where(year: year, month: month) }
    scope :for_employee, ->(employee) { where(employee: employee) }
    scope :with_deficit, -> { where('deficit_hours > 0') }
    scope :recent, -> { order(year: :desc, month: :desc) }

    # Class methods for monthly calculation
    def self.calculate_for_employee_month(employee, year, month)
      record = find_or_initialize_by(employee: employee, year: year, month: month)

      start_date = Date.new(year, month, 1)
      end_date = start_date.end_of_month

      # Calculate actual work hours from periods
      work_periods = Attendance::Period.work_periods
                                      .where(employee: employee)
                                      .for_date_range(start_date, end_date)

      actual_hours = work_periods.sum { |period| period.duration_hours }

      # Calculate expected hours dynamically using WorkingDaysCalculator
      expected_hours = Attendance::WorkingDaysCalculator.monthly_expected_hours(year, month)

      # Calculate deficit
      deficit_hours = [ expected_hours - actual_hours, 0 ].max

      # Update record
      record.assign_attributes(
        expected_hours: expected_hours,
        actual_hours: actual_hours,
        deficit_hours: deficit_hours
      )

      record.save!
      record
    end

    def self.get_or_calculate(employee, year, month)
      existing = find_by(employee: employee, year: year, month: month)
      return existing if existing&.up_to_date?

      calculate_for_employee_month(employee, year, month)
    end

    def self.bulk_calculate_for_employee(employee, start_date, end_date)
      results = []

      current_date = start_date.beginning_of_month
      end_date = end_date.beginning_of_month

      while current_date <= end_date
        record = calculate_for_employee_month(employee, current_date.year, current_date.month)
        results << record
        current_date = current_date.next_month
      end

      results
    end

    # Instance methods
    def up_to_date?
      # Consider record up-to-date if updated within last hour
      # This prevents excessive recalculation during salary processing
      updated_at > 1.hour.ago
    end

    def month_name
      Date::MONTHNAMES[month]
    end

    def date_range
      start_date = Date.new(year, month, 1)
      end_date = start_date.end_of_month
      start_date..end_date
    end

    def has_deficit?
      deficit_hours > 0
    end

    def attendance_percentage
      return 100.0 if expected_hours == 0
      [ (actual_hours / expected_hours) * 100, 100.0 ].min.round(2)
    end

    def working_days_in_month
      Attendance::WorkingDaysCalculator.working_days_count(year, month)
    end

    def deficit_percentage
      return 0.0 if expected_hours == 0
      [ (deficit_hours / expected_hours) * 100, 100.0 ].min.round(2)
    end

    # Calculate salary deduction based on deficit
    def calculate_salary_deduction(base_salary)
      return 0 unless has_deficit?

      deduction_percentage = deficit_percentage / 100.0
      (base_salary * deduction_percentage).round(2)
    end

    # Get working days in the month (excluding weekends)
    def working_days_count
      weekend_days = Setting.attendance_weekend_days

      total_days = 0
      date_range.each do |date|
        total_days += 1 unless weekend_days.include?(date.wday)
      end

      total_days
    end

    # Get expected hours per working day
    def expected_hours_per_day
      return 0 if working_days_count == 0
      expected_hours / working_days_count
    end

    # Summary for reporting
    def summary
      {
        employee_name: employee.name,
        year: year,
        month: month,
        month_name: month_name,
        expected_hours: expected_hours,
        actual_hours: actual_hours,
        deficit_hours: deficit_hours,
        attendance_percentage: attendance_percentage,
        deficit_percentage: deficit_percentage,
        working_days: working_days_count,
        has_deficit: has_deficit?
      }
    end

    # Class method to get summary for multiple employees
    def self.monthly_summary(year, month, employee_ids = nil)
      scope = for_year_month(year, month)
      scope = scope.where(employee_id: employee_ids) if employee_ids.present?

      scope.includes(:employee).map(&:summary)
    end
  end
end
