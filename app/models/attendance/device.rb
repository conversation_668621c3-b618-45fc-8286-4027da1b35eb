module Attendance
  class Device < ApplicationRecord
    include Athar::Commons::Models::Concerns::Ransackable

    # Use the existing table name for compatibility
    self.table_name = 'attendance_devices'

    # Device status enum
    enum :status, {
      active: 0,
      inactive: 1,
      maintenance: 2,
      error: 3
    }

    # Associations
    has_many :sync_logs, class_name: 'Attendance::SyncLog', foreign_key: :attendance_device_id, dependent: :destroy
    has_many :events, class_name: 'Attendance::Event', foreign_key: :source_device_id, dependent: :nullify

    # Command execution associations
    has_many :command_executions, class_name: 'Attendance::CommandExecution', foreign_key: :device_id, dependent: :destroy

    # Legacy associations for backward compatibility
    has_many :attendance_sync_logs, class_name: 'Attendance::SyncLog', foreign_key: :attendance_device_id, dependent: :destroy
    has_many :attendance_events, class_name: 'Attendance::Event', foreign_key: :source_device_id, dependent: :nullify

    # Device mapping associations
    has_many :employee_device_mappings, foreign_key: :attendance_device_id, dependent: :destroy
    has_many :mapped_employees, through: :employee_device_mappings, source: :employee

    # Validations
    validates :name, presence: true, uniqueness: true
    validates :adapter_type, presence: true
    validates :ip_address, presence: true, if: :requires_network_connection?
    validates :port, presence: true, numericality: { in: 1..65535 }, if: :requires_network_connection?
    validates :location, length: { maximum: 255 }
    validate :validate_adapter_class_exists
    validate :validate_adapter_configuration

    # Scopes
    scope :active, -> { where(status: :active) }
    scope :by_adapter_type, ->(type) { where(adapter_type: type) }
    scope :by_location, ->(location) { where(location: location) }
    scope :network_devices, -> { where.not(adapter_type: 'file_import') }

    # Callbacks
    before_validation :set_defaults
    after_initialize :ensure_configs

    # Instance methods
    def requires_network_connection?
      !['file_import'].include?(adapter_type)
    end

    def adapter_class
      "Attendance::Adapters::#{adapter_type.camelize}Adapter".constantize
    rescue NameError
      raise "Adapter not found for adapter type: #{adapter_type}"
    end

    def create_adapter
      adapter_class.new(adapter_options)
    end

    def adapter_options
      base_options = {
        device_name: name,
        device_id: id,
        adapter_type: adapter_type
      }

      # Merge all connection config
      config = connection_config || {}

      # Add network details if this is a network device
      if requires_network_connection?
        base_options.merge!(
          device_ip: ip_address,
          device_port: port
        )
      end

      # Merge all configuration from JSONB
      base_options.merge(config.symbolize_keys)
    end

    def test_connection
      adapter = create_adapter
      adapter.test_connection
    rescue => e
      Rails.logger.error("Connection test failed for device #{name}: #{e.message}")
      false
    end

    def get_device_info
      adapter = create_adapter
      adapter.get_device_info
    rescue => e
      Rails.logger.error("Failed to get device info for #{name}: #{e.message}")
      {}
    end

    def sync_attendance_data(start_date = Date.yesterday, end_date = Date.today)
      adapter = create_adapter
      adapter.fetch_data(start_date, end_date)
    rescue => e
      Rails.logger.error("Sync failed for device #{name}: #{e.message}")
      []
    end

    def last_sync_log
      sync_logs.order(created_at: :desc).first
    end

    def last_successful_sync
      sync_logs.where(status: 'success').order(created_at: :desc).first
    end

    def sync_health_score
      recent_logs = sync_logs.where('created_at > ?', 7.days.ago)
      return 100 if recent_logs.empty?

      success_count = recent_logs.where(status: 'success').count
      total_count = recent_logs.count

      (success_count.to_f / total_count * 100).round(2)
    end

    def update_status_from_connection
      if test_connection
        update(status: :active, last_seen_at: Time.current)
      else
        update(status: :error)
      end
    end

    # Dynamic capability checking
    def supports_capability?(capability)
      capabilities[capability.to_s] || false
    end

    def get_capability(capability, default = nil)
      capabilities[capability.to_s] || default
    end

    def set_capability(capability, value)
      self.capabilities = capabilities.merge(capability.to_s => value)
      save! if persisted?
    end

    # Device mapping methods (multi-device support)

    # Find employee by device user ID on this specific device
    def find_employee_by_device_user_id(device_user_id)
      mapping = employee_device_mappings.find_by(device_user_id: device_user_id)
      mapping&.employee
    end

    # Get device user ID for specific employee on this device
    def device_user_id_for_employee(employee)
      mapping = employee_device_mappings.find_by(employee: employee)
      mapping&.device_user_id
    end

    # Get all user mappings for this device
    def user_mappings
      employee_device_mappings.includes(:employee)
    end

    # Check if a device user ID is mapped
    def device_user_id_mapped?(device_user_id)
      employee_device_mappings.exists?(device_user_id: device_user_id)
    end

    # Get unmapped device users (users on device but not in our system)
    def unmapped_device_users
      adapter = create_adapter
      return [] unless adapter.supports_user_management?

      device_users = adapter.get_users
      mapped_user_ids = employee_device_mappings.pluck(:device_user_id)

      device_users.reject { |user| mapped_user_ids.include?(user[:user_id]) }
    end

    # Get mapped device users (users on device that are in our system)
    def mapped_device_users
      adapter = create_adapter
      return [] unless adapter.supports_user_management?

      device_users = adapter.get_users
      mapped_user_ids = employee_device_mappings.pluck(:device_user_id)

      device_users.select { |user| mapped_user_ids.include?(user[:user_id]) }
    end

    private

    def set_defaults
      self.status ||= :active
    end

    def ensure_configs
      self.connection_config ||= {}
      self.sync_config ||= default_sync_config
      self.capabilities ||= {}
    end

    def default_sync_config
      {
        'sync_interval' => 30, # minutes
        'batch_size' => 1000,
        'retry_attempts' => 3,
        'retry_delay' => 60, # seconds
        'auto_sync_enabled' => true
      }
    end

    def validate_adapter_class_exists
      return unless adapter_type.present?

      # Validate that the adapter class exists
      begin
        adapter_class
      rescue => e
        errors.add(:adapter_type, "Invalid adapter type: #{e.message}")
      end
    end

    def validate_adapter_configuration
      return unless adapter_type.present?

      # Simple dynamic validation - just call the right method
      method_name = "validate_#{adapter_type}_config"
      if respond_to?(method_name, true)
        send(method_name)
      else
        errors.add(:adapter_type, "Unknown adapter type: #{adapter_type}")
      end
    end

    def validate_zkteco_config
      if requires_network_connection? && ip_address.blank?
        errors.add(:ip_address, "is required for ZKTeco devices")
      end
    end

    def validate_generic_http_config
      config = connection_config || {}
      if requires_network_connection? && ip_address.blank? && config['base_url'].blank?
        errors.add(:base, "Either ip_address or base_url in connection_config is required for Generic HTTP devices")
      end
    end

    def validate_file_import_config
      config = connection_config || {}
      if config['file_path'].blank?
        errors.add(:connection_config, "file_path is required for File Import devices")
      end
    end
  end
end
