# frozen_string_literal: true

module Attendance
  class DeviceCommand < Athar::Commons::ActiveStruct::Base
    include ValidationConcern
    include ExecutionConcern
    include ParameterExtractionConcern

    # Use name as the primary identifier
    with_id(auto_generate: false)

    # Basic attributes
    attribute :id, :string
    attribute :name, :string
    attribute :display_name, :string
    attribute :description, :string
    attribute :parameters

    # Device association
    belongs_to :device, class_name: 'Attendance::Device'

    def self.for_device(device)
      adapter = device.create_adapter

      return [] unless adapter.supports_commands?

      adapter.available_commands.map do |command_name|
        command_class = adapter.get_command_class(command_name)
        next unless command_class

        new(
          id: command_name,
          name: command_name,
          display_name: command_class.command_name.titleize,
          description: command_class.description,
          parameters: extract_parameter_definitions(command_class),
          device_id: device.id
        )
      end.compact
    end
  end
end
