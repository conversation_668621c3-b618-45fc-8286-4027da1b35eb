module Attendance
  class Event < ApplicationRecord
    include Athar::Commons::Models::Concerns::Ransackable
    include Attendance::Event::ValidationConcern
    include Attendance::Event::ResolutionConcern
    include Attendance::Event::StatisticsConcern

    # Use the existing table name for compatibility
    self.table_name = 'attendance_events'

    attr_accessor :skip_period_calculation

    belongs_to :employee
    belongs_to :source_device, class_name: 'Attendance::Device', foreign_key: :source_device_id, optional: true
    has_many :attendance_periods, through: :employee

    # Legacy association for backward compatibility
    belongs_to :attendance_device, class_name: 'Attendance::Device', foreign_key: :source_device_id, optional: true

    # Set default activity type based on event type
    before_validation :set_default_activity_type, if: :new_record?

    after_commit :schedule_period_calculation, on: [ :create, :update, :destroy ], unless: :skip_period_calculation

    # Event types
    enum :event_type, {
      check_in: 0,
      check_out: 1,
      undetermined: 2
    }

    # Activity types - with regular as default for check-ins
    enum :activity_type, {
      regular: 0,
      break: 1,
      lunch: 2,
      meeting: 3,
      business_trip: 4,
      work_from_home: 5,
      remote: 6,
      training: 7,
      personal_errand: 8
    }

    # Scopes for working with integer timestamps
    scope :for_date, ->(date) {
      start_of_day = date.beginning_of_day.to_i
      end_of_day = date.end_of_day.to_i
      where(timestamp: start_of_day..end_of_day)
    }

    scope :for_date_range, ->(start_date, end_date) {
      start_timestamp = start_date.beginning_of_day.to_i
      end_timestamp = end_date.end_of_day.to_i
      where(timestamp: start_timestamp..end_timestamp)
    }

    # Event type scopes
    scope :check_ins, -> { where(event_type: :check_in) }
    scope :check_outs, -> { where(event_type: :check_out) }
    scope :undetermined, -> { where(event_type: :undetermined) }
    scope :by_activity, ->(activity) { where(activity_type: activity) }
    scope :not_duplicates, -> { where(potential_duplicate: false) }
    scope :from_device, ->(device) { where(source_device: device) }
    scope :recent, -> { order(timestamp: :desc) }

    # Ransackers for date filtering
    ransacker :by_date do |parent|
      # This converts the unix timestamp to a date for filtering
      # SQL might vary by database - this assumes MySQL/MariaDB
      Arel.sql("DATE(FROM_UNIXTIME(timestamp))")
    end

    ransacker :timestamp_date do |parent|
      Arel.sql("DATE(FROM_UNIXTIME(timestamp))")
    end

    # Custom ransacker to support date range filtering
    # Usage: params[:q][:by_date_range] = [start_date.to_s, end_date.to_s]
    ransacker :by_date_range, args: [ :parent, :ransacker_args ] do |parent, args|
      start_date, end_date = args

      if start_date.present? && end_date.present?
        # Convert dates to timestamps for proper comparison
        start_timestamp = Time.zone.parse(start_date).beginning_of_day.to_i
        end_timestamp = Time.zone.parse(end_date).end_of_day.to_i

        parent.table[:timestamp].between(start_timestamp..end_timestamp)
      end
    end

    # Class methods for daily events
    def self.daily_events(employee, date)
      where(employee: employee).for_date(date).order(timestamp: :asc)
    end

    # Helper methods for working with integer timestamps

    # Convert the integer timestamp to a Time object
    def timestamp_as_time
      Time.zone.at(timestamp) if timestamp.present?
    end

    # Get the date portion of the timestamp
    def timestamp_date
      timestamp_as_time&.to_date
    end

    # Format the timestamp for display
    def formatted_timestamp(format = "%Y-%m-%d %H:%M:%S")
      timestamp_as_time&.strftime(format)
    end

    private

    # Set default activity type based on event type if not explicitly set
    def set_default_activity_type
      return if activity_type_changed? # Skip if activity type was explicitly set

      self.activity_type = case event_type&.to_sym
                           when :check_in
                             :regular # Check-ins are typically for regular work
                           when :check_out
                             # Find the previous event before this timestamp
                             previous_event = employee&.attendance_events
                                                &.where("timestamp < ?", timestamp)
                                                &.order(timestamp: :desc)
                                                &.first

                             if previous_event&.check_in?
                               :regular # If previous event was check-in, this is end of regular work
                             else
                               :break # Default for other check-outs
                             end
                           else
                             :break # Default for undetermined events
                           end
    end

    # Schedule period calculation after changes
    def schedule_period_calculation
      # Only schedule if we have a valid employee and timestamp
      return unless employee_id.present? && timestamp.present?

      # Get the date from the timestamp
      date = timestamp_date
      return unless date

      # Queue the background job
      Attendance::PeriodCalculationWorker.perform_async(employee_id, date.to_s)
    end
  end
end
