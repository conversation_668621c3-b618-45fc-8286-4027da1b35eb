# frozen_string_literal: true

module Attendance
  module Adapters
    class BaseCommand < Athar::Commons::ActiveStruct::Base
      without_id

      include ActiveModel::Validations

      def self.command_name
        raise NotImplementedError, "#{self} must implement .command_name"
      end

      def self.description
        raise NotImplementedError, "#{self} must implement .description"
      end

      def self.required_parameters
        raise NotImplementedError, "#{self} must implement .required_parameters"
      end

      def execute(adapter)
        return validation_error unless valid?
        perform_execution(adapter)
      end

      def validation_result
        if valid?
          { valid: true }
        else
          { valid: false, error: errors.full_messages.join(', ') }
        end
      end

      protected

      def perform_execution(adapter)
        raise NotImplementedError, "#{self.class} must implement #perform_execution"
      end

      def validation_error
        Attendance::CommandResult.failure(errors.full_messages.join(', '))
      end
    end
  end
end
