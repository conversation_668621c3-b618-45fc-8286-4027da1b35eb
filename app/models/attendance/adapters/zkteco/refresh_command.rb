# frozen_string_literal: true

module Attendance
  module Adapters
    module Zkteco
      class RefreshCommand < Attendance::Adapters::BaseCommand
        # No parameters needed for refresh

        def self.command_name
          'refresh'
        end

        def self.description
          'Refresh device data and settings on the ZKTeco device'
        end

        def self.required_parameters
          []  # No parameters
        end

        protected

        def perform_execution(adapter)
          adapter.with_connection do |zk_client|
            if zk_client.respond_to?(:refresh_data)
              result = zk_client.refresh_data
              adapter.log_info("Device data refreshed successfully")
              Attendance::CommandResult.success("Device data refreshed")
            else
              adapter.log_warn("Refresh command not supported by the current rbzk version")
              Attendance::CommandResult.failure("Operation not supported by the current device library")
            end
          end
        rescue => e
          adapter.log_error("Error refreshing device data", e)
          Attendance::CommandResult.failure(e.message)
        end
      end
    end
  end
end
