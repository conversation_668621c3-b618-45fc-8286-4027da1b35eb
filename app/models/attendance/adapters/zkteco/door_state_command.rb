# frozen_string_literal: true

module Attendance
  module Adapters
    module Zkteco
      class DoorStateCommand < Attendance::Adapters::BaseCommand
        # No parameters needed for door state query

        def self.command_name
          'door_state'
        end

        def self.description
          'Get the current door state from the ZKTeco device'
        end

        def self.required_parameters
          []  # No parameters
        end

        protected

        def perform_execution(adapter)
          adapter.with_connection do |zk_client|
            if zk_client.respond_to?(:get_door_state)
              state = zk_client.get_door_state
              adapter.log_info("Door state retrieved successfully")
              Attendance::CommandResult.success("Door state retrieved", { door_state: state })
            else
              adapter.log_warn("Door state command not supported by the current rbzk version")
              Attendance::CommandResult.failure("Operation not supported by the current device library")
            end
          end
        rescue => e
          adapter.log_error("Error getting door state", e)
          Attendance::CommandResult.failure(e.message)
        end
      end
    end
  end
end
