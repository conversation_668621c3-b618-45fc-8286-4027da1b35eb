# frozen_string_literal: true

module Attendance
  module Adapters
    module Zkteco
      class UnlockCommand < Attendance::Adapters::BaseCommand
        # Command parameters
        attribute :door_id, :integer, default: 1
        attribute :duration_seconds, :integer, default: 5

        validates :door_id, presence: true, numericality: {
          greater_than: 0,
          less_than_or_equal_to: 4,
          message: "Door ID must be between 1 and 4 for ZKTeco devices"
        }
        validates :duration_seconds, numericality: {
          in: 1..60,
          message: "Duration must be between 1 and 60 seconds"
        }

        def self.command_name
          'unlock'
        end

        def self.description
          'Unlock a specific door on the ZKTeco device'
        end

        def self.parameter_descriptions
          {
            'door_id' => 'Door identifier (1-4, where 1 is the main door and 2-4 are auxiliary doors)',
            'duration_seconds' => 'How long to keep the door unlocked in seconds'
          }
        end

        def self.required_parameters
          %w[door_id]  # duration_seconds has default
        end

        protected

        def perform_execution(adapter)
          adapter.with_connection do |zk_client|
            if zk_client.respond_to?(:unlock_door)
              result = zk_client.unlock_door
              adapter.log_info("Door unlocked successfully")
              Attendance::CommandResult.success("Door #{door_id} unlocked for #{duration_seconds} seconds")
            else
              adapter.log_warn("Unlock command not supported by the current rbzk version")
              Attendance::CommandResult.failure("Operation not supported by the current device library")
            end
          end
        rescue => e
          adapter.log_error("Error unlocking door", e)
          Attendance::CommandResult.failure(e.message)
        end
      end
    end
  end
end
