# frozen_string_literal: true

module Attendance
  module Adapters
    module Zkteco
      class ClearLcdCommand < Attendance::Adapters::BaseCommand
        # No parameters needed for clear LCD

        def self.command_name
          'clear_lcd'
        end

        def self.description
          'Clear the LCD display on the ZKTeco device'
        end

        def self.required_parameters
          []  # No parameters
        end

        protected

        def perform_execution(adapter)
          adapter.with_connection do |zk_client|
            if zk_client.respond_to?(:clear_lcd)
              result = zk_client.clear_lcd
              adapter.log_info("LCD cleared successfully")
              Attendance::CommandResult.success("LCD display cleared")
            else
              adapter.log_warn("Clear LCD command not supported by the current rbzk version")
              Attendance::CommandResult.failure("Operation not supported by the current device library")
            end
          end
        rescue => e
          adapter.log_error("Error clearing LCD", e)
          Attendance::CommandResult.failure(e.message)
        end
      end
    end
  end
end
