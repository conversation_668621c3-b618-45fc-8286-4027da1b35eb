# frozen_string_literal: true

module Attendance
  module Adapters
    module Zkteco
      class RestartCommand < Attendance::Adapters::BaseCommand
        # Command parameters
        attribute :delay_seconds, :integer, default: 0

        validates :delay_seconds, numericality: {
          greater_than_or_equal_to: 0,
          less_than_or_equal_to: 60,
          message: "ZKTeco devices support delay between 0 and 60 seconds"
        }

        def self.command_name
          'restart'
        end

        def self.description
          'Restart the ZKTeco attendance device'
        end

        def self.parameter_descriptions
          {
            'delay_seconds' => 'Delay before restarting the device in seconds (0-60, allows time for current operations to complete)'
          }
        end

        def self.required_parameters
          []  # delay_seconds has default
        end

        protected

        def perform_execution(adapter)
          adapter.with_connection do |zk_client|
            sleep(delay_seconds) if delay_seconds > 0

            if zk_client.respond_to?(:restart)
              result = zk_client.restart
              adapter.log_info("Device restart command sent successfully")
              Attendance::CommandResult.success("Device restarting")
            else
              adapter.log_warn("Restart command not supported by the current rbzk version")
              Attendance::CommandResult.failure("Operation not supported by the current device library")
            end
          end
        rescue => e
          adapter.log_error("Error restarting device", e)
          Attendance::CommandResult.failure(e.message)
        end
      end
    end
  end
end
