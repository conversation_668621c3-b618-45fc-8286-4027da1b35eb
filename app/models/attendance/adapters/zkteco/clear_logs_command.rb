# frozen_string_literal: true

module Attendance
  module Adapters
    module Zkteco
      class ClearLogsCommand < Attendance::Adapters::BaseCommand
        # Command parameters
        attribute :log_type, :string, default: 'attendance'
        attribute :confirm, :boolean, default: false

        validates :log_type, inclusion: {
          in: %w[attendance admin all],
          message: "Log type must be one of: attendance, admin, all"
        }
        validates :confirm, inclusion: {
          in: [ true ],
          message: "confirm parameter must be true to clear logs"
        }

        def self.command_name
          'clear_logs'
        end

        def self.description
          'Clear attendance logs from the ZKTeco device memory'
        end

        def self.parameter_descriptions
          {
            'log_type' => 'Type of logs to clear (attendance: employee check-in/out records, admin: administrative logs, all: everything)',
            'confirm' => 'Confirmation flag - must be true to proceed with clearing logs (safety measure)'
          }
        end

        def self.required_parameters
          %w[confirm]  # log_type has default, confirm required for safety
        end

        protected

        def perform_execution(adapter)
          raise "DANGEROUS !!!"

          adapter.with_connection do |zk_client|
            if zk_client.respond_to?(:clear_data)
              result = zk_client.clear_data
              adapter.log_info("Logs cleared successfully")
              Attendance::CommandResult.success("#{log_type.titleize} logs cleared successfully")
            else
              adapter.log_warn("Clear logs command not supported by the current rbzk version")
              Attendance::CommandResult.failure("Operation not supported by the current device library")
            end
          end
        rescue => e
          adapter.log_error("Error clearing logs", e)
          Attendance::CommandResult.failure(e.message)
        end
      end
    end
  end
end
