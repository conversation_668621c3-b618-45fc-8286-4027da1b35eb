# frozen_string_literal: true

module Attendance
  module Adapters
    module Zkteco
      class PoweroffCommand < Attendance::Adapters::BaseCommand
        # Command parameters
        attribute :confirm, :boolean, default: false

        validates :confirm, inclusion: {
          in: [ true ],
          message: "confirm parameter must be true to power off device"
        }

        def self.command_name
          'poweroff'
        end

        def self.description
          'Power off the ZKTeco attendance device'
        end

        def self.parameter_descriptions
          {
            'confirm' => 'Confirmation flag - must be true to proceed with powering off the device (safety measure)'
          }
        end

        def self.required_parameters
          %w[confirm]  # Safety requirement
        end

        protected

        def perform_execution(adapter)
          adapter.with_connection do |zk_client|
            if zk_client.respond_to?(:poweroff)
              result = zk_client.poweroff
              adapter.log_info("Device poweroff command sent successfully")
              Attendance::CommandResult.success("Device powering off")
            else
              adapter.log_warn("Poweroff command not supported by the current rbzk version")
              Attendance::CommandResult.failure("Operation not supported by the current device library")
            end
          end
        rescue => e
          adapter.log_error("Error powering off device", e)
          Attendance::CommandResult.failure(e.message)
        end
      end
    end
  end
end
