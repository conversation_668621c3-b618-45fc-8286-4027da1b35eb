# frozen_string_literal: true

module Attendance
  module Adapters
    module Zkteco
      class WriteLcdCommand < Attendance::Adapters::BaseCommand
        # Command parameters
        attribute :line_number, :integer, default: 1
        attribute :text, :string

        validates :line_number, presence: true, numericality: {
          greater_than: 0,
          less_than_or_equal_to: 4,
          message: "Line number must be between 1 and 4"
        }
        validates :text, presence: { message: "Text parameter is required" }
        validates :text, length: { maximum: 100, message: "Text must be 100 characters or less" }

        def self.command_name
          'write_lcd'
        end

        def self.description
          'Write a message to the ZKTeco device LCD display'
        end

        def self.parameter_descriptions
          {
            'line_number' => 'LCD line number to write to (1-4, where 1 is the top line)',
            'text' => 'Text message to display on the LCD screen'
          }
        end

        def self.required_parameters
          %w[text]  # line_number has default
        end

        protected

        def perform_execution(adapter)
          adapter.with_connection do |zk_client|
            if zk_client.respond_to?(:write_lcd)
              result = zk_client.write_lcd(line_number, text)
              adapter.log_info("Text written to LCD line #{line_number} successfully")
              Attendance::CommandResult.success("Text written to LCD line #{line_number}: #{text}")
            else
              adapter.log_warn("Write LCD command not supported by the current rbzk version")
              Attendance::CommandResult.failure("Operation not supported by the current device library")
            end
          end
        rescue => e
          adapter.log_error("Error writing to LCD", e)
          Attendance::CommandResult.failure(e.message)
        end
      end
    end
  end
end
