# frozen_string_literal: true

module Attendance
  class DeviceUser < Athar::Commons::ActiveStruct::Base
    # Disable auto-generated ID
    with_id(auto_generate: false)

    # Use user_id as the primary identifier (integer for proper sorting)
    attribute :id, :integer

    # Device user attributes from the device (only what's actually available)
    attribute :user_id, :integer
    attribute :name, :string
    attribute :privilege, :integer
    attribute :password, :string
    attribute :group_id, :string
    attribute :card_number, :integer

    belongs_to :device, class_name: 'Attendance::Device'
    belongs_to :mapping, class_name: 'Attendance::EmployeeDeviceMapping',
               foreign_key: :user_id, primary_key: :device_user_id, optional: true
    has_one :employee, through: :mapping

    # Delegations
    delegate :name, :id, to: :employee, prefix: true, allow_nil: true
    delegate :id, :notes, to: :mapping, prefix: true, allow_nil: true

    # Mapping helper methods
    def mapped?
      mapping.present?
    end

    # Helper methods
    def admin?
      privilege == 14 # Based on the actual data showing privilege: 14
    end

    def user?
      privilege == 0
    end

    def has_card?
      card_number.present? && card_number > 0
    end

    def has_password?
      password.present?
    end

    def verification_methods
      methods = []
      methods << 'password' if has_password?
      methods << 'card' if has_card?
      methods
    end

    # Class method to create from device hash with device context
    def self.from_device_hash(hash, device_data)
      new(
        id: hash[:user_id].to_i, # Use user_id as the ID for JSON:API (integer for proper sorting)
        user_id: hash[:user_id].to_i, # Convert to integer for proper sorting
        name: hash[:name],
        privilege: hash[:privilege],
        password: hash[:password],
        group_id: hash[:group_id],
        card_number: hash[:card_number],
        device_id: device_data[:id] # belongs_to will automatically handle the association
      )
    end

    # Ransack support
    def self.ransackable_attributes(auth_object = nil)
      %w[id user_id name privilege password group_id card_number device_id]
    end

    def self.ransackable_associations(auth_object = nil)
      %w[device employee_device_mappings]
    end
  end
end
