class Attendance::EmployeeDeviceMapping < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable

  belongs_to :employee
  belongs_to :attendance_device, class_name: 'Attendance::Device'

  validates :device_user_id, presence: true
  validates :employee_id, uniqueness: {
    scope: :attendance_device_id,
    message: "is already mapped to this device"
  }
  validates :device_user_id, uniqueness: {
    scope: :attendance_device_id,
    message: "is already taken on this device"
  }

  # Scopes
  scope :for_device, ->(device) { where(attendance_device: device) }
  scope :for_employee, ->(employee) { where(employee: employee) }
  scope :with_employee_data, -> { includes(:employee) }
  scope :with_device_data, -> { includes(:attendance_device) }
  scope :with_full_data, -> { includes(:employee, :attendance_device) }

  # Class methods
  def self.find_employee_for_device(device, device_user_id)
    mapping = find_by(attendance_device: device, device_user_id: device_user_id)
    mapping&.employee
  end

  def self.find_device_user_id_for_employee(employee, device)
    mapping = find_by(employee: employee, attendance_device: device)
    mapping&.device_user_id
  end

  # Find device users that don't have employee mappings
  def self.unmapped_device_users(device)
    return [] unless device.create_adapter.supports_user_management?

    device_users = device.create_adapter.get_users
    mapped_user_ids = where(attendance_device: device).pluck(:device_user_id)

    device_users.reject { |user| mapped_user_ids.include?(user[:user_id].to_s) }
  rescue => e
    Rails.logger.error("Error getting unmapped device users: #{e.message}")
    []
  end

  # Find employees that don't have mappings for a specific device
  def self.unmapped_employees(device)
    mapped_employee_ids = where(attendance_device: device).pluck(:employee_id)
    Employee.where.not(id: mapped_employee_ids)
  end

  # Instance methods
  def device_name
    attendance_device.name
  end

  def employee_name
    employee.name
  end

  def to_s
    "#{employee_name} → Device #{device_name} (User ID: #{device_user_id})"
  end
end
