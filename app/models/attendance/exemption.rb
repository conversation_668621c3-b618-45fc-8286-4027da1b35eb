module Attendance
  class Exemption < ApplicationRecord
    include Athar::Commons::Models::Concerns::Ransackable

    # Use the existing table naming convention
    self.table_name = 'attendance_exemptions'

    # Validations
    validates :name, presence: true
    validates :start_date, :end_date, presence: true
    validates :exemption_type, presence: true
    validate :end_date_after_start_date

    # Exemption types
    enum :exemption_type, {
      holiday: 0, # National/religious holidays
      company_closure: 1, # Office maintenance, renovations
      emergency_closure: 2, # Weather, emergencies
      system_maintenance: 3 # IT maintenance affecting attendance
    }

    # Control flags
    validates :is_active, inclusion: { in: [ true, false ] }
    validates :affects_salary, inclusion: { in: [ true, false ] }
    validates :affects_attendance, inclusion: { in: [ true, false ] }

    # Scopes
    scope :active, -> { where(is_active: true) }
    scope :for_date_range, ->(start_date, end_date) {
      where('start_date <= ? AND end_date >= ?', end_date, start_date)
    }
    scope :affecting_attendance, -> { where(affects_attendance: true) }
    scope :affecting_salary, -> { where(affects_salary: true) }
    scope :for_date, ->(date) { where('start_date <= ? AND end_date >= ?', date, date) }

    # Helper methods
    def duration_days
      (end_date - start_date).to_i + 1
    end

    def exemption_type_label
      I18n.t("attendance.exemption.exemption_types.#{exemption_type}", default: exemption_type.humanize)
    end

    def covers_date?(date)
      date >= start_date && date <= end_date
    end

    private

    def end_date_after_start_date
      return unless start_date && end_date

      if end_date < start_date
        errors.add(:end_date, "must be after or equal to start date")
      end
    end
  end
end
