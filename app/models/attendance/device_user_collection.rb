# frozen_string_literal: true

module Attendance
  class DeviceUserCollection
    include Athar::Commons::ActiveStruct::Collection

    collection_item_class DeviceUser

    # Helper methods for mapping statistics
    def mapped_count
      count(&:mapped?)
    end

    def unmapped_count
      count { |user| !user.mapped? }
    end

    def mapping_statistics
      {
        total: size,
        mapped: mapped_count,
        unmapped: unmapped_count,
        mapping_percentage: size > 0 ? (mapped_count.to_f / size * 100).round(2) : 0
      }
    end
  end
end
