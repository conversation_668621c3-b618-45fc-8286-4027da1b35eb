# frozen_string_literal: true

module Attendance
  class DeviceCommand
    module ParameterExtractionConcern
      extend ActiveSupport::Concern

      class_methods do
        def extract_parameter_definitions(command_class)
          parameters = {}

          command_class.attribute_names.each do |attr_name|
            # Skip internal attributes
            next if %w[adapter_type device_id].include?(attr_name)

            attribute_definition = command_class.attribute_types[attr_name]
            parameters[attr_name] = {
              type: attribute_definition.type,
              required: command_required_parameter?(command_class, attr_name),
              default: extract_default_value(command_class, attr_name),
              description: extract_parameter_description_from_model(command_class, attr_name)
            }
          end

          parameters
        end

        def command_required_parameter?(command_class, attr_name)
          command_class.required_parameters.include?(attr_name)
        end

        def extract_default_value(command_class, attr_name)
          # Try to get default from ActiveStruct attribute definition
          begin
            # Create a temporary instance to get the default value
            temp_instance = command_class.new
            default_value = temp_instance.send(attr_name)

            # Only return non-nil defaults or explicit false values
            return default_value if default_value != nil || default_value == false
          rescue
            # If we can't get the default, return nil
          end

          nil
        end

        def extract_parameter_description_from_model(command_class, attr_name)
          # Check if the command class defines parameter descriptions
          if command_class.respond_to?(:parameter_descriptions)
            descriptions = command_class.parameter_descriptions
            return descriptions[attr_name] if descriptions[attr_name]
          end

          # Check if the command class defines a specific method for this parameter
          description_method = "#{attr_name}_description"
          if command_class.respond_to?(description_method)
            return command_class.send(description_method)
          end

          # Fallback: generate description from attribute name and validations
          generate_parameter_description(command_class, attr_name)
        end

        def generate_parameter_description(command_class, attr_name)
          # Create a temporary instance to inspect validations
          temp_instance = command_class.new
          temp_instance.valid? # Trigger validations

          # Get validation details for this attribute
          validations = temp_instance.errors.details[attr_name.to_sym] || []

          # Generate description based on attribute name and validations
          base_description = attr_name.humanize.downcase

          # Add validation hints
          validation_hints = []
          validations.each do |validation|
            case validation[:error]
            when :inclusion
              if validation[:value]
                validation_hints << "must be one of: #{validation[:value].join(', ')}"
              end
            when :numericality
              if validation[:greater_than]
                validation_hints << "must be greater than #{validation[:greater_than]}"
              end
              if validation[:less_than_or_equal_to]
                validation_hints << "must be #{validation[:less_than_or_equal_to]} or less"
              end
            when :length
              if validation[:maximum]
                validation_hints << "maximum #{validation[:maximum]} characters"
              end
            end
          end

          description = base_description
          description += " (#{validation_hints.join(', ')})" if validation_hints.any?
          description.capitalize
        end
      end
    end
  end
end
