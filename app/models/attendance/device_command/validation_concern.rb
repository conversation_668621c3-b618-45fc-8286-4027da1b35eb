# frozen_string_literal: true

module Attendance
  class DeviceCommand
    module ValidationConcern
      extend ActiveSupport::Concern

      class_methods do
        def validate_for_device(device, command_name, parameters = {})
          adapter = device.create_adapter

          unless adapter.supports_commands?
            return { valid: false, error: "Device does not support commands" }
          end

          command_class = adapter.get_command_class(command_name)
          unless command_class
            return { valid: false, error: "Command '#{command_name}' not supported by #{device.adapter_type} adapter" }
          end

          command = command_class.new(parameters)
          command.validation_result
        end
      end
    end
  end
end
