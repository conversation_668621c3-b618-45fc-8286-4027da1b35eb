# frozen_string_literal: true

module Attendance
  class DeviceCommand
    module ExecutionConcern
      extend ActiveSupport::Concern

      class_methods do
        def execute_for_device(device, command_name, parameters = {}, employee = nil)
          # Create execution record
          execution = create_execution_record(device, command_name, parameters, employee)

          begin
            # Execute command on adapter
            adapter = device.create_adapter

            # Check if adapter supports commands
            unless adapter.supports_commands?
              result = Attendance::CommandResult.failure("Device does not support commands")
              mark_execution_failed(execution, result)
              return result
            end

            # Execute command using adapter's command discovery
            result = adapter.execute_command(command_name, parameters)

            # Update execution record based on result
            mark_execution_completed(execution, result)

            result
          rescue => e
            result = Attendance::CommandResult.failure(e.message)
            mark_execution_failed(execution, result)
            result
          end
        end

        private

        def create_execution_record(device, command_name, parameters, employee)
          Attendance::CommandExecution.create!(
            device: device,
            command_name: command_name,
            parameters: parameters,
            status: :running,
            executed_by: employee,
            started_at: Time.current
          )
        end

        def mark_execution_completed(execution, result)
          execution.update!(
            status: result.success ? :completed : :failed,
            result: result.as_json,
            completed_at: Time.current
          )
        end

        def mark_execution_failed(execution, result)
          execution.update!(
            status: :failed,
            result: result.as_json,
            completed_at: Time.current
          )
        end
      end
    end
  end
end
