module Attendance
  class Summary < ApplicationRecord
    include Athar::Commons::Models::Concerns::Ransackable

    # Use the existing table name for compatibility
    self.table_name = 'attendance_summaries'

    belongs_to :employee

    validates :date, presence: true
    validates :employee_id, uniqueness: { scope: :date }

    # Scopes
    scope :for_date, ->(date) { where(date: date) }
    scope :for_date_range, ->(start_date, end_date) { where(date: start_date..end_date) }
    scope :for_employee, ->(employee) { where(employee: employee) }

    # Class method to recalculate summary
    def self.recalculate_for(employee, date)
      summary = find_or_initialize_by(employee: employee, date: date)

      events = ::Attendance::Event.daily_events(employee, date)

      # For first check-in, use the first event if it's a check-in or undetermined
      first_event = events.first
      if first_event && (first_event.check_in? || first_event.undetermined?)
        summary.first_check_in = first_event.timestamp_as_time
      else
        summary.first_check_in = events.check_ins.first&.timestamp_as_time
      end

      # For last check-out, use the last event if it's a check-out or undetermined
      last_event = events.last
      if last_event && (last_event.check_out? || last_event.undetermined?)
        summary.last_check_out = last_event.timestamp_as_time
      else
        summary.last_check_out = events.check_outs.last&.timestamp_as_time
      end

      summary.total_duration_minutes = calculate_daily_duration(employee, date, events)
      summary.work_status = calculate_daily_status(employee, date, events)
      summary.event_count = events.count
      summary.undetermined_count = events.undetermined.count
      summary.save

      summary
    end

    # Batch recalculation for multiple employees/dates
    def self.recalculate_for_date_range(employee, start_date, end_date)
      (start_date..end_date).each do |date|
        recalculate_for(employee, date)
      end
    end

    def self.recalculate_for_all_employees(date)
      Employee.active.find_each do |employee|
        recalculate_for(employee, date)
      end
    end

    # Instance methods
    def formatted_duration
      return "0h 0m" unless total_duration_minutes

      hours = total_duration_minutes / 60
      minutes = total_duration_minutes % 60

      if hours > 0
        "#{hours}h #{minutes}m"
      else
        "#{minutes}m"
      end
    end

    def duration_hours
      return 0 unless total_duration_minutes
      (total_duration_minutes / 60.0).round(2)
    end

    def work_status_label
      case work_status
      when 'present'
        'Present'
      when 'absent'
        'Absent'
      when 'partial'
        'Partial Day'
      when 'late'
        'Late Arrival'
      when 'early_departure'
        'Early Departure'
      else
        work_status&.humanize || 'Unknown'
      end
    end

    def has_undetermined_events?
      undetermined_count && undetermined_count > 0
    end

    # Make the model ransackable
    def self.ransackable_attributes(auth_object = nil)
      %w[id employee_id date first_check_in last_check_out total_duration_minutes work_status event_count undetermined_count notes created_at updated_at]
    end

    def self.ransackable_associations(auth_object = nil)
      %w[employee]
    end

    private

    def self.calculate_daily_duration(employee, date, events = nil)
      events ||= ::Attendance::Event.daily_events(employee, date)
      return 0 if events.empty?

      total_duration = 0
      current_check_in = nil

      events.each do |event|
        case event.event_type
        when 'check_in'
          current_check_in = event.timestamp
        when 'check_out'
          if current_check_in
            duration = event.timestamp - current_check_in
            total_duration += duration
            current_check_in = nil
          end
        when 'undetermined'
          # For undetermined events, try to infer the type
          inferred_type = event.infer_event_type_by_sequence
          if inferred_type == 'check_in'
            current_check_in = event.timestamp
          elsif inferred_type == 'check_out' && current_check_in
            duration = event.timestamp - current_check_in
            total_duration += duration
            current_check_in = nil
          end
        end
      end

      # Convert seconds to minutes
      (total_duration / 60).round
    end

    def self.calculate_daily_status(employee, date, events = nil)
      events ||= ::Attendance::Event.daily_events(employee, date)

      return 'absent' if events.empty?

      # Check if there are any check-ins or inferred check-ins
      has_check_in = events.any? { |e| e.check_in? || e.infer_event_type_by_sequence == 'check_in' }

      return 'absent' unless has_check_in

      # Calculate total duration
      duration_minutes = calculate_daily_duration(employee, date, events)

      # Determine status based on duration and events
      if duration_minutes >= 480 # 8 hours
        'present'
      elsif duration_minutes >= 240 # 4 hours
        'partial'
      elsif events.first&.timestamp_as_time&.hour.to_i >= 10 # Late arrival (after 10 AM)
        'late'
      else
        'partial'
      end
    end
  end
end
