# frozen_string_literal: true
require 'securerandom'

module Salary
  class CalculationResult < Athar::Commons::ActiveStruct::Base
    # Disable auto-generated ID
    with_id(auto_generate: false)

    # Basic attributes
    attribute :success, array: true, default: []
    attribute :failure, array: true, default: []

    # Helper methods
    def add_success(employee_id, calculation_id)
      success << {
        id: "success_#{employee_id}_#{calculation_id}",
        employee_id: employee_id,
        calculation_id: calculation_id
      }
    end

    def add_failure(employee_id, errors)
      failure_entry = {
        id: "failure_#{employee_id}_#{SecureRandom.hex(4)}",
        employee_id: employee_id,
        errors: errors
      }
      failure << failure_entry
    end

    def success_count
      success.size
    end

    def failure_count
      failure.size
    end

    def total_count
      success_count + failure_count
    end

    # Methods for serializer relationships
    def successes
      success
    end

    def failures
      failure
    end
  end
end
