class ApprovalRequest < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable

  belongs_to :requestor, class_name: "Employee"
  belongs_to :approvable, polymorphic: true

  has_many :approval_steps, dependent: :destroy
  has_many :approval_actions, dependent: :destroy

  include Athar::Commons::Models::Concerns::ApprovalOperations

  enum :status, { pending: 0, approved: 1, rejected: 2, canceled: 3 }

  validates :workflow_id, presence: true
  validates :workflow_name, presence: true
  validates :requestor_id, presence: true
  validates :status, presence: true

  after_create :create_steps_from_data

  private

  def create_steps_from_data
    return unless steps_data.is_a?(Array)

    steps_data.each do |step_data|
      approval_steps.create!(
        step_id: step_data['step_id'],
        name: step_data['name'],
        sequence: step_data['sequence'],
        approval_type: step_data['approval_type'],
        approver_ids: step_data['approver_ids']
      )
    end
  end
end
