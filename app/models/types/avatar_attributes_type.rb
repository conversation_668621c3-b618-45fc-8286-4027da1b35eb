require_relative '../employees/avatar_struct'

module Types
  # Custom ActiveModel type specifically for handling avatar_attributes
  # This type allows storing binary image data in avatar_attributes without
  # attempting to cast them to JSON, which would fail for binary content
  class AvatarAttributesType < ActiveModel::Type::Value
    # Cast input to the appropriate format for database storage
    # Convert input to AvatarStruct
    def cast(value)
      return nil if value.nil?

      # If it's already an AvatarStruct, return it
      return value if value.is_a?(Employees::AvatarStruct)

      # If it's an uploaded file, convert it to AvatarStruct
      if value.respond_to?(:read) && value.respond_to?(:original_filename)
        return Employees::AvatarStruct.from_uploaded_file(value)
      end

      # If it's a hash, convert it to AvatarStruct
      if value.is_a?(Hash)
        return Employees::AvatarStruct.new(value)
      end

      # Default fallback
      Employees::AvatarStruct.new
    end

    # Serialize the value for JSON representation
    # This method is called when converting the attribute to JSON
    def serialize(value)
      return {} if value.nil?

      # Convert to hash if it's an AvatarStruct
      if value.is_a?(Employees::AvatarStruct)
        return value.as_json
      end

      # Process the hash
      if value.is_a?(Hash)
        return value
      end

      # Default fallback
      {}
    end

    # Deserialize from database format to Ruby object
    # This handles converting from the serialized format back to the original
    def deserialize(value)
      return Employees::AvatarStruct.new if value.nil?

      # If it's already an AvatarStruct, return it
      return value if value.is_a?(Employees::AvatarStruct)

      # If the value is a JSON string, parse it
      if value.is_a?(String)
        begin
          value = JSON.parse(value)
        rescue JSON::ParserError
          return Employees::AvatarStruct.new
        end
      end

      # Convert hash to AvatarStruct
      if value.is_a?(Hash)
        return Employees::AvatarStruct.new(value)
      end

      # Default fallback
      Employees::AvatarStruct.new
    end
  end
end
