class TaxConfig < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable

  validates :name, presence: true
  validates :config_data, presence: true
  validates :effective_date, presence: true

  scope :active_at, ->(date) { where("effective_date <= ? AND (end_date IS NULL OR end_date >= ?)", date, date) }

  def self.current(date = Date.today)
    active_at(date).order(effective_date: :desc).first
  end

  # Make the model ransackable
  def self.ransackable_attributes(auth_object = nil)
    %w[id name effective_date end_date created_at updated_at]
  end

  def self.ransackable_associations(auth_object = nil)
    []
  end
end
