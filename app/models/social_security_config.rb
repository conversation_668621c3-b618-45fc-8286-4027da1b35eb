class SocialSecurityConfig < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable

  validates :employee_rate, presence: true, numericality: { greater_than: 0, less_than: 100 }
  validates :employer_rate, presence: true, numericality: { greater_than: 0, less_than: 100 }
  validates :effective_date, presence: true

  scope :active_at, ->(date) { where("effective_date <= ? AND (end_date IS NULL OR end_date >= ?)", date, date) }

  def self.current(date = Date.today)
    active_at(date).order(effective_date: :desc).first
  end

  # Make the model ransackable
  def self.ransackable_attributes(auth_object = nil)
    %w[id employee_rate employer_rate max_salary effective_date end_date created_at updated_at]
  end

  def self.ransackable_associations(auth_object = nil)
    []
  end
end
