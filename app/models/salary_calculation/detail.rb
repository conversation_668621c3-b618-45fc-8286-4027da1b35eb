class SalaryCalculation::Detail < ApplicationRecord
  self.table_name = 'salary_calculation_details'

  belongs_to :salary_calculation

  validates :detail_type, presence: true # 'base', 'addition', 'deduction'
  validates :category, presence: true # e.g., 'base_salary', 'social_security', 'tax', 'leave'
  validates :amount, presence: true
  validates :description, presence: true

  # Optional reference to related records (e.g., leave_id for leave deductions)
  belongs_to :reference, polymorphic: true, optional: true
end
