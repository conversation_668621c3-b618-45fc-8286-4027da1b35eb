# frozen_string_literal: true

module Statistics
  class MetricCard < Athar::Commons::ActiveStruct::Base
    # Disable auto-generated ID since we'll use specific card IDs
    with_id(auto_generate: false)

    # Basic attributes
    attribute :id, :string
    attribute :title, :string
    attribute :value, :string
    attribute :unit, :string

    # Comparison attributes
    attribute :comparison_percentage, :float
    attribute :trend, :string
    attribute :comparison_text, :string

    # Helper methods
    def comparison
      {
        percentage: comparison_percentage,
        trend: trend,
        text: comparison_text
      }
    end
  end
end
