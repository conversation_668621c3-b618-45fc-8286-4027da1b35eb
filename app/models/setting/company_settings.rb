module Setting::CompanySettings
  extend ActiveSupport::Concern

  class_methods do
    # Company settings helper methods
    def company_name
      get('company', 'name', 'ATHAR')
    end

    def company_tagline
      get('company', 'tagline', 'Empowering Communities Through Innovation')
    end

    def company_address
      get('company', 'address', '<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Bldg.55')
    end

    def company_phone
      phone_string = get('company', 'phone', '+962780803959')
      Phonelib.parse(phone_string)
    end

    def company_email
      get('company', 'email', '<EMAIL>')
    end

    def company_logo_path
      get('company', 'logo_path', 'app/assets/images/slip_athar_logo.png')
    end
  end
end
