module Setting::TypeHandlers
  class StringHandler < BaseHandler
    def self.handles_type?(type)
      %w[string url].include?(type.to_s)
    end

    def self.parse(value, setting = nil)
      value.to_s.strip
    end

    def self.normalize(input, setting = nil)
      input.to_s.strip
    end

    def self.default_value
      ''
    end

    def self.display_name
      'Text'
    end

    def self.validation_rules
      {
        type: 'string',
        example: 'Sample text'
      }
    end

    # Enhanced validation rules based on specific string type
    def self.validation_rules_for_subtype(subtype)
      case subtype.to_s
      when 'email'
        {
          type: 'email',
          pattern: '^[\\w+\\-.]+@[a-z\\d\\-]+(\\.[a-z\\d\\-]+)*\\.[a-z]+$',
          example: '<EMAIL>'
        }
      when 'phone'
        {
          type: 'phone',
          pattern: '^\\+?[\\d\\s\\-\\(\\)]+$',
          example: '+1234567890'
        }
      when 'url'
        {
          type: 'url',
          example: 'https://example.com'
        }
      else
        validation_rules
      end
    end
  end
end
