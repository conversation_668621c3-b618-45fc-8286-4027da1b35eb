module Setting::TypeHandlers
  class TimeHandler < BaseHandler
    def self.handles_type?(type)
      %w[time date datetime].include?(type.to_s)
    end

    def self.parse(value, setting = nil)
      safe_parse(value) do |v|
        case setting&.setting_type&.to_sym
        when :time
          parse_time_value(v)
        when :date
          Date.parse(v.to_s)
        when :datetime
          DateTime.parse(v.to_s)
        else
          Time.parse(v.to_s)
        end
      end
    end

    def self.normalize(input, setting = nil)
      safe_normalize(input) do |i|
        case setting&.setting_type&.to_sym
        when :time
          normalize_time_input(i)
        when :date
          Date.parse(i.to_s).to_s
        when :datetime
          DateTime.parse(i.to_s).to_s
        else
          Time.parse(i.to_s).to_s
        end
      end
    end

    def self.default_value
      Time.current
    end

    def self.display_name
      'Time'
    end

    def self.validation_rules
      {
        type: 'time',
        pattern: '^\\d{1,2}:\\d{2}$',
        example: '09:00'
      }
    end

    private

    def self.parse_time_value(value)
      return Time.parse("#{Date.current} #{value}") if value.match?(/^\d{1,2}:\d{2}$/)
      Time.parse(value.to_s)
    end

    def self.normalize_time_input(input)
      return input if input.is_a?(String) && input.match?(/^\d{2}:\d{2}$/)
      
      time = input.is_a?(Time) ? input : Time.parse(input.to_s)
      time.strftime('%H:%M')
    end
  end
end
