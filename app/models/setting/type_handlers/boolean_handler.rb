module Setting::TypeHandlers
  class BooleanHandler < BaseHandler
    TRUTHY_VALUES = %w[true 1 yes on].freeze
    FALSY_VALUES = %w[false 0 no off].freeze

    def self.handles_type?(type)
      type.to_s == 'boolean'
    end

    def self.parse(value, setting = nil)
      safe_parse(value) do |v|
        # Remove surrounding quotes if present
        clean_value = v.to_s.strip.gsub(/^["']|["']$/, '')
        clean_value.downcase.in?(TRUTHY_VALUES)
      end
    end

    def self.normalize(input, setting = nil)
      safe_normalize(input) do |i|
        # Remove surrounding quotes if present
        clean_input = i.to_s.strip.gsub(/^["']|["']$/, '')

        case clean_input.downcase
        when *TRUTHY_VALUES
          'true'
        when *FALSY_VALUES
          'false'
        else
          # Fallback for other values
          clean_input.downcase.in?(TRUTHY_VALUES) ? 'true' : 'false'
        end
      end
    end

    def self.default_value
      false
    end

    def self.display_name
      'Boolean'
    end

    def self.validation_rules
      {
        type: 'boolean',
        options: %w[true false],
        example: 'true'
      }
    end
  end
end
