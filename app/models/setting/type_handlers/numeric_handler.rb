module Setting::TypeHandlers
  class NumericHandler < BaseHandler
    def self.handles_type?(type)
      %w[integer decimal currency].include?(type.to_s)
    end

    def self.parse(value, setting = nil)
      safe_parse(value) do |v|
        case setting&.setting_type&.to_sym
        when :integer
          v.to_i
        when :decimal, :currency
          BigDecimal(v.to_s)
        else
          v.to_f
        end
      end
    end

    def self.normalize(input, setting = nil)
      safe_normalize(input) do |i|
        case setting&.setting_type&.to_sym
        when :integer
          i.to_i.to_s
        when :decimal, :currency
          BigDecimal(i.to_s).to_s
        else
          i.to_f.to_s
        end
      end
    end

    def self.default_value
      0
    end

    def self.display_name
      'Number'
    end

    def self.validation_rules
      {
        type: 'number',
        example: '42'
      }
    end

    # Enhanced validation rules based on specific numeric type
    def self.validation_rules_for_subtype(subtype)
      case subtype.to_s
      when 'integer'
        {
          type: 'integer',
          pattern: '^-?\\d+$',
          example: '123'
        }
      when 'decimal', 'currency'
        {
          type: 'decimal',
          pattern: '^-?\\d+(\\.\\d+)?$',
          example: '123.45'
        }
      else
        validation_rules
      end
    end
  end
end
