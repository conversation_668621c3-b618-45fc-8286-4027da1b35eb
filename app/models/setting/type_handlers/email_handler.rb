module Setting::TypeHandlers
  class EmailHandler < BaseHandler
    def self.handles_type?(type)
      type.to_s == 'email'
    end

    def self.parse(value, setting = nil)
      safe_parse(value) do |v|
        v.to_s.strip.downcase
      end
    end

    def self.normalize(input, setting = nil)
      safe_normalize(input) do |i|
        i.to_s.strip.downcase
      end
    end

    def self.default_value
      ''
    end

    def self.display_name
      'Email Address'
    end

    def self.validation_rules
      {
        type: 'email',
        pattern: '^[\\w+\\-.]+@[a-z\\d\\-]+(\\.[a-z\\d\\-]+)*\\.[a-z]+$',
        example: '<EMAIL>'
      }
    end
  end
end
