module Setting::TypeHandlers
  class PhoneHandler < BaseHandler
    def self.handles_type?(type)
      type.to_s == 'phone'
    end

    def self.parse(value, setting = nil)
      safe_parse(value) do |v|
        # Convert stored value to proper E164 format for API
        if defined?(Phonelib) && v.present?
          phone = Phonelib.parse(v, default_country)
          phone.e164.presence || v.to_s.strip
        else
          v.to_s.strip
        end
      end
    end

    def self.normalize(input, setting = nil)
      safe_normalize(input) do |i|
        # Store as E164 format in database
        if defined?(Phonelib) && i.present?
          phone = Phonelib.parse(i, default_country)
          phone.valid? ? phone.e164 : i.to_s.strip
        else
          i.to_s.strip
        end
      end
    end

    def self.default_value
      ''
    end

    def self.display_name
      'Phone Number'
    end

    def self.validation_rules
      {
        type: 'phone',
        pattern: '^\\+?[\\d\\s\\-\\(\\)]+$',
        example: '+966501234567',
        description: 'International phone number format'
      }
    end

    private

    def self.default_country
      # Use Saudi Arabia as default based on the Phonofy config
      :sa
    end
  end
end
