module Setting::TypeHandlers
  class CollectionHandler < BaseHandler
    def self.handles_type?(type)
      %w[json array].include?(type.to_s)
    end

    def self.parse(value, setting = nil)
      safe_parse(value) do |v|
        case setting&.setting_type&.to_sym
        when :json
          JSON.parse(v.to_s)
        when :array
          v.to_s.split(',').map(&:strip)
        else
          v
        end
      end
    end

    def self.normalize(input, setting = nil)
      safe_normalize(input) do |i|
        case setting&.setting_type&.to_sym
        when :json
          i.is_a?(String) ? i : i.to_json
        when :array
          i.is_a?(Array) ? i.join(',') : i.to_s
        else
          i.to_s
        end
      end
    end

    def self.default_value
      []
    end

    def self.display_name
      'Collection'
    end

    def self.validation_rules
      {
        type: 'array',
        example: '["item1", "item2"]'
      }
    end
  end
end
