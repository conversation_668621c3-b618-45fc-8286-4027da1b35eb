module Setting::TypeHandlers
  class BaseHandler
    class << self
      # Override in subclasses to specify which types this handler supports
      def handles_type?(type)
        false
      end

      # Parse stored string value to typed value
      def parse(value, setting = nil)
        value
      end

      # Normalize input to storage format
      def normalize(input, setting = nil)
        input.to_s
      end

      # Default value when parsing fails
      def default_value
        ''
      end

      # Display name for the type
      def display_name
        'Unknown'
      end

      # Validation rules for the type
      def validation_rules
        {
          type: 'string',
          example: 'value'
        }
      end

      protected

      # Helper to safely parse with fallback
      def safe_parse(value, &block)
        yield(value)
      rescue => e
        Rails.logger.warn("Parse error for #{self.name}: #{e.message}")
        default_value
      end

      # Helper to safely normalize with fallback
      def safe_normalize(input, &block)
        yield(input)
      rescue => e
        Rails.logger.warn("Normalize error for #{self.name}: #{e.message}")
        input.to_s
      end
    end
  end
end
