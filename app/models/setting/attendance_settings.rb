module Setting::AttendanceSettings
  extend ActiveSupport::Concern

  class_methods do
    # Simple attendance setting helper methods
    def attendance_work_start_time
      get('attendance', 'work_start_time', Time.parse('08:00'))
    end

    def attendance_work_end_time
      get('attendance', 'work_end_time', Time.parse('16:00'))
    end

    def attendance_duplicate_threshold_seconds
      get('attendance', 'duplicate_threshold_seconds', 60)
    end

    def attendance_break_threshold_minutes
      get('attendance', 'break_threshold_minutes', 120)
    end

    def attendance_deductions_enabled?
      get('attendance', 'deductions_enabled', false)
    end

    def attendance_daily_expected_hours
      get('attendance', 'daily_expected_hours', 8.0)
    end

    def attendance_daily_work_threshold
      get('attendance', 'daily_work_threshold', 5.0)
    end

    def attendance_accumulated_hours_threshold
      get('attendance', 'accumulated_hours_threshold', 9.0)
    end

    def attendance_exclude_weekends?
      get('attendance', 'exclude_weekends', true)
    end

    def attendance_exclude_holidays?
      get('attendance', 'exclude_holidays', true)
    end

    def attendance_weekend_days
      value = get('attendance', 'weekend_days', '5,6')
      # Handle both string and array formats for backward compatibility
      if value.is_a?(Array)
        value.map(&:to_i)
      else
        value.split(',').map(&:to_i)
      end
    end
  end
end
