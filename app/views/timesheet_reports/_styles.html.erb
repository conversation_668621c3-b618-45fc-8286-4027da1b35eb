<style>
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: Arial, sans-serif;
    font-size: 12px;
    line-height: 1.4;
    color: #333;
  }

  .timesheet-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
  }

  .timesheet-header {
    text-align: center;
    margin-bottom: 30px;
  }

  .timesheet-header h1 {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .period-info {
    font-size: 14px;
    color: #666;
  }

  .employee-info {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
  }

  .info-row {
    margin-bottom: 8px;
    display: flex;
  }

  .info-row .label {
    font-weight: bold;
    width: 80px;
    flex-shrink: 0;
  }

  .info-row .value {
    flex: 1;
  }

  .attendance-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
  }

  .attendance-table th,
  .attendance-table td {
    border: 1px solid #333;
    padding: 8px;
    text-align: center;
    vertical-align: middle;
  }

  .attendance-table th {
    background-color: #f0f0f0;
    font-weight: bold;
    font-size: 11px;
  }

  .attendance-table td {
    height: 25px;
    font-size: 10px;
  }

  .day-number {
    width: 8%;
  }

  .day-name {
    width: 12%;
  }

  .date {
    width: 20%;
  }

  .arrival-time,
  .departure-time {
    width: 20%;
  }

  .signature {
    width: 20%;
  }

  /* Row highlighting */
  .weekend-row {
    background-color: #fff2cc !important;
  }

  .leave-row {
    background-color: #ffe6e6 !important;
  }

  .absent-row {
    background-color: #f0f0f0 !important;
  }

  .present-row {
    background-color: #ffffff;
  }

  .summary-section {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #ddd;
    background-color: #f9f9f9;
  }

  .summary-row {
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
  }

  .summary-label {
    font-weight: bold;
  }

  .summary-value {
    font-weight: bold;
    color: #0066cc;
  }

  .signatures-section {
    margin-top: 30px;
    padding: 20px;
    border: 1px solid #ddd;
  }

  .signature-label {
    font-weight: bold;
    margin-bottom: 20px;
  }

  .signature-lines {
    display: flex;
    justify-content: space-around;
  }

  .signature-line {
    width: 200px;
    height: 1px;
    border-bottom: 1px solid #333;
    margin-top: 40px;
  }

  /* Print styles */
  @media print {
    .timesheet-container {
      max-width: none;
      margin: 0;
      padding: 10px;
    }

    .attendance-table {
      font-size: 9px;
    }

    .attendance-table th,
    .attendance-table td {
      padding: 4px;
    }

    body {
      font-size: 11px;
    }
  }

  /* Page break handling */
  @page {
    size: A4;
    margin: 0.5in;
  }

  .attendance-table {
    page-break-inside: avoid;
  }

  .signatures-section {
    page-break-inside: avoid;
  }
</style>
