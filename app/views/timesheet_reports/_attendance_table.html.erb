<table class="attendance-table">
  <thead>
    <tr>
      <th>No.</th>
      <th>Day</th>
      <th>Date</th>
      <th>Arrival time</th>
      <th>Leave time</th>
      <th>Signature</th>
    </tr>
  </thead>
  <tbody>
    <% @data[:days].each do |day| %>
      <% 
        row_classes = ['attendance-row']
        case day[:status]
        when 'weekend'
          row_classes << 'weekend-row'
        when 'leave'
          row_classes << 'leave-row'
        when 'absent'
          row_classes << 'absent-row'
        when 'present'
          row_classes << 'present-row'
        end
      %>
      <tr class="<%= row_classes.join(' ') %>">
        <td class="day-number"><%= day[:day_number] %></td>
        <td class="day-name"><%= day[:day_name] %></td>
        <td class="date"><%= day[:formatted_date] %></td>
        <td class="arrival-time"><%= day[:arrival_time] || '' %></td>
        <td class="departure-time"><%= day[:departure_time] || '' %></td>
        <td class="signature"></td>
      </tr>
    <% end %>
  </tbody>
</table>
