<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Salary Slip - <%= @employee.name %></title>
  <style>
    <%= render 'salary_slips/styles' %>
  </style>
</head>
<body>
<div class="salary-slip">
  <!-- Header Section -->
  <header class="slip-header">
    <div class="header-left">
      <div class="logo-container">
        <div class="logo-placeholder">A</div>
      </div>
    </div>
    <div class="header-right">
      <div class="total-amount-section">
        <div class="total-label">Total Amount</div>
        <div class="total-value"><%= format_currency(@salary_calculation.net_salary) %></div>
      </div>
    </div>
  </header>

  <!-- Employee Details Section -->
  <section class="employee-details">
    <div class="details-left">
      <div class="detail-item">
        <div class="detail-label">Date</div>
        <div class="detail-value"><%= Date.current.strftime('%d/%m/%Y') %></div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Salary Month</div>
        <div class="detail-value"><%= format_salary_month(@salary_calculation.period) %></div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Industry</div>
        <div class="detail-value"><%= company_industry %></div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Payment Type</div>
        <div class="detail-value"><%= default_payment_method %></div>
      </div>
    </div>

    <div class="details-right">
      <div class="employee-info">
        <div class="job-title"><%= employee_job_title %></div>
        <div class="employee-name"><%= @employee.name %></div>
        <div class="contact-info">
          <% employee_contact_display.each do |contact_line| %>
            <div><%= contact_line %></div>
          <% end %>
        </div>

        <div class="note-section">
          <div class="note-label">Note</div>
          <div class="note-text">
            This salary slip is generated automatically and contains confidential information.
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Salary Table Section -->
  <section class="salary-table-section">
    <table class="salary-table">
      <thead>
      <tr>
        <th>ARTICLE</th>
        <th>AMOUNT</th>
        <th>DEDUCTIONS</th>
        <th>FINAL AMOUNT</th>
      </tr>
      </thead>
      <tbody>
      <%= render 'salary_slips/salary_rows' %>
      </tbody>
    </table>
  </section>

  <!-- Totals Section -->
  <section class="totals-section">
    <div class="totals-container">
      <table class="totals-table">
        <tr>
          <td class="total-label">Total Deductions</td>
          <td class="total-value"><%= format_currency(@salary_calculation.total_deductions) %></td>
        </tr>
        <tr>
          <td class="total-label">Total Taxes</td>
          <td class="total-value"><%= format_currency(calculate_total_taxes) %></td>
        </tr>
        <tr>
          <td class="total-label">Total VAT</td>
          <td class="total-value"><%= format_currency(0) %></td>
        </tr>
        <tr class="empty-row">
          <td></td>
          <td></td>
        </tr>
        <tr class="final-total">
          <td class="total-label">Total Price</td>
          <td class="total-value"><%= format_currency(@salary_calculation.net_salary) %></td>
        </tr>
      </table>
    </div>
  </section>

  <!-- Footer Section -->
  <footer class="slip-footer">
    <div class="footer-content">
      <div class="terms-title">Terms & Conditions</div>
      <div class="terms-text">
        This is a computer-generated document and does not require a signature.
        Please keep this document for your records.
      </div>
    </div>
  </footer>
</div>
</body>
</html>
