/* Reset and base styles */
* {
margin: 0;
padding: 0;
box-sizing: border-box;
}

body {
font-family: 'Helvetica', Arial, sans-serif;
font-size: 9px;
line-height: 1.4;
color: #374151;
background: white;
}

/* Print-specific styles */
@media print {
body {
-webkit-print-color-adjust: exact;
print-color-adjust: exact;
}

.salary-slip {
page-break-inside: avoid;
}
}

/* Main container */
.salary-slip {
width: 100%;
max-width: 210mm;
margin: 0 auto;
padding: 30px;
background: white;
}

/* Header styles */
.slip-header {
display: flex;
justify-content: space-between;
align-items: flex-start;
margin-bottom: 10px;
height: 50px;
}

.header-left {
display: flex;
align-items: center;
}

.logo-container {
width: 35px;
height: 35px;
border: 1px solid #374151;
display: flex;
align-items: center;
justify-content: center;
}

.logo-placeholder {
font-size: 16px;
font-weight: bold;
color: #2563EB;
}

.header-right {
text-align: right;
}

.total-amount-section {
text-align: right;
}

.total-label {
font-size: 9px;
color: #6B7280;
margin-bottom: 2px;
}

.total-value {
font-size: 18px;
font-weight: bold;
color: #2563EB;
}

/* Employee details section */
.employee-details {
display: flex;
justify-content: space-between;
margin-bottom: 15px;
min-height: 80px;
}

.details-left {
width: 250px;
}

.details-right {
width: 270px;
}

.detail-item {
margin-bottom: 3px;
}

.detail-label {
font-size: 7px;
color: #6B7280;
margin-bottom: 1px;
}

.detail-value {
font-size: 9px;
color: #374151;
}

.employee-info {
text-align: left;
}

.job-title {
font-size: 9px;
color: #6B7280;
margin-bottom: 2px;
}

.employee-name {
font-size: 10px;
font-weight: bold;
color: #374151;
margin-bottom: 2px;
}

.contact-info {
font-size: 8px;
color: #6B7280;
line-height: 1.3;
margin-bottom: 6px;
}

.note-section {
margin-top: 6px;
}

.note-label {
font-size: 8px;
color: #6B7280;
margin-bottom: 1px;
}

.note-text {
font-size: 7px;
color: #6B7280;
line-height: 1.5;
}

/* Salary table styles */
.salary-table-section {
margin-bottom: 20px;
}

.salary-table {
width: 100%;
border-collapse: collapse;
border: 0.5px solid #F0F0F0;
}

.salary-table th {
background-color: #F8F9FA;
padding: 8px;
font-size: 9px;
font-weight: bold;
color: #6B7280;
text-align: left;
border: 0.5px solid #F0F0F0;
}

.salary-table th:nth-child(2),
.salary-table th:nth-child(3),
.salary-table th:nth-child(4) {
text-align: right;
}

.salary-table td {
padding: 6px 8px;
border: 0.5px solid #F0F0F0;
font-size: 9px;
}

.salary-table .main-row {
background-color: white;
font-weight: bold;
color: #374151;
}

.salary-table .description-row {
background-color: #F8F9FA;
font-size: 8px;
color: #6B7280;
padding: 4px 8px;
}

.salary-table .amount-cell {
text-align: right;
}

/* Totals section */
.totals-section {
display: flex;
justify-content: flex-end;
margin-bottom: 40px;
}

.totals-container {
width: 190px;
}

.totals-table {
width: 100%;
border-collapse: collapse;
}

.totals-table td {
padding: 4px 8px;
border: none;
}

.totals-table .total-label {
font-size: 9px;
font-weight: bold;
color: #374151;
}

.totals-table .total-value {
text-align: right;
font-size: 9px;
font-weight: bold;
color: #374151;
}

.totals-table .final-total {
border-top: 1px solid #6B7280;
padding-top: 6px;
}

.totals-table .final-total .total-label,
.totals-table .final-total .total-value {
font-size: 11px;
color: #2563EB;
padding: 6px 8px;
}

.totals-table .empty-row td {
padding: 2px;
}

/* Footer styles */
.slip-footer {
position: relative;
bottom: 0;
width: 100%;
}

.footer-content {
font-size: 8px;
color: #6B7280;
}

.terms-title {
font-weight: bold;
margin-bottom: 3px;
}

.terms-text {
line-height: 2;
}
