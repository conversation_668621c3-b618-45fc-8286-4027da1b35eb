module Attendance
  class EmployeeSyncWorker
    include Sidekiq::Worker

    def perform(device_id, sync_log_id)
      device = ::Attendance::Device.find(device_id)
      sync_log = ::Attendance::SyncLog.find(sync_log_id)

      Rails.logger.info("Starting employee sync for device #{device.name} (ID: #{device_id})")

      sync_log.mark_as_running!
      start_time = Time.current

      begin
        result = perform_employee_sync(device, sync_log)

        duration = Time.current - start_time
        sync_log.mark_as_completed!(result.merge(duration: duration))

        Rails.logger.info("Employee sync completed for device #{device.name}")
        result
      rescue => e
        duration = Time.current - start_time
        error_details = {
          error: e.message,
          backtrace: e.backtrace.first(5),
          duration: duration
        }

        sync_log.mark_as_failed!(error_details)
        Rails.logger.error("Employee sync failed for device #{device.name}: #{e.message}")
        raise e
      end
    end

    # Class method for immediate execution (non-async)
    def self.perform_sync(device_id, sync_log_id)
      new.perform(device_id, sync_log_id)
    end

    private

    def perform_employee_sync(device, sync_log)
      adapter = device.create_adapter

      unless adapter.supports_user_management?
        raise "Device does not support user management"
      end

      # Step 1: Get device users and employees
      device_users = adapter.get_users
      employees = Employee.all

      # Step 2: Create/update employee-device mappings
      mapping_result = create_employee_mappings(device, device_users, employees)

      # Step 3: Sync employees to device (if bulk_employee_sync)
      sync_result = if sync_log.bulk_employee_sync?
                      sync_employees_to_device(adapter, device, employees)
                    else
                      { message: "Employee mappings created, no user sync performed" }
                    end

      # Step 4: Return combined results
      {
        mapping_result: mapping_result,
        sync_result: sync_result,
        total_device_users: device_users.count,
        total_employees: employees.count
      }
    end

    def create_employee_mappings(device, device_users, employees)
      created_count = 0
      updated_count = 0
      errors = []

      device_users.each do |device_user|
        begin
          # Find matching employee by name
          employee = find_matching_employee(device_user, employees)

          if employee
            mapping = Attendance::EmployeeDeviceMapping.find_or_initialize_by(
              employee: employee,
              attendance_device: device
            )

            if mapping.persisted?
              # Update existing mapping
              mapping.update!(device_user_id: device_user[:user_id])
              updated_count += 1
            else
              # Create new mapping
              mapping.device_user_id = device_user[:user_id]
              mapping.notes = "Auto-created by employee sync"
              mapping.save!
              created_count += 1
            end

            Rails.logger.debug("Mapped employee #{employee.name} to device user #{device_user[:user_id]}")
          else
            errors << "No employee found for device user: #{device_user[:name]} (ID: #{device_user[:user_id]})"
          end
        rescue => e
          errors << "Error mapping device user #{device_user[:name]}: #{e.message}"
        end
      end

      {
        created_mappings: created_count,
        updated_mappings: updated_count,
        mapping_errors: errors
      }
    end

    def sync_employees_to_device(adapter, device, employees)
      success_count = 0
      failure_count = 0
      errors = []

      # Get employees that have mappings for this device
      mapped_employees = employees.joins(:employee_device_mappings)
                                  .where(employee_device_mappings: { attendance_device: device })

      mapped_employees.each do |employee|
        begin
          device_user_id = employee.device_code_for(device)

          if adapter.sync_user(employee, device_user_id)
            success_count += 1
            Rails.logger.debug("Synced employee #{employee.name} to device")
          else
            failure_count += 1
            errors << "Failed to sync employee #{employee.name}"
          end
        rescue => e
          failure_count += 1
          errors << "Error syncing employee #{employee.name}: #{e.message}"
        end
      end

      {
        synced_employees: success_count,
        failed_employees: failure_count,
        sync_errors: errors
      }
    end

    def find_matching_employee(device_user, employees)
      device_user_id = device_user[:user_id].to_i
      device_name = device_user[:name]

      # Strategy 1: Match by user_id (if device user ID matches employee user_id)
      employee = employees.find { |emp| emp.user_id == device_user_id }
      return employee if employee

      # Strategy 2: Match by ID (if device user ID matches employee ID)
      employee = employees.find { |emp| emp.id == device_user_id }
      return employee if employee

      # Strategy 3: Create mapping based on position/order
      # Since we can't match by name, we'll create a simple mapping
      # This is a fallback strategy - you may want to customize this

      # For now, we'll create a mapping for the first N employees
      # where N is the device user ID, if it's within reasonable range
      if device_user_id > 0 && device_user_id <= employees.count
        # Map device user ID 1 to first employee, ID 2 to second employee, etc.
        employee = employees.sort_by(&:id)[device_user_id - 1]
        return employee if employee
      end

      # No match found - log this for manual review
      Rails.logger.warn("No employee match found for device user: #{device_name} (ID: #{device_user_id})")
      nil
    end
  end
end
