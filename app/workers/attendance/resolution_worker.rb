class Attendance::ResolutionWorker
  include Sidekiq::Worker

  sidekiq_options queue: :attendance, retry: 3

  def perform(date_string = nil)
    # Use the provided date or default to yesterday
    target_date = date_string.present? ? Date.parse(date_string) : Date.yesterday

    Rails.logger.info "Starting automatic resolution of undetermined attendance events for #{target_date}..."

    # Get employee count first to use in the block
    employee_count = Attendance::Event.undetermined.for_date(target_date).pluck(:employee_id).uniq.count

    results = ::Attendance::ResolutionService.resolve_for_date(target_date, employee_count) do |employee, index, resolved_for_employee, undetermined_after|
      Rails.logger.info "Processing employee #{index + 1}/#{employee_count}: #{employee.name} (ID: #{employee.id})"
      Rails.logger.info "  Resolved #{resolved_for_employee} events, #{undetermined_after} remain unresolved"
    end

    Rails.logger.info "Completed automatic resolution. Resolved #{results[:resolved_count]} events."

    # Report remaining undetermined events
    if results[:remaining_undetermined] > 0
      Rails.logger.warn "WARNING: #{results[:remaining_undetermined]} events remain undetermined and require manual resolution."

      # You could add code here to send an email notification to HR staff
      # AttendanceMailer.undetermined_events_report(target_date, results[:remaining_undetermined]).deliver_now
    end
  end
end
