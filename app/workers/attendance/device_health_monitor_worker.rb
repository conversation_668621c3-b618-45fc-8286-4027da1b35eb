class Attendance::<PERSON>ceHealthMonitorWorker
  include Sidekiq::Worker

  sidekiq_options queue: :attendance_monitor, retry: 2

  def perform(device_ids = [])
    Rails.logger.info("Starting device health monitoring")

    devices = device_ids.any? ? Attendance::Device.where(id: device_ids) : Attendance::Device.all

    health_report = {
      total_devices: devices.count,
      online_devices: 0,
      offline_devices: 0,
      error_devices: 0,
      maintenance_devices: 0,
      device_statuses: []
    }

    devices.find_each do |device|
      device_health = check_device_health(device)
      health_report[:device_statuses] << device_health

      case device_health[:status]
      when 'active'
        health_report[:online_devices] += 1
      when 'error'
        health_report[:error_devices] += 1
      when 'maintenance'
        health_report[:maintenance_devices] += 1
      else
        health_report[:offline_devices] += 1
      end
    end

    Rails.logger.info("Device Health Report: #{health_report}")

    # Send alerts for problematic devices
    send_health_alerts(health_report)

    # Store health metrics (could be stored in a metrics table)
    store_health_metrics(health_report)

    health_report
  end

  private

  def check_device_health(device)
    Rails.logger.debug("Checking health for device: #{device.name}")

    health_status = {
      device_id: device.id,
      device_name: device.name,
      device_type: device.device_type,
      status: device.status,
      last_seen_at: device.last_seen_at,
      connection_test: false,
      sync_health_score: device.sync_health_score,
      last_successful_sync: device.last_successful_sync&.created_at,
      issues: []
    }

    # Skip connection test for file import devices
    if device.file_import?
      health_status[:connection_test] = true
      health_status[:status] = 'active'
    else
      # Test connection for network devices
      begin
        health_status[:connection_test] = device.test_connection

        if health_status[:connection_test]
          device.update(status: :active, last_seen_at: Time.current)
          health_status[:status] = 'active'
        else
          device.update(status: :error)
          health_status[:status] = 'error'
          health_status[:issues] << 'Connection test failed'
        end
      rescue => e
        Rails.logger.error("Health check failed for #{device.name}: #{e.message}")
        device.update(status: :error)
        health_status[:status] = 'error'
        health_status[:issues] << "Connection error: #{e.message}"
      end
    end

    # Check sync health
    check_sync_health(device, health_status)

    # Check device capacity (if supported)
    check_device_capacity(device, health_status)

    health_status
  end

  def check_sync_health(device, health_status)
    # Check if device hasn't synced recently
    last_sync = device.last_sync_log
    if last_sync.nil?
      health_status[:issues] << 'No sync history found'
    elsif last_sync.created_at < 24.hours.ago
      health_status[:issues] << 'No recent sync (>24 hours)'
    end

    # Check sync success rate
    if health_status[:sync_health_score] < 80
      health_status[:issues] << "Low sync success rate: #{health_status[:sync_health_score]}%"
    end

    # Check for recent failures
    recent_failures = device.attendance_sync_logs
                            .where('created_at > ? AND status = ?', 24.hours.ago, 'failed')
                            .count

    if recent_failures > 3
      health_status[:issues] << "Multiple recent sync failures: #{recent_failures}"
    end
  end

  def check_device_capacity(device, health_status)
    return unless device.supports_device_info?

    begin
      device_info = device.get_device_info

      if device_info.present?
        # Check user capacity
        max_users = device.capabilities['max_users']
        current_users = device_info[:users_count]

        if max_users && current_users
          user_usage = (current_users.to_f / max_users * 100).round(2)
          if user_usage > 90
            health_status[:issues] << "User capacity critical: #{user_usage}%"
          elsif user_usage > 80
            health_status[:issues] << "User capacity warning: #{user_usage}%"
          end
        end

        # Check record capacity
        max_records = device.capabilities['max_records']
        current_records = device_info[:records_count]

        if max_records && current_records
          record_usage = (current_records.to_f / max_records * 100).round(2)
          if record_usage > 90
            health_status[:issues] << "Record capacity critical: #{record_usage}%"
          elsif record_usage > 80
            health_status[:issues] << "Record capacity warning: #{record_usage}%"
          end
        end
      end
    rescue => e
      Rails.logger.debug("Could not check device capacity for #{device.name}: #{e.message}")
    end
  end

  def send_health_alerts(health_report)
    # Send alerts for devices with issues
    problematic_devices = health_report[:device_statuses].select { |d| d[:issues].any? }

    if problematic_devices.any?
      Rails.logger.warn("Found #{problematic_devices.count} devices with health issues")

      # Could send email notifications, Slack messages, etc.
      problematic_devices.each do |device_status|
        Rails.logger.warn("Device #{device_status[:device_name]} issues: #{device_status[:issues].join(', ')}")
      end
    end

    # Send critical alerts for offline devices
    offline_devices = health_report[:device_statuses].select { |d| d[:status] == 'error' }

    if offline_devices.any?
      Rails.logger.error("CRITICAL: #{offline_devices.count} devices are offline")
      # Could trigger immediate notifications
    end
  end

  def store_health_metrics(health_report)
    # This could store metrics in a time-series database, Redis, or a metrics table
    # For now, we'll just log the metrics

    Rails.logger.info("Storing health metrics: #{health_report.except(:device_statuses)}")

    # Example: Could store in Redis with timestamp
    # Redis.current.zadd("device_health_metrics", Time.current.to_i, health_report.to_json)
  end
end
