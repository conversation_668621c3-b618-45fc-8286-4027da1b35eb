class SalaryCalculationSerializer
  include JSONAPI::Serializer

  attributes :period, :period_start_date, :period_end_date,
             :status, :calculation_date, :payment_date, :notes,
             :created_at, :updated_at

  # Ensure numeric fields are returned as numeric values
  attribute :gross_salary do |calculation|
    calculation.gross_salary.to_f
  end

  attribute :net_salary do |calculation|
    calculation.net_salary.to_f
  end

  attribute :total_hours do |calculation|
    calculation.total_hours.to_f
  end

  attribute :total_deductions do |calculation|
    calculation.total_deductions.to_f
  end

  # Complete deductions breakdown including leave deductions
  attribute :deductions do |calculation|
    base_deductions = calculation.deductions || {}
    leave_deductions_amount = calculation.leave_deductions

    # Convert all values to floats to ensure numeric response
    result = base_deductions.transform_values { |v| v.to_f }

    # Add leave deductions to the breakdown if they exist
    if leave_deductions_amount > 0
      result.merge('leave_deductions' => leave_deductions_amount.to_f)
    else
      result
    end
  end

  attribute :has_salary_slip do |calculation|
    calculation.salary_slip_pdf.attached?
  end

  belongs_to :employee
  belongs_to :salary_package
  belongs_to :approved_by, serializer: :employee, if: Proc.new { |record| record.approved_by.present? }

  has_many :calculation_details, serializer: SalaryCalculation::DetailSerializer

  # Add a method to format the details in a more structured way
  attribute :detailed_breakdown, if: Proc.new { |_record, params| params && params[:include_details] } do |object|
    details = object.calculation_details.group_by(&:detail_type)

    {
      base: details['base'] || [],
      additions: details['addition'] || [],
      deductions: details['deduction'] || [],
      total_additions: (details['addition'] || []).sum(&:amount),
      total_deductions: (details['deduction'] || []).sum(&:amount)
    }
  end

  has_one :approval_request, serializer: ApprovalRequestSerializer
end
