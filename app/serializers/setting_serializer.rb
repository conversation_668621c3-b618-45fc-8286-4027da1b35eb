class SettingSerializer
  include JSONAPI::Serializer

  attributes :namespace, :key, :description, :is_editable, :setting_type, :logical_key

  # Return typed value directly as 'value'
  attribute :value do |setting|
    setting.value
  end

  # Include type metadata for frontend validation
  attribute :type_info do |setting|
    {
      type: setting.setting_type,
      display_name: setting.type_display_name,
      validation_rules: setting.validation_rules_for_type
    }
  end
end
