class SalaryCalculation::DetailSerializer
  include JSONAPI::Serializer

  set_type :salary_calculation_detail

  attributes :id, :detail_type, :category, :description

  # Ensure amount is returned as a numeric value
  attribute :amount do |object|
    object.amount.to_f
  end

  # Include reference information if available
  attribute :reference_info do |object|
    if object.reference.present?
      {
        id: object.reference.id,
        type: object.reference_type
        # Add any other relevant reference information
      }
    else
      nil
    end
  end
end
