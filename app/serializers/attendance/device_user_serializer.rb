module Attendance
  class DeviceUserSerializer
    include JSONAPI::Serializer

    set_type :attendance_device_user

    attributes :user_id, :name, :privilege, :password, :group_id, :card_number, :device_id

    # Helper attributes based on available data
    attribute :admin? do |object|
      object.admin?
    end

    attribute :user? do |object|
      object.user?
    end

    attribute :has_card? do |object|
      object.has_card?
    end

    attribute :has_password? do |object|
      object.has_password?
    end

    attribute :verification_methods do |object|
      object.verification_methods
    end

    # Relationships
    belongs_to :mapping, serializer: ::Attendance::EmployeeDeviceMappingSerializer, if: proc { |record| record.mapped? }
    belongs_to :device, serializer: ::Attendance::DeviceSerializer
    belongs_to :employee, through: :mapping, serializer: ::EmployeeSerializer
  end
end
