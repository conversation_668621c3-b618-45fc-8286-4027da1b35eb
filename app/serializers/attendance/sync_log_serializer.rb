module Attendance
  class SyncLogSerializer
    include JSONAPI::Serializer

    set_type :attendance_sync_log

    attributes :status, :sync_type, :started_at, :completed_at, :sync_params,
               :result_summary, :error_details, :created_at, :updated_at

    belongs_to :device, serializer: Attendance::DeviceSerializer
    belongs_to :triggered_by, serializer: EmployeeSerializer

    # Computed attributes
    attribute :duration do |sync_log|
      sync_log.duration
    end

    attribute :duration_in_words do |sync_log|
      sync_log.duration_in_words
    end

    attribute :success_rate do |sync_log|
      sync_log.success_rate
    end

    attribute :has_errors do |sync_log|
      sync_log.has_errors?
    end

    attribute :error_count do |sync_log|
      sync_log.error_count
    end

    attribute :primary_error do |sync_log|
      sync_log.primary_error
    end

    attribute :records_summary do |sync_log|
      summary = sync_log.result_summary || {}
      {
        total_processed: summary['total_processed'] || 0,
        successful: summary['success'] || 0,
        failed: summary['failure'] || 0,
        duplicates: summary['duplicate'] || 0
      }
    end

    attribute :sync_performance do |sync_log|
      duration = sync_log.duration
      total_processed = sync_log.result_summary['total_processed'] || 0

      if duration && duration > 0 && total_processed > 0
        {
          records_per_second: (total_processed / duration).round(2),
          average_time_per_record: (duration / total_processed).round(4)
        }
      else
        {
          records_per_second: 0,
          average_time_per_record: 0
        }
      end
    end

    attribute :status_badge do |sync_log|
      case sync_log.status
      when 'success'
        { color: 'green', text: 'Success' }
      when 'failed'
        { color: 'red', text: 'Failed' }
      when 'partial'
        { color: 'yellow', text: 'Partial' }
      when 'running'
        { color: 'blue', text: 'Running' }
      else
        { color: 'gray', text: 'Pending' }
      end
    end

    attribute :sync_type_label do |sync_log|
      case sync_log.sync_type
      when 'manual'
        'Manual Sync'
      when 'scheduled'
        'Scheduled Sync'
      when 'real_time'
        'Real-time Sync'
      when 'retry'
        'Retry Sync'
      else
        sync_log.sync_type.humanize
      end
    end
  end
end
