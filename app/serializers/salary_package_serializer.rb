class SalaryPackageSerializer
  include JSONAPI::Serializer

  attributes :id, :base_salary, :transportation_allowance,
             :other_allowances, :effective_date, :end_date,
             :total_package_value, :active, :status,
             :adjustment_reason, :previous_package_id, :notes,
             :created_at, :updated_at, :created_by_id,
             :cancelled_at, :cancellation_reason

  # Add a virtual attribute to indicate if the package is currently active
  attribute :active do |package|
    package.active?
  end

  # Business logic attributes for UI convenience
  attribute :submittable do |package|
    package.submittable?
  end

  attribute :editable do |package|
    package.editable?
  end

  # Creator information attributes
  attribute :creator_name do |package|
    package.creator_name
  end

  attribute :creator_department do |package|
    package.creator_department
  end

  # Action availability flags for frontend logic
  attribute :cancellable do |package|
    package.cancellable?
  end

  belongs_to :employee
  belongs_to :created_by, serializer: EmployeeSerializer

  has_one :approval_request, serializer: ApprovalRequestSerializer
end
