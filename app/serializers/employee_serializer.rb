class EmployeeSerializer
  include JSONAPI::Serializer

  attributes :name, :email, :user_id,
             :avatar_url, :department, :start_date,
             :status, :phone, :phone_intl, :department_name,
             :exempt_from_attendance_deductions

  has_many :user_roles, serializer: Employees::UserRoleSerializer

  has_one :salary_package, serializer: SalaryPackageSerializer
  has_one :pending_salary_package, serializer: SalaryPackageSerializer
  has_one :draft_salary_package, serializer: SalaryPackageSerializer do |employee, params|
    employee.draft_salary_package(params[:current_employee]&.id)
  end
end
