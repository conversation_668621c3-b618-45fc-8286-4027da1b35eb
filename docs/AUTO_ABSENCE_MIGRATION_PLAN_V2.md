# **Auto-Absence Migration Plan V2 - On-The-Fly Calculation Approach**

**Status: 🆕 NEW SIMPLIFIED APPROACH**  
**Date: 2025-07-12**  
**Approach: On-the-fly missing hours → unpaid leave days conversion**

## **Overview**

**V2 Approach**: Instead of creating auto-generated Leave records, calculate missing hours on-the-fly during salary processing and convert them to unpaid leave days for deduction purposes.

## **Key Differences from V1:**

| Aspect | V1 (Auto-Generated Leaves) | V2 (On-The-Fly Calculation) |
|--------|---------------------------|------------------------------|
| **Data Storage** | Creates Leave records with `auto_absence` type | No additional records, uses existing periods |
| **Complexity** | High (workflows, approvals, conflicts) | Low (simple calculation) |
| **Salary Slip** | "Auto-absence leave: X days" | "Unpaid leave: X days (attendance-based)" |
| **Threshold Logic** | Accumulate → convert → store | Calculate → convert → deduct (no storage) |
| **Carry Forward** | Complex accumulated hours tracking | None (ignore remaining hours) |
| **Performance** | Background processing + queries | Real-time calculation only |

## **Business Rules (Unchanged)**

- ✅ **Daily work < 8 hours** = missing hours for that day
- ✅ **Missing hours ÷ 8** = unpaid leave days (ignore remainder)
- ✅ **Exclude weekends and holidays** from calculation
- ✅ **Manual leave takes precedence** (skip auto-calculation for those days)
- ✅ **Employee exemptions** respected
- ✅ **No carry-forward** between salary periods

---

## **Phase 0: Rollback V1 Changes** ⚠️ **CRITICAL - EXECUTE FIRST**

### **0.1 Identify Current V1 Changes**

**Step 0.1.1**: Check current git status
```bash
# See what V1 changes are currently staged/unstaged
cd /Users/<USER>/workspace/athar/athar-ems/services/people
git status
git diff --name-only
```

**Step 0.1.2**: Review current Leave model changes
```bash
# Check current auto_absence implementation
git diff app/models/leave.rb
```

### **0.2 Rollback Leave Model Changes**

**File: `app/models/leave.rb`**

**Step 0.2.1**: Remove auto_absence enum value
```ruby
# CURRENT (V1 approach):
enum :leave_type, {
  annual: 0,
  sick: 1,
  marriage: 2,
  maternity: 3,
  paternity: 4,
  unpaid: 5,
  auto_absence: 6  # ← REMOVE THIS
}

# ROLLBACK TO:
enum :leave_type, {
  annual: 0,
  sick: 1,
  marriage: 2,
  maternity: 3,
  paternity: 4,
  unpaid: 5
}
```

**Step 0.2.2**: Remove any auto_absence helper methods
```ruby
# REMOVE if present:
def auto_absence?
  leave_type == 'auto_absence'
end

# REMOVE any auto-approval callbacks:
before_create :auto_approve_if_system_generated

def auto_approve_if_system_generated
  # ... remove this entire method
end
```

### **0.3 Rollback Salary Leave Integration Changes**

**File: `app/models/concerns/salary/leave_integration.rb`**

**Step 0.3.1**: Revert to simple unpaid leave query
```ruby
# CURRENT (V1 approach):
leave_query = employee.leaves
              .approved
              .where(leave_type: [ :unpaid, :auto_absence ])
              .where("start_date <= ? AND end_date >= ?", end_date, start_date)

# Filter out auto-absence leaves if salary deductions are disabled
unless Setting.attendance_deductions_enabled?
  leave_query = leave_query.where.not(leave_type: :auto_absence)
end

# ROLLBACK TO:
unpaid_leaves = employee.leaves
                .approved
                .where(leave_type: :unpaid)
                .where("start_date <= ? AND end_date >= ?", end_date, start_date)
```

**Step 0.3.2**: Remove auto_absence specific logic
```ruby
# REMOVE exemption handling for auto_absence:
if leave.auto_absence? && calculation.employee.exempt_from_attendance_deductions
  calculation.calculation_details.build(
    detail_type: 'exemption',
    category: 'attendance_exempt',
    amount: 0,
    description: 'Employee is exempt from auto-generated leave deductions'
  )
  next
end

# REMOVE category logic:
category = leave.auto_absence? ? 'leave_auto_absence' : 'leave_unpaid'

# ROLLBACK TO simple:
category = 'leave_unpaid'
```

**Step 0.3.3**: Simplify the loop
```ruby
# CHANGE FROM:
leave_query.each do |leave|
  # Skip auto_absence leaves if employee is exempt...

# CHANGE TO:
unpaid_leaves.each do |leave|
  # Simple unpaid leave processing
```

### **0.4 Rollback Salary Calculation Service Changes**

**File: `app/services/salary/calculation_service.rb`**

**Step 0.4.1**: Check what attendance integration was removed
```bash
# Check if attendance_integration.rb was deleted
ls -la app/models/concerns/salary/attendance_integration.rb
git status | grep attendance_integration
```

**Step 0.4.2**: Restore simple deduction structure
```ruby
# CURRENT (V1 approach):
def calculate_enhanced_deductions(calculation)
  social_security_breakdown = calculate_social_security_breakdown(calculation.gross_salary, @start_date)

  {
    employee_social_security: social_security_breakdown[:employee_social_security] || 0,
    income_tax: 0.0,
    medical_insurance: 0.0,
    salary_advances: calculate_salary_advances(calculation),
    other_deductions: calculate_other_deductions(calculation),
    leave: calculate_leave_deductions(calculation)  # Only leave deductions now
  }
end

# VERIFY this is correct - no attendance integration should remain
```

### **0.5 Keep Employee Exemption Field** ✅ **IMPORTANT - DO NOT REMOVE**

**File: `app/models/employee.rb`**

**Step 0.5.1**: Verify exemption field exists
```bash
# Check if exempt_from_attendance_deductions field exists
docker exec athar-people-app-1 bin/rails runner "puts Employee.column_names.include?('exempt_from_attendance_deductions')"
```

**Step 0.5.2**: Restore exemption field if missing
```ruby
# IF the field was removed, restore it:
# The exemption field is needed in V2 for skipping attendance-based deductions

# Check migration status:
docker exec athar-people-app-1 bin/rails db:migrate:status | grep exempt_from_attendance_deductions

# If migration is missing, recreate it:
# docker exec athar-people-app-1 bin/rails generate migration AddExemptFromAttendanceDeductionsToEmployees exempt_from_attendance_deductions:boolean
```

**Business Justification**:
- V1: Skip auto-leave creation for exempt employees
- V2: Skip attendance-based deduction calculation for exempt employees
- **Same business need, different implementation point**

### **0.6 Clean Up V1 Documentation and Scripts**

**Step 0.6.1**: Remove V1 documentation
```bash
# Remove or rename V1 plan
mv docs/AUTO_ABSENCE_MIGRATION_PLAN.md docs/AUTO_ABSENCE_MIGRATION_PLAN_V1_ARCHIVED.md

# Remove V1 summary
rm docs/AUTO_ABSENCE_MIGRATION_SUMMARY.md  # if exists
```

**Step 0.6.2**: Clean up test scripts
```bash
# Remove V1 test scripts that test auto-leave creation
rm scripts/test_automatic_leave_creation.rb
rm scripts/test_accumulated_hours_threshold.rb
rm scripts/threshold_test.rb
# Keep scripts/verify_transformation_plan.rb for reference
```

### **0.7 Database Cleanup**

**Step 0.7.1**: Remove any auto_absence leaves
```bash
# Check if any auto_absence leaves exist
docker exec athar-people-app-1 bin/rails runner "
puts 'Checking for auto_absence leaves...'
if defined?(Leave) && Leave.respond_to?(:auto_absence)
  count = Leave.where(leave_type: 'auto_absence').count
  puts \"Found #{count} auto_absence leaves\"
  if count > 0
    Leave.where(leave_type: 'auto_absence').delete_all
    puts 'Deleted auto_absence leaves'
  end
else
  puts 'No auto_absence enum found'
end
"
```

**Step 0.7.2**: Clean up any leave periods in attendance_periods
```bash
# Remove leave periods from attendance_periods table
docker exec athar-people-app-1 bin/rails runner "
count = Attendance::Period.where(period_type: 'leave').count
puts \"Found #{count} leave periods in attendance_periods\"
if count > 0
  Attendance::Period.where(period_type: 'leave').delete_all
  puts 'Deleted leave periods from attendance_periods'
end
"
```

### **0.8 Verify Rollback Completion**

**Step 0.8.1**: Run verification script
```bash
docker exec athar-people-app-1 bin/rails runner "
puts '=== V1 Rollback Verification ==='
puts

# Check Leave model
if defined?(Leave)
  leave_types = Leave.leave_types.keys
  puts \"Leave types: #{leave_types}\"
  puts \"✅ auto_absence removed: #{!leave_types.include?('auto_absence')}\"
else
  puts '❌ Leave model not found'
end

# Check for auto_absence leaves
auto_leaves = Leave.where(\"leave_type = 6 OR leave_type = 'auto_absence'\").count rescue 0
puts \"Auto-absence leaves in DB: #{auto_leaves}\"
puts \"✅ No auto_absence leaves: #{auto_leaves == 0}\"

# Check attendance periods
leave_periods = Attendance::Period.where(period_type: 'leave').count rescue 0
puts \"Leave periods in attendance_periods: #{leave_periods}\"
puts \"✅ No leave periods in attendance: #{leave_periods == 0}\"

# Check employee exemption field (SHOULD EXIST)
has_exemption = Employee.column_names.include?('exempt_from_attendance_deductions')
puts \"Employee exemption field exists: #{has_exemption}\"
puts \"✅ Exemption field preserved: #{has_exemption}\"

puts
puts '=== Rollback Status ==='
all_clean = !leave_types.include?('auto_absence') &&
           auto_leaves == 0 &&
           leave_periods == 0 &&
           has_exemption  # ← CHANGED: Should exist, not removed

puts all_clean ? '✅ V1 rollback COMPLETE' : '❌ V1 rollback INCOMPLETE'
"
```

**Step 0.8.2**: Commit rollback changes
```bash
# Stage and commit the rollback
git add .
git commit -m "Rollback V1 auto-absence approach, prepare for V2 on-the-fly calculation"
```

---

## **Phase 1: Remove Auto-Leave Infrastructure** ⚠️ **NEEDS EXECUTION**

### **1.1 Clean Up AccumulatedHours Model**

**File: `app/models/attendance/accumulated_hours.rb`**

**Step 1.1.1**: Remove auto-leave creation methods
```ruby
# REMOVE lines 46-99 (convert_to_leave and create_leave_period methods)
# REMOVE lines 102-107 (convert_to_leave! method)

# Keep only these methods:
# - self.add_missing_hours (lines 23-35)
# - self.check_for_conversion (lines 37-44) - but modify it
# - self.total_for_employee (lines 118-120)
# - self.total_for_month (lines 123-130)
```

**Step 1.1.2**: Modify check_for_conversion to not create leaves
```ruby
# REPLACE lines 37-44:
def self.check_for_conversion(employee)
  # Remove auto-leave creation logic
  # Keep only for statistics/reporting purposes
  threshold = Setting.get('attendance', 'accumulated_hours_threshold', '9.0').to_f
  total_accumulated = active.for_employee(employee).sum(:missing_hours)

  Rails.logger.info("Employee #{employee.id} accumulated hours: #{total_accumulated}/#{threshold}")

  # Return statistics instead of creating leaves
  {
    total_accumulated: total_accumulated,
    threshold: threshold,
    would_convert: total_accumulated >= threshold
  }
end
```

### **1.2 Remove Auto-Absence from Leave Model**

**File: `app/models/leave.rb`**

**Step 1.2.1**: Remove auto_absence enum value
```ruby
# FIND line with enum :leave_type (around line 17)
# CHANGE FROM:
enum :leave_type, {
  annual: 0,
  sick: 1,
  marriage: 2,
  maternity: 3,
  paternity: 4,
  unpaid: 5,
  auto_absence: 6
}

# CHANGE TO:
enum :leave_type, {
  annual: 0,
  sick: 1,
  marriage: 2,
  maternity: 3,
  paternity: 4,
  unpaid: 5
}
```

**Step 1.2.2**: Remove auto_absence helper methods (if any exist)
```ruby
# REMOVE any methods like:
# - auto_absence?
# - auto_approve_if_system_generated
# - requires_approval? overrides for auto_absence
```

### **1.3 Clean Up Salary Leave Integration**

**File: `app/models/concerns/salary/leave_integration.rb`**

**Step 1.3.1**: Revert to simple unpaid leave handling
```ruby
# FIND the calculate_leave_deductions method (around line 5)
# REPLACE lines 11-16:
# FROM:
leave_query = employee.leaves
              .approved
              .where(leave_type: [ :unpaid, :auto_absence ])
              .where("start_date <= ? AND end_date >= ?", end_date, start_date)

# TO:
unpaid_leaves = employee.leaves
                .approved
                .where(leave_type: :unpaid)
                .where("start_date <= ? AND end_date >= ?", end_date, start_date)
```

**Step 1.3.2**: Remove auto_absence specific logic
```ruby
# REMOVE lines 17-22 (Setting.attendance_deductions_enabled? filter)
# REMOVE lines 25-33 (auto_absence exemption handling)
# REMOVE lines 55-56 (category = leave.auto_absence? logic)

# REPLACE the loop to use unpaid_leaves directly:
unpaid_leaves.each do |leave|
  # Calculate days in this period
  leave_start = [ leave.start_date, start_date ].max
  leave_end = [ leave.end_date, end_date ].min

  # ... rest of existing logic unchanged ...

  calculation.calculation_details.build(
    detail_type: 'deduction',
    category: 'leave_unpaid',  # Always unpaid, no auto_absence category
    amount: deduction,
    description: "Unpaid leave from #{leave_start} to #{leave_end} (#{business_days} business days)",
    reference: leave
  )
end
```

### **1.4 Remove Auto-Absence Jobs and Workers**

**Step 1.4.1**: Remove monthly reset job
```bash
# Delete files:
rm app/jobs/attendance/monthly_reset_job.rb
rm app/workers/attendance/monthly_reset_worker.rb  # if exists
```

**Step 1.4.2**: Remove from sidekiq scheduler (if configured)
```yaml
# Check config/sidekiq_scheduler.yml
# Remove any entries like:
# attendance_monthly_reset:
#   cron: "0 0 1 * *"
#   class: Attendance::MonthlyResetJob
```

### **1.5 Clean Up Database Migration**

**Step 1.5.1**: Create cleanup migration
```bash
# Generate migration:
docker exec athar-people-app-1 bin/rails generate migration CleanupAutoAbsenceSystem
```

**Step 1.5.2**: Migration content
```ruby
# db/migrate/xxx_cleanup_auto_absence_system.rb
class CleanupAutoAbsenceSystem < ActiveRecord::Migration[8.0]
  def up
    # Remove any existing auto_absence leaves
    execute "DELETE FROM leaves WHERE leave_type = 6"  # auto_absence enum value

    # Clean up any leave periods in attendance_periods
    execute "DELETE FROM attendance_periods WHERE period_type = 'leave'"

    # Reset accumulated hours status
    execute "UPDATE attendance_accumulated_hours SET status = 'reset' WHERE status = 'converted'"

    puts "Cleaned up auto-absence system data"
  end

  def down
    # Cannot restore deleted data
    raise ActiveRecord::IrreversibleMigration
  end
end
```

### **1.6 Update Tests**

**Step 1.6.1**: Remove auto_absence from factories
```ruby
# spec/factories/leaves.rb
# REMOVE any auto_absence leave factories

# spec/factories/attendance/accumulated_hours.rb
# UPDATE to not reference leave creation
```

**Step 1.6.2**: Update leave model tests
```ruby
# spec/models/leave_spec.rb
# REMOVE tests for auto_absence leave_type
# REMOVE tests for auto_approval logic
```

---

## **Phase 2: Implement On-The-Fly Calculation** ⚠️ **NEEDS EXECUTION**

### **2.1 Add Required Settings**

**File: `app/models/setting/attendance_settings.rb`**

**Step 2.1.1**: Add missing settings methods
```ruby
# ADD these methods to the class_methods block:

def attendance_daily_required_hours
  get('attendance', 'daily_required_hours', 8.0).to_f
end

def attendance_exclude_weekends?
  get('attendance', 'exclude_weekends', true)
end

def attendance_exclude_holidays?
  get('attendance', 'exclude_holidays', true)
end
```

**Step 2.1.2**: Seed the new settings
```ruby
# Add to db/seeds/00_shared.rb or create new seed file:

# Attendance calculation settings
attendance_calculation_settings = [
  {
    namespace: 'attendance',
    key: 'daily_required_hours',
    value: '8.0',
    description: 'Required work hours per day for attendance calculations'
  },
  {
    namespace: 'attendance',
    key: 'exclude_weekends',
    value: 'true',
    description: 'Exclude weekends from attendance deduction calculations'
  },
  {
    namespace: 'attendance',
    key: 'exclude_holidays',
    value: 'true',
    description: 'Exclude holidays from attendance deduction calculations'
  }
]

attendance_calculation_settings.each do |setting|
  Setting.find_or_create_by(
    namespace: setting[:namespace],
    key: setting[:key]
  ) do |s|
    s.value = setting[:value]
    s.description = setting[:description]
  end
end
```

### **2.2 Create Attendance Deduction Calculator Service**

**Step 2.2.1**: Create the service file
```bash
# Create directory and file:
mkdir -p app/services/salary
touch app/services/salary/attendance_deduction_calculator.rb
```

**Step 2.2.2**: Implement the calculator
```ruby
# File: app/services/salary/attendance_deduction_calculator.rb
module Salary
  class AttendanceDeductionCalculator
    attr_reader :employee, :start_date, :end_date

    def initialize(employee, start_date, end_date)
      @employee = employee
      @start_date = start_date
      @end_date = end_date
    end

    def calculate_deduction
      return 0 unless Setting.attendance_deductions_enabled?
      return 0 if employee.exempt_from_attendance_deductions?  # ← KEEP THIS CHECK

      total_missing_hours = calculate_total_missing_hours
      unpaid_leave_days = convert_to_unpaid_days(total_missing_hours)

      return 0 if unpaid_leave_days == 0

      calculate_deduction_amount(unpaid_leave_days)
    end

    def build_calculation_detail(calculation)
      total_missing_hours = calculate_total_missing_hours
      unpaid_leave_days = convert_to_unpaid_days(total_missing_hours)
      deduction_amount = calculate_deduction_amount(unpaid_leave_days)

      return unless deduction_amount > 0

      calculation.calculation_details.build(
        detail_type: 'deduction',
        category: 'leave_unpaid',
        amount: deduction_amount,
        description: "Unpaid leave: #{unpaid_leave_days} days (#{total_missing_hours.round(1)}h attendance-based)"
      )

      deduction_amount
    end

    # Statistics methods for API
    def generate_statistics
      total_missing_hours = calculate_total_missing_hours
      unpaid_days = convert_to_unpaid_days(total_missing_hours)
      deduction_amount = calculate_deduction_amount(unpaid_days)

      {
        total_missing_hours: total_missing_hours.round(1),
        unpaid_leave_days_equivalent: unpaid_days,
        ignored_remaining_hours: (total_missing_hours % Setting.attendance_daily_required_hours).round(1),
        projected_deduction: deduction_amount,
        working_days_processed: count_processed_days,
        excluded_days: count_excluded_days
      }
    end

    def generate_daily_breakdown
      breakdown = []

      (start_date..end_date).each do |date|
        if should_process_date?(date) && !has_manual_leave?(date)
          missing_hours = calculate_daily_missing_hours(date)
          work_hours = calculate_daily_work_hours(date)

          breakdown << {
            date: date,
            work_hours: work_hours.round(1),
            missing_hours: missing_hours.round(1),
            status: missing_hours > 0 ? 'attendance_deficit' : 'sufficient_work'
          }
        elsif has_manual_leave?(date)
          breakdown << {
            date: date,
            status: 'manual_leave',
            leave_type: get_leave_type_for_date(date)
          }
        elsif !should_process_date?(date)
          breakdown << {
            date: date,
            status: date.weekend? ? 'weekend' : 'holiday'
          }
        end
      end

      breakdown
    end

    private

    def calculate_total_missing_hours
      total_missing = 0

      (start_date..end_date).each do |date|
        next unless should_process_date?(date)
        next if has_manual_leave?(date)

        daily_missing = calculate_daily_missing_hours(date)
        total_missing += daily_missing
      end

      total_missing
    end

    def should_process_date?(date)
      return false if Setting.attendance_exclude_weekends? && date.weekend?
      return false if Setting.attendance_exclude_holidays? && holiday_exists?(date)
      true
    end

    def holiday_exists?(date)
      # Check if Holiday model exists and has record for this date
      return false unless defined?(Holiday)
      Holiday.exists?(date: date)
    rescue
      false
    end

    def has_manual_leave?(date)
      employee.leaves.approved
              .where("start_date <= ? AND end_date >= ?", date, date)
              .exists?
    end

    def calculate_daily_missing_hours(date)
      daily_work_hours = calculate_daily_work_hours(date)
      required_hours = Setting.attendance_daily_required_hours

      [required_hours - daily_work_hours, 0].max
    end

    def calculate_daily_work_hours(date)
      work_periods = employee.attendance_periods
                            .where(date: date)
                            .where(period_type: 'work')

      work_periods.sum(:duration_minutes) / 60.0
    end

    def convert_to_unpaid_days(total_missing_hours)
      daily_threshold = Setting.attendance_daily_required_hours
      (total_missing_hours / daily_threshold).floor
    end

    def calculate_deduction_amount(unpaid_days)
      return 0 if unpaid_days == 0

      daily_rate = employee.daily_salary_rate
      unpaid_days * daily_rate
    end

    def count_processed_days
      (start_date..end_date).count do |date|
        should_process_date?(date) && !has_manual_leave?(date)
      end
    end

    def count_excluded_days
      (start_date..end_date).count do |date|
        !should_process_date?(date) || has_manual_leave?(date)
      end
    end

    def get_leave_type_for_date(date)
      leave = employee.leaves.approved
                     .where("start_date <= ? AND end_date >= ?", date, date)
                     .first
      leave&.leave_type || 'unknown'
    end
  end
end
```

### **2.3 Add Employee Daily Salary Rate Method**

**File: `app/models/employee.rb`**

**Step 2.3.1**: Add daily_salary_rate method and verify exemption field
```ruby
# ADD this method to the Employee model:

def daily_salary_rate
  return 0 unless current_salary_package

  # Assuming 22 working days per month (configurable)
  working_days_per_month = Setting.get('salary', 'working_days_per_month', 22).to_f
  current_salary_package.total_package_value / working_days_per_month
end

# VERIFY exemption field and methods exist:
def exempt_from_attendance_deductions?
  exempt_from_attendance_deductions
end
```

### **2.4 Integrate with Salary Calculation Service**

**File: `app/services/salary/calculation_service.rb`**

**Step 2.4.1**: Add attendance deduction to enhanced deductions
```ruby
# FIND the calculate_enhanced_deductions method (around line 151)
# MODIFY to include attendance deduction:

def calculate_enhanced_deductions(calculation)
  social_security_breakdown = calculate_social_security_breakdown(calculation.gross_salary, @start_date)

  {
    employee_social_security: social_security_breakdown[:employee_social_security] || 0,
    income_tax: 0.0,  # No tax calculations as requested
    medical_insurance: 0.0,  # No medical insurance calculations as requested
    salary_advances: calculate_salary_advances(calculation),
    other_deductions: calculate_other_deductions(calculation),
    leave: calculate_all_leave_deductions(calculation)  # ← CHANGE THIS LINE
  }
end
```

**Step 2.4.2**: Create combined leave deduction method
```ruby
# ADD this new method to the private section:

def calculate_all_leave_deductions(calculation)
  # Manual unpaid leave deductions
  manual_leave_deduction = calculate_leave_deductions(calculation)

  # Attendance-based unpaid leave deductions
  attendance_deduction = calculate_attendance_based_leave_deduction(calculation)

  manual_leave_deduction + attendance_deduction
end

def calculate_attendance_based_leave_deduction(calculation)
  calculator = Salary::AttendanceDeductionCalculator.new(
    employee,
    @start_date,
    @end_date
  )

  calculator.build_calculation_detail(calculation) || 0
end
```

**Step 2.4.3**: Update calculation service includes
```ruby
# FIND the top of the file (around line 2)
# ENSURE these includes are present:

module Salary
  class CalculationService
    include Salary::LeaveIntegration
    include Salary::TaxIntegration
    include Salary::SocialSecurityIntegration
    include Salary::MedicalInsuranceIntegration
    include Salary::CalculationTracking
    # ... rest of the class
```

---

## **Phase 3: Add Statistics and Monitoring** ⚠️ **NEEDS EXECUTION**

### **3.1 Create Attendance Statistics Controller**

**Step 3.1.1**: Create controller directory and file
```bash
# Create directory structure:
mkdir -p app/controllers/api/employees
touch app/controllers/api/employees/attendance_statistics_controller.rb
```

**Step 3.1.2**: Implement the controller
```ruby
# File: app/controllers/api/employees/attendance_statistics_controller.rb
class Api::Employees::AttendanceStatisticsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_employee
  before_action :authorize_read_employee

  def show
    period_start = parse_date_param(:start_date) || Date.current.beginning_of_month
    period_end = parse_date_param(:end_date) || Date.current.end_of_month

    calculator = Salary::AttendanceDeductionCalculator.new(@employee, period_start, period_end)

    render json: {
      data: {
        employee_id: @employee.id,
        period: {
          start_date: period_start,
          end_date: period_end
        },
        summary: calculator.generate_statistics,
        daily_breakdown: calculator.generate_daily_breakdown
      }
    }
  rescue => e
    Rails.logger.error "Attendance statistics error: #{e.message}"
    render json: { error: 'Failed to calculate attendance statistics' }, status: :internal_server_error
  end

  private

  def set_employee
    @employee = Employee.find(params[:employee_id])
  end

  def authorize_read_employee
    # Add authorization logic based on your existing patterns
    # Example:
    authorize @employee, :show?
  rescue
    # Fallback if no authorization system
    head :forbidden unless can_read_employee?
  end

  def can_read_employee?
    # Basic authorization - adjust based on your system
    current_user.admin? ||
    current_user.hr? ||
    current_user.employee == @employee ||
    current_user.manages?(@employee)
  end

  def parse_date_param(param_name)
    return nil unless params[param_name].present?
    Date.parse(params[param_name])
  rescue ArgumentError
    nil
  end
end
```

### **3.2 Add Routes for Statistics API**

**File: `config/routes.rb`**

**Step 3.2.1**: Add nested route for attendance statistics
```ruby
# FIND the employees routes section (around line with resources :employees)
# ADD the nested route:

resources :employees do
  # ... existing nested routes ...

  # Add this line:
  resource :attendance_statistics, only: [:show], controller: 'employees/attendance_statistics'
end
```

### **3.3 Add API Documentation**

**File: `config/initializers/apipie.rb`** (if using Apipie)

**Step 3.3.1**: Document the new endpoint
```ruby
# Add to the API documentation:

api :GET, '/api/employees/:employee_id/attendance_statistics', 'Get employee attendance statistics'
param :employee_id, Integer, desc: 'Employee ID', required: true
param :start_date, String, desc: 'Period start date (YYYY-MM-DD)', required: false
param :end_date, String, desc: 'Period end date (YYYY-MM-DD)', required: false
returns code: 200, desc: 'Attendance statistics' do
  property :data, Hash do
    property :employee_id, Integer
    property :period, Hash do
      property :start_date, String
      property :end_date, String
    end
    property :summary, Hash do
      property :total_missing_hours, Float
      property :unpaid_leave_days_equivalent, Integer
      property :ignored_remaining_hours, Float
      property :projected_deduction, Float
      property :working_days_processed, Integer
      property :excluded_days, Integer
    end
    property :daily_breakdown, Array
  end
end
```

---

## **Phase 4: Testing and Validation** ⚠️ **NEEDS EXECUTION**

### **4.1 Create Unit Tests for Calculator**

**Step 4.1.1**: Create test file
```bash
# Create test directory and file:
mkdir -p spec/services/salary
touch spec/services/salary/attendance_deduction_calculator_spec.rb
```

**Step 4.1.2**: Implement comprehensive tests
```ruby
# File: spec/services/salary/attendance_deduction_calculator_spec.rb
require 'rails_helper'

RSpec.describe Salary::AttendanceDeductionCalculator do
  let(:employee) { create(:employee) }
  let(:start_date) { Date.current.beginning_of_month }
  let(:end_date) { Date.current.end_of_month }
  let(:calculator) { described_class.new(employee, start_date, end_date) }

  before do
    # Set up default settings
    allow(Setting).to receive(:attendance_deductions_enabled?).and_return(true)
    allow(Setting).to receive(:attendance_daily_required_hours).and_return(8.0)
    allow(Setting).to receive(:attendance_exclude_weekends?).and_return(true)
    allow(Setting).to receive(:attendance_exclude_holidays?).and_return(true)
  end

  describe '#calculate_deduction' do
    context 'when employee has missing hours' do
      before do
        # Create attendance periods with missing hours
        create(:attendance_period,
               employee: employee,
               date: start_date + 1.day,
               period_type: 'work',
               duration_minutes: 240) # 4 hours work = 4 hours missing

        create(:attendance_period,
               employee: employee,
               date: start_date + 2.days,
               period_type: 'work',
               duration_minutes: 300) # 5 hours work = 3 hours missing

        # Total: 7 missing hours = 0 unpaid days (less than 8 hour threshold)
      end

      it 'converts missing hours to unpaid leave days' do
        # Add more missing hours to cross threshold
        create(:attendance_period,
               employee: employee,
               date: start_date + 3.days,
               period_type: 'work',
               duration_minutes: 60) # 1 hour work = 7 hours missing

        # Total: 14 missing hours = 1 unpaid day
        expect(calculator.calculate_deduction).to be > 0
      end

      it 'ignores remaining hours less than daily threshold' do
        # 7 missing hours = 0 unpaid days (ignores the 7 hours)
        expect(calculator.calculate_deduction).to eq(0)
      end

      it 'excludes weekends from calculation' do
        weekend_date = start_date.beginning_of_week + 5.days # Saturday

        # Create zero work on weekend
        create(:attendance_period,
               employee: employee,
               date: weekend_date,
               period_type: 'work',
               duration_minutes: 0)

        # Should not count weekend missing hours
        expect(calculator.send(:should_process_date?, weekend_date)).to be_falsey
      end

      it 'skips days with manual leave' do
        leave_date = start_date + 1.day

        create(:leave,
               employee: employee,
               start_date: leave_date,
               end_date: leave_date,
               leave_type: :annual,
               status: :approved)

        expect(calculator.send(:has_manual_leave?, leave_date)).to be_truthy
      end
    end

    context 'when employee is exempt' do
      before do
        allow(employee).to receive(:exempt_from_attendance_deductions?).and_return(true)
      end

      it 'returns zero deduction' do
        expect(calculator.calculate_deduction).to eq(0)
      end
    end

    context 'when attendance deductions are disabled' do
      before do
        allow(Setting).to receive(:attendance_deductions_enabled?).and_return(false)
      end

      it 'returns zero deduction' do
        expect(calculator.calculate_deduction).to eq(0)
      end
    end
  end

  describe '#generate_statistics' do
    before do
      create(:attendance_period,
             employee: employee,
             date: start_date + 1.day,
             period_type: 'work',
             duration_minutes: 240) # 4 hours work = 4 hours missing
    end

    it 'provides comprehensive attendance summary' do
      stats = calculator.generate_statistics

      expect(stats).to include(
        :total_missing_hours,
        :unpaid_leave_days_equivalent,
        :ignored_remaining_hours,
        :projected_deduction,
        :working_days_processed,
        :excluded_days
      )
    end

    it 'shows daily breakdown with missing hours' do
      breakdown = calculator.generate_daily_breakdown

      expect(breakdown).to be_an(Array)
      expect(breakdown.first).to include(:date, :status)
    end
  end

  describe '#build_calculation_detail' do
    let(:calculation) { build(:salary_calculation, employee: employee) }

    context 'when there are unpaid leave days' do
      before do
        create(:attendance_period,
               employee: employee,
               date: start_date + 1.day,
               period_type: 'work',
               duration_minutes: 0) # 0 hours = 8 hours missing = 1 unpaid day
      end

      it 'builds calculation detail with correct category' do
        calculator.build_calculation_detail(calculation)

        detail = calculation.calculation_details.last
        expect(detail.category).to eq('leave_unpaid')
        expect(detail.detail_type).to eq('deduction')
        expect(detail.description).to include('attendance-based')
      end
    end

    context 'when there are no unpaid leave days' do
      it 'does not build calculation detail' do
        expect {
          calculator.build_calculation_detail(calculation)
        }.not_to change { calculation.calculation_details.count }
      end
    end
  end
end
```

### **4.2 Create Integration Tests**

**Step 4.2.1**: Create integration test file
```bash
touch spec/integration/attendance_deduction_integration_spec.rb
```

**Step 4.2.2**: Test full salary calculation flow
```ruby
# File: spec/integration/attendance_deduction_integration_spec.rb
require 'rails_helper'

RSpec.describe 'Attendance Deduction Integration' do
  let(:employee) { create(:employee_with_salary_package) }
  let(:start_date) { Date.current.beginning_of_month }
  let(:end_date) { Date.current.end_of_month }

  before do
    allow(Setting).to receive(:attendance_deductions_enabled?).and_return(true)
    allow(Setting).to receive(:attendance_daily_required_hours).and_return(8.0)
  end

  describe 'Salary calculation with attendance deductions' do
    context 'when employee has insufficient attendance' do
      before do
        # Create 16 hours of missing work (2 unpaid days)
        2.times do |i|
          create(:attendance_period,
                 employee: employee,
                 date: start_date + i.days,
                 period_type: 'work',
                 duration_minutes: 0) # 0 work = 8 hours missing each day
        end
      end

      it 'includes attendance-based unpaid leave in salary calculation' do
        service = Salary::CalculationService.new(
          employee: employee,
          start_date: start_date,
          end_date: end_date,
          reason: 'monthly'
        )

        calculation = service.calculate

        # Should have leave deduction
        leave_details = calculation.calculation_details
                                  .where(category: 'leave_unpaid')
                                  .where("description LIKE ?", "%attendance-based%")

        expect(leave_details).to exist
        expect(leave_details.first.amount).to be > 0
      end
    end
  end
end
```

### **4.3 Create Controller Tests**

**Step 4.3.1**: Create controller test file
```bash
touch spec/controllers/api/employees/attendance_statistics_controller_spec.rb
```

**Step 4.3.2**: Test API endpoints
```ruby
# File: spec/controllers/api/employees/attendance_statistics_controller_spec.rb
require 'rails_helper'

RSpec.describe Api::Employees::AttendanceStatisticsController, type: :controller do
  let(:user) { create(:user, :hr) }
  let(:employee) { create(:employee) }

  before do
    sign_in user
  end

  describe 'GET #show' do
    it 'returns attendance statistics' do
      get :show, params: { employee_id: employee.id }

      expect(response).to have_http_status(:success)

      json_response = JSON.parse(response.body)
      expect(json_response['data']).to include(
        'employee_id',
        'period',
        'summary',
        'daily_breakdown'
      )
    end

    it 'accepts custom date range' do
      start_date = '2025-01-01'
      end_date = '2025-01-31'

      get :show, params: {
        employee_id: employee.id,
        start_date: start_date,
        end_date: end_date
      }

      expect(response).to have_http_status(:success)

      json_response = JSON.parse(response.body)
      period = json_response['data']['period']
      expect(period['start_date']).to eq(start_date)
      expect(period['end_date']).to eq(end_date)
    end
  end
end
```

---

## **Benefits of V2 Approach**

✅ **Dramatically Simplified**: 80% less code than V1  
✅ **Real-time Accuracy**: Always reflects current attendance data  
✅ **No Data Inconsistency**: No stale auto-generated records  
✅ **Same Business Outcome**: Unpaid leave deductions in salary slip  
✅ **Better Performance**: No background jobs or complex workflows  
✅ **Easier Maintenance**: Simple calculation logic  
✅ **Flexible Reporting**: Statistics API provides rich attendance insights  
✅ **Clean Integration**: Uses existing unpaid leave category  

## **Implementation Timeline**

**Week 1**: Phase 1 (Cleanup) + Phase 2.1 (Calculator)  
**Week 2**: Phase 2.2 (Integration) + Phase 3 (Statistics)  
**Week 3**: Phase 4 (Testing) + Documentation  

**Total: ~3 weeks vs ~8 weeks for V1 approach**
