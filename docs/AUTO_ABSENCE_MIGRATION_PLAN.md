# **Auto-Generated Absence Migration Plan**

**Status: ⚠️ PARTIALLY COMPLETED - NEEDS COMPLETION**
**Date: 2025-07-11**
**Started: 2025-07-11**
**Current Issue: Hybrid system still calculating both attendance AND leave deductions**

## **Overview**

Move auto-generated attendance absences from `attendance_periods` table to `leaves` table with a new `auto_absence` leave type, creating a cleaner separation between attendance tracking and leave management.

## **Current System Issues**

- Auto-generated leaves stored in `attendance_periods` table with `period_type: 'leave'`
- Mixing attendance tracking with leave management concepts
- Complex salary calculation logic handling two different tables
- Confusing semantics: attendance periods shouldn't contain "leaves"

## **Target Architecture**

- **Manual leaves**: Employee-requested leaves in `leaves` table (`annual`, `sick`, `unpaid`, etc.)
- **Auto-generated absences**: System-detected absences in `leaves` table with `auto_absence` type
- **Attendance periods**: Pure attendance tracking (`work`, `break`, `late`, `early_departure`, `early_arrival`)

---

## **Phase 0: Pre-execution Fixes**

### **0.1 Current Settings Method Names**

```ruby
# Found in app/models/setting/attendance_settings.rb:
# - attendance_auto_leave_enabled? (controls generation)
# - attendance_deductions_enabled? (controls salary deductions)

# These are the correct method names to use throughout the plan
```

### **0.2 Clean Up Existing Leave Periods**

```ruby
# Since system is fresh, clean up any existing leave periods in attendance_periods
# Run this before starting Phase 1:

# In Rails console or migration:
Attendance::Period.where(period_type: 'leave').delete_all
puts "Cleaned up existing leave periods from attendance_periods table"
```

### **0.3 Add Missing Model Methods**

```ruby
# app/models/leave.rb - Add auto_absence helper method:
def auto_absence?
  leave_type == 'auto_absence'
end

# Add exemption check for auto_absence leaves:
def subject_to_exemption?
  auto_absence? # Only auto_absence leaves respect attendance exemptions
end
```

### **0.4 Update Approval Logic**

```ruby
# app/models/concerns/leave/approval_concern.rb or app/models/leave.rb
# Exclude auto_absence from approval workflows:

def requires_approval?
  return false if auto_absence? # Auto-absence leaves bypass approval

  # Existing approval logic for other leave types...
  true
end

def auto_approve_if_system_generated
  if auto_absence?
    self.status = :approved
    self.approved_at = Time.current
    # Don't create approval_request for auto_absence
  end
end

# Add callback:
before_save :auto_approve_if_system_generated
```

---

## **Phase 1: Database Schema Updates**

### **1.1 Update Leave Model Enum**

```ruby
# app/models/leave.rb
enum :leave_type, {
  annual: 0,
  sick: 1,
  marriage: 2,
  maternity: 3,
  paternity: 4,
  unpaid: 5,
  auto_absence: 6  # NEW: Auto-generated from attendance system
}
```

---

## **Phase 2: Update Attendance System**

### **2.1 Modify PeriodService**

```ruby
# app/services/attendance/period_service.rb

# REPLACE this method:
def create_leave_period(notes, auto_generated = true)
  ::Attendance::Period.create!(
    employee: employee,
    date: date,
    period_type: ::Attendance::Period::PERIOD_TYPES[:leave],
    # ... rest of attendance period creation
  )
end

# WITH this method:
def create_auto_absence_leave(notes)
  Leave.create!(
    employee: employee,
    leave_type: :auto_absence,
    start_date: date,
    end_date: date,
    status: :approved,  # Auto-approved by system
    reason: notes,
    leave_duration: :full_day
  )
end

# UPDATE method calls:
# Line 329: create_leave_period("Auto-generated: No attendance recorded", true)
# BECOMES: create_auto_absence_leave("Auto-generated: No attendance recorded")

# Line 33: create_leave_period('Approved leave', false)
# BECOMES: # Remove this - manual leaves should go through normal approval process
```

### **2.2 Update AccumulatedHours Model**

```ruby
# app/models/attendance/accumulated_hours.rb

# REPLACE this method:
def self.create_leave_period(employee, date, converted_hours)
  Attendance::Period.create!(
    employee: employee,
    date: date,
    period_type: 'leave',
    auto_generated: true,
    # ... rest of period creation
  )
end

# WITH this method:
def self.create_auto_absence_leave(employee, date, converted_hours)
  Leave.create!(
    employee: employee,
    leave_type: :auto_absence,
    start_date: date,
    end_date: date,
    status: :approved,
    reason: "Auto-generated absence: Converted from #{converted_hours.round(2)} accumulated missing hours",
    leave_duration: :full_day
  )
end

# UPDATE method calls:
# Line 105: self.class.create_leave_period(employee, date, missing_hours)
# BECOMES: self.class.create_auto_absence_leave(employee, date, missing_hours)
```

---

## **Phase 3: Update Salary Calculation System**

### **3.1 Simplify LeaveIntegration**

```ruby
# app/models/concerns/salary/leave_integration.rb

def calculate_leave_deductions(calculation)
  start_date = calculation.period_start_date
  end_date = calculation.period_end_date

  # Get all unpaid and auto-absence leaves for this period
  leave_query = employee.leaves
                        .approved
                        .where(leave_type: [:unpaid, :auto_absence])
                        .where("start_date <= ? AND end_date >= ?", end_date, start_date)

  # Filter out auto-absence leaves if salary deductions are disabled
  unless Setting.attendance_deductions_enabled?
    leave_query = leave_query.where.not(leave_type: :auto_absence)
  end

  total_deduction = 0

  leave_query.each do |leave|
    # Skip auto_absence leaves if employee is exempt from attendance deductions
    if leave.auto_absence? && calculation.employee.exempt_from_attendance_deductions
      next
    end

    # Calculate days in this period
    leave_start = [leave.start_date, start_date].max
    leave_end = [leave.end_date, end_date].min

    # Calculate business days (excluding weekends)
    business_days = calculate_working_days(leave_start, leave_end)

    # Adjust for half-days
    if leave.respond_to?(:half_day?) && leave.half_day?
      business_days = business_days * 0.5
    end

    # Calculate daily rate
    working_days_in_month = calculate_working_days(start_date, end_date)
    daily_rate = working_days_in_month > 0 ? calculation.gross_salary / working_days_in_month : 0

    # Calculate deduction
    deduction = daily_rate * business_days
    total_deduction += deduction

    # Track this specific leave deduction with appropriate category
    category = leave.auto_absence? ? 'leave_auto_absence' : 'leave_unpaid'

    calculation.calculation_details.build(
      detail_type: 'deduction',
      category: category,
      amount: deduction,
      description: "#{leave.leave_type.humanize} leave from #{leave_start} to #{leave_end} (#{business_days} business days)",
      reference: leave
    )
  end

  total_deduction
end
```

### **3.2 Update Computed Fields**

```ruby
# app/models/concerns/salary_calculation/computed_fields.rb

def leave_deductions
  categories = ['leave_unpaid']

  # Include auto-absence deductions if enabled and employee is not exempt
  if attendance_deductions_enabled? && !employee_exempt_from_attendance_deductions?
    categories += ['leave_auto_absence']
  end

  calculation_details
    .where(detail_type: 'deduction', category: categories)
    .sum(:amount)
end
```

### **3.3 Remove AttendanceIntegration (No Longer Needed)**

```ruby
# app/models/concerns/salary/attendance_integration.rb
# DELETE this entire file - attendance deductions now handled through leaves

# app/services/salary/calculation_service.rb
# REMOVE this line:
# attendance: calculate_attendance_deductions(calculation),

# UPDATE calculate_enhanced_deductions method:
def calculate_enhanced_deductions(calculation)
  social_security_breakdown = calculate_social_security_breakdown(calculation.gross_salary, @start_date)

  {
    employee_social_security: social_security_breakdown[:employee_social_security] || 0,
    income_tax: calculate_income_tax(calculation.gross_salary, @start_date),
    medical_insurance: calculate_medical_insurance(calculation),
    salary_advances: calculate_salary_advances(calculation),
    other_deductions: calculate_other_deductions(calculation),
    leave: calculate_leave_deductions(calculation)  # Only leave deductions now
  }
end
```

---

## **Phase 4: Clean Up Attendance Periods**

### **4.1 Update Period Model**

```ruby
# app/models/attendance/period.rb

# REMOVE leave from PERIOD_TYPES:
PERIOD_TYPES = {
  work: 'work',
  break: 'break',
  late: 'late',
  early_departure: 'early_departure',
  early_arrival: 'early_arrival'
  # REMOVE: leave: 'leave'
}.freeze

# REMOVE these scopes:
# scope :leave_periods
# scope :auto_generated_leaves
# scope :manual_leaves

# REMOVE these methods:
# def leave_period?
# def auto_generated_leave?
# def manual_leave?
# def leave_type (lines 108-111)
```

---

## **Phase 5: Update Settings and Configuration**

### **5.1 Update Setting Descriptions**

```ruby
# db/migrate/xxx_update_attendance_setting_descriptions.rb
class UpdateAttendanceSettingDescriptions < ActiveRecord::Migration[8.0]
  def up
    execute(<<-SQL)
      UPDATE settings
      SET description = 'Include auto-generated absence leaves in salary deductions'
      WHERE namespace = 'attendance' AND key = 'deductions_enabled'
    SQL
  end

  def down
    execute(<<-SQL)
      UPDATE settings
      SET description = 'Enable attendance-based salary deductions globally'
      WHERE namespace = 'attendance' AND key = 'deductions_enabled'
    SQL
  end
end
```

### **5.2 Update Method Comments**

```ruby
# app/models/setting/attendance_settings.rb
# UPDATE comment for clarity:
def attendance_deductions_enabled?
  # Controls whether auto-generated absence leaves are included in salary deductions
  get('attendance', 'deductions_enabled', false)
end
```

---

## **Phase 6: Update Tests**

### **6.1 Update Factory**

```ruby
# spec/factories/leaves.rb
# ADD new trait:
trait :auto_absence do
  leave_type { :auto_absence }
  status { :approved }
  reason { 'Auto-generated absence from attendance system' }
  start_date { Date.current }
  end_date { Date.current }
end
```

### **6.2 Update Specific Test Files**

```ruby
# spec/models/leave_spec.rb
# - Add tests for auto_absence? method
# - Add tests for approval bypass logic
# - Add tests for exemption handling

# spec/services/attendance/period_service_spec.rb
# - Update tests that create leave periods to create Leave records instead
# - Test create_auto_absence_leave method

# spec/models/attendance/accumulated_hours_spec.rb
# - Update tests for create_auto_absence_leave method

# spec/models/concerns/salary_calculation/computed_fields_spec.rb
# - Update leave_deductions tests to handle auto_absence
# - Test exemption logic for auto_absence leaves

# spec/services/salary/calculation_service_spec.rb
# - Update tests to use new leave structure
# - Test attendance_deductions_enabled? setting behavior

# spec/controllers/api/leaves_controller_spec.rb
# - Add tests preventing manual auto_absence creation/editing
# - Test authorization for auto_absence visibility

# spec/serializers/attendance/period_serializer_spec.rb
# - Remove leave-related serialization tests
```

---

## **Phase 7: Controllers and API Updates**

### **7.1 Update Leave Controllers**

```ruby
# app/controllers/api/people/leaves_controller.rb
# UPDATE create method to prevent manual creation of auto_absence leaves:

def create
  if leave_params[:leave_type] == 'auto_absence'
    return serialize_errors({ detail: "auto_absence leaves can only be created by the system" }, :forbidden)
  end

  # Rest of existing create logic...
end

private

def leave_params
  params.require(:leave).permit(:leave_type, :start_date, :end_date, :reason, :leave_duration)
  # Note: auto_absence should not be in permitted params for manual creation
end
```

### **7.2 Update Leave Serializers**

```ruby
# app/serializers/leave_serializer.rb
# ADD auto_absence to leave_type field documentation:

attribute :leave_type do |leave|
  leave.leave_type
end

# UPDATE API documentation to include new leave type
```

### **7.3 Update Permissions and Policies**

```ruby
# app/policies/leave_policy.rb or similar authorization logic

def create?
  # Prevent users from creating auto_absence leaves manually
  return false if record.auto_absence?

  # Existing permission logic for other leave types...
end

def update?
  # Auto-absence leaves should be read-only
  return false if record.auto_absence?

  # Existing permission logic...
end

def destroy?
  # Auto-absence leaves cannot be deleted manually
  return false if record.auto_absence?

  # Existing permission logic...
end
```

### **7.4 Update API Documentation**

```ruby
# Update Apipie documentation for leaves endpoints:

api! "Creates a new leave request"
param :leave_type, String, required: true,
      desc: "Type of leave (annual, sick, marriage, maternity, paternity, unpaid). Note: auto_absence is system-generated only"

# Add note about auto_absence being read-only
```

### **7.5 Update Frontend/Client Handling**

```ruby
# If there are frontend components that handle leave types:
# - Add auto_absence to leave type dropdowns (but disable for creation)
# - Update leave type display logic
# - Add visual indicators for system-generated leaves
# - Update leave filtering/reporting to handle auto_absence
```

---

## **Phase 7: Controllers and API Updates**

### **7.1 Update Leaves Controller**

```ruby
# app/controllers/api/leaves_controller.rb

# UPDATE create method to prevent manual creation of auto_absence:
def create
  return unless authorize!(:create, :leave)

  # Prevent manual creation of auto_absence leaves
  if leave_params[:leave_type] == 'auto_absence'
    return serialize_errors({ detail: "auto_absence leaves can only be created by the system" }, :forbidden)
  end

  @leave = Leave.new(leave_params)
  @leave.actor_user_id = current_user.id

  if @leave.save
    serialize_response(@leave, status: :created, include: %w[approval_request documents])
  else
    serialize_errors(@leave.errors)
  end
end

# UPDATE update method to prevent editing auto_absence:
def update
  return unless authorize!(:update, :leave)

  # Prevent editing auto_absence leaves
  if @leave.auto_absence?
    return serialize_errors({ detail: "auto_absence leaves cannot be edited manually" }, :forbidden)
  end

  if @leave.update(leave_params)
    serialize_response(@leave, include: ["approval_request"])
  else
    serialize_errors(@leave.errors)
  end
end

# UPDATE withdraw method to prevent withdrawing auto_absence:
def withdraw
  # Prevent withdrawing auto_absence leaves
  if @leave.auto_absence?
    return serialize_errors({ detail: "auto_absence leaves cannot be withdrawn" }, :forbidden)
  end

  @leave.actor_user_id = current_user.id
  if @leave.update(status: :withdrawn)
    serialize_response(@leave.reload, include: ["approval_request"])
  else
    serialize_errors(@leave.errors)
  end
end

# UPDATE leave_params to exclude auto_absence from manual creation:
private

def leave_params
  permitted_types = [:annual, :sick, :marriage, :maternity, :paternity, :unpaid]

  params.require(:leave).permit(
    :employee_id,
    :leave_duration,
    :start_date,
    :end_date,
    :reason,
    :status,
    documents: []
  ).tap do |whitelisted|
    # Only allow permitted leave types for manual creation
    if params[:leave][:leave_type].present?
      leave_type = params[:leave][:leave_type].to_sym
      whitelisted[:leave_type] = leave_type if permitted_types.include?(leave_type)
    end
  end
end
```

### **7.2 Update Attendance Periods Controller**

```ruby
# app/controllers/api/attendance/periods_controller.rb

# UPDATE period_params to remove leave_type filter (no longer needed):
def period_params
  params.permit(:date, :start_date, :end_date, :period_type, :employee_id, :auto_generated, :year, :month)
  # REMOVE: :leave_type (no longer relevant)
end

# UPDATE API documentation to remove leave period references:
# Line 18: Remove "leave" from period_type description
# Line 20: Remove leave_type parameter documentation
```

### **7.3 Update Leave Serializer**

```ruby
# app/serializers/leave_serializer.rb
# No changes needed - auto_absence will be automatically included in leave_type enum
# The serializer already handles all leave types dynamically
```

### **7.4 Update Attendance Period Serializer**

```ruby
# app/serializers/attendance/period_serializer.rb

# REMOVE leave-related attributes and logic:
# Lines 45-46: Remove 'leave' case from period_type_label
# Lines 71-76: Remove 'leave' case from status_badge
# Lines 84-86: Remove leave_type attribute entirely

attribute :period_type_label do |period|
  case period.period_type
  when 'work'
    'Work Period'
  when 'break'
    'Break Period'
  when 'late'
    'Late Arrival'
  when 'early_departure'
    'Early Departure'
  when 'early_arrival'
    'Early Arrival'
  # REMOVE: when 'leave' case
  else
    period.period_type.humanize
  end
end

attribute :status_badge do |period|
  if period.is_predicted
    { color: 'orange', text: 'Predicted' }
  else
    case period.period_type
    when 'work'
      { color: 'green', text: 'Work' }
    when 'break'
      { color: 'blue', text: 'Break' }
    when 'late'
      { color: 'red', text: 'Late' }
    when 'early_departure'
      { color: 'yellow', text: 'Early Out' }
    when 'early_arrival'
      { color: 'purple', text: 'Early In' }
    # REMOVE: when 'leave' case
    else
      { color: 'gray', text: period.period_type.humanize }
    end
  end
end

# REMOVE leave_type attribute entirely:
# Lines 84-86: Remove leave_type attribute
```

### **7.5 Update Authorization Logic**

```ruby
# app/controllers/api/leaves_controller.rb

# UPDATE authorization methods to handle auto_absence:
def authorize_read_all_or_own
  if can?(:read, :leave)
    @collection = Leave.all
  elsif can?(:manage_own, :leave)
    # Employees can see their own leaves but not auto_absence (system-generated)
    @collection = Leave.where(employee_id: current_user.id)
                       .where.not(leave_type: :auto_absence)
  else
    render_forbidden("You don't have permission to view leaves")
    false
  end
end

# For HR/managers, they can see auto_absence leaves for reporting
# For employees, auto_absence leaves are hidden from their own view
```

### **7.6 Update API Documentation**

```ruby
# Update Apipie documentation:

# app/controllers/api/leaves_controller.rb
param :leave_type, String, required: true,
      desc: "Type of leave (annual, sick, marriage, maternity, paternity, unpaid). Note: auto_absence is system-generated only and cannot be created manually"

# app/controllers/api/attendance/periods_controller.rb
param :period_type, String, desc: "Filter by period type (work, break, late, early_departure, early_arrival)"
# REMOVE: leave from the list
```

---

## **🚨 CRITICAL: INCOMPLETE IMPLEMENTATION IDENTIFIED**

### **Current Status Analysis (2025-07-11):**

#### **✅ COMPLETED PHASES:**

- ✅ **Phase 0**: Pre-execution fixes and cleanup
- ✅ **Phase 1**: Database schema updates (auto_absence enum added)
- ✅ **Phase 2**: Attendance system updates (auto-absence leaves being created)
- ✅ **Phase 4**: Clean up attendance periods (leave type removed)
- ✅ **Phase 5**: Settings and configuration
- ✅ **Phase 6**: Tests (partially updated)
- ✅ **Phase 7**: Controllers and API updates

#### **❌ INCOMPLETE PHASE:**

- ❌ **Phase 3**: Salary calculation updates **NOT FULLY IMPLEMENTED**

#### **🔍 SPECIFIC ISSUES FOUND:**

1. **AttendanceIntegration Still Active** ❌

   - File: `app/models/concerns/salary/attendance_integration.rb` still exists
   - Still being called in calculation service: `attendance: calculate_attendance_deductions(calculation)`
   - **Should be removed entirely per Phase 3.3**

2. **LeaveIntegration Incomplete** ❌

   - File: `app/models/concerns/salary/leave_integration.rb`
   - Still looking for old `attendance_periods` with `period_type: 'leave'`
   - **NOT including `auto_absence` leaves in the query**
   - **Should query `employee.leaves.where(leave_type: [:unpaid, :auto_absence])`**

3. **Hybrid Deduction System** ❌

   - Both attendance AND leave deductions being calculated
   - Results in confusing categorization (attendance vs leave)
   - **Should only have leave deductions**

4. **Computed Fields Mismatch** ❌
   - Added `attendance_deductions` method but this contradicts the plan
   - **Should only have `leave_deductions` method**

---

## **🔧 PHASE 3 COMPLETION REQUIRED**

### **Phase 3.1: Fix LeaveIntegration (CRITICAL)**

```ruby
# app/models/concerns/salary/leave_integration.rb
# REPLACE the entire calculate_leave_deductions method:

def calculate_leave_deductions(calculation)
  start_date = calculation.period_start_date
  end_date = calculation.period_end_date

  # Get all unpaid and auto-absence leaves for this period
  leave_query = employee.leaves
                        .approved
                        .where(leave_type: [:unpaid, :auto_absence])
                        .where("start_date <= ? AND end_date >= ?", end_date, start_date)

  # Filter out auto-absence leaves if salary deductions are disabled
  unless Setting.attendance_deductions_enabled?
    leave_query = leave_query.where.not(leave_type: :auto_absence)
  end

  total_deduction = 0

  leave_query.each do |leave|
    # Skip auto_absence leaves if employee is exempt from attendance deductions
    if leave.auto_absence? && calculation.employee.exempt_from_attendance_deductions
      # Create exemption record for transparency
      calculation.calculation_details.build(
        detail_type: 'exemption',
        category: 'attendance_exempt',
        amount: 0,
        description: 'Employee is exempt from auto-generated leave deductions'
      )
      next
    end

    # Calculate days in this period
    leave_start = [leave.start_date, start_date].max
    leave_end = [leave.end_date, end_date].min

    # Calculate business days (excluding weekends)
    business_days = calculate_working_days(leave_start, leave_end)

    # Adjust for half-days
    if leave.respond_to?(:half_day?) && leave.half_day?
      business_days = business_days * 0.5
    end

    # Calculate daily rate
    working_days_in_month = calculate_working_days(start_date, end_date)
    daily_rate = working_days_in_month > 0 ? calculation.gross_salary / working_days_in_month : 0

    # Calculate deduction
    deduction = daily_rate * business_days
    total_deduction += deduction

    # Track this specific leave deduction with appropriate category
    category = leave.auto_absence? ? 'leave_auto_absence' : 'leave_unpaid'

    calculation.calculation_details.build(
      detail_type: 'deduction',
      category: category,
      amount: deduction,
      description: "#{leave.leave_type.humanize} leave from #{leave_start} to #{leave_end} (#{business_days} business days)",
      reference: leave
    )
  end

  total_deduction
end
```

### **Phase 3.2: Remove AttendanceIntegration (CRITICAL)**

```ruby
# 1. DELETE FILE: app/models/concerns/salary/attendance_integration.rb

# 2. UPDATE: app/services/salary/calculation_service.rb
# REMOVE this line:
include Salary::AttendanceIntegration

# 3. UPDATE: calculate_enhanced_deductions method
def calculate_enhanced_deductions(calculation)
  social_security_breakdown = calculate_social_security_breakdown(calculation.gross_salary, @start_date)

  {
    employee_social_security: social_security_breakdown[:employee_social_security] || 0,
    income_tax: 0.0,  # No tax calculations as requested
    medical_insurance: 0.0,  # No medical insurance calculations as requested
    salary_advances: calculate_salary_advances(calculation),
    other_deductions: calculate_other_deductions(calculation),
    leave: calculate_leave_deductions(calculation)  # ONLY leave deductions now
    # REMOVE: attendance: calculate_attendance_deductions(calculation)
  }
end
```

### **Phase 3.3: Fix Computed Fields (CRITICAL)**

```ruby
# app/models/concerns/salary_calculation/computed_fields.rb

# REMOVE this method (contradicts the plan):
# def attendance_deductions
#   deductions['attendance']&.to_f || 0
# end

# UPDATE leave_deductions method to handle auto_absence:
def leave_deductions
  categories = ['leave_unpaid']

  # Include auto-absence deductions if enabled and employee is not exempt
  if Setting.attendance_deductions_enabled? && !employee.exempt_from_attendance_deductions
    categories += ['leave_auto_absence']
  end

  calculation_details
    .where(detail_type: 'deduction', category: categories)
    .sum(:amount)
end

# UPDATE leaves_to_pay to be an alias for leave_deductions:
def leaves_to_pay
  leave_deductions
end
```

### **Phase 3.4: Fix Slip Service (CRITICAL)**

```ruby
# app/services/salary/slip_service.rb

# REMOVE attendance deductions section:
# if @salary_calculation.attendance_deductions > 0
#   rows << render_deduction_row(
#     "Attendance Deductions",
#     "Auto-absence and attendance-based deductions",
#     @salary_calculation.attendance_deductions
#   )
# end

# UPDATE leave deductions section to handle both unpaid and auto-absence:
if @salary_calculation.leave_deductions > 0
  rows << render_deduction_row(
    "Leave Deductions",
    "Unpaid leave and auto-absence deductions",
    @salary_calculation.leave_deductions
  )
end
```

---

## **Execution Order**

1. **Phase 0**: Pre-execution fixes and cleanup ✅
2. **Phase 1**: Database schema updates ✅
3. **Phase 2**: Attendance system updates ✅
4. **Phase 3**: Salary calculation updates ❌ **NEEDS COMPLETION**
5. **Phase 4**: Clean up attendance periods ✅
6. **Phase 5**: Settings and configuration ✅
7. **Phase 6**: Tests ✅
8. **Phase 7**: Controllers and API updates ✅

---

## **🎯 IMMEDIATE ACTION REQUIRED**

To fix the current issue where employee 64's leave deductions (540.0) are showing as "Attendance Deductions" instead of "Leave Deductions":

### **Step 1: Complete Phase 3.1 - Fix LeaveIntegration**

- Update `calculate_leave_deductions` to include `auto_absence` leaves
- Remove old `attendance_periods` logic

### **Step 2: Complete Phase 3.2 - Remove AttendanceIntegration**

- Delete `attendance_integration.rb` file
- Remove from calculation service includes
- Remove `attendance:` from deductions hash

### **Step 3: Complete Phase 3.3 - Fix Computed Fields**

- Remove `attendance_deductions` method
- Update `leave_deductions` to handle auto_absence

### **Step 4: Complete Phase 3.4 - Fix Slip Service**

- Remove attendance deductions section
- Update leave deductions section

### **Expected Result:**

- ✅ Employee 64's 540.0 deduction will show as **"Leave Deductions"**
- ✅ No more "Attendance Deductions" in salary slips
- ✅ Clean leave-based approach as originally planned

---

## **Phase 8: Auto-Leave Generation Logic** ⚠️ **MISSING IMPLEMENTATION**

### **8.1 Fix AccumulatedHours Model**

**Issue**: Current `AccumulatedHours.create_leave_period()` still creates `Attendance::Period` records instead of `Leave` records.

```ruby
# CURRENT (WRONG):
def self.create_leave_period(employee, date, converted_hours)
  Attendance::Period.create!(
    period_type: 'leave',  # ❌ Old system approach
    auto_generated: true,
    # ...
  )
end

# SHOULD BE (CORRECT):
def self.create_auto_absence_leave(employee, date, converted_hours)
  Leave.create!(
    employee: employee,
    leave_type: :auto_absence,  # ✅ New system approach
    start_date: date,
    end_date: date,
    status: :approved,  # Auto-approved
    auto_generated: true,
    notes: "Auto-generated: Converted from #{converted_hours.round(2)} accumulated hours"
  )
end
```

### **8.2 Implement Daily Auto-Leave Detection**

**Missing**: Service that detects when daily work < 5 hours and creates auto-absence leaves.

```ruby
# app/services/attendance/auto_absence_service.rb
class Attendance::AutoAbsenceService
  def self.process_daily_attendance(employee, date)
    return unless Setting.attendance_auto_leave_enabled?
    return if employee.exempt_from_attendance_deductions?

    daily_work_hours = calculate_daily_work_hours(employee, date)

    if daily_work_hours == 0
      # Full day absence - create immediate auto-absence leave
      create_full_day_auto_absence(employee, date)
    elsif daily_work_hours < 5.0
      # Partial work - accumulate missing hours
      missing_hours = 8.0 - daily_work_hours
      AccumulatedHours.add_missing_hours(employee, date, missing_hours)
    end
  end

  private

  def self.create_full_day_auto_absence(employee, date)
    Leave.create!(
      employee: employee,
      leave_type: :auto_absence,
      start_date: date,
      end_date: date,
      status: :approved,
      auto_generated: true,
      notes: "Auto-generated: Full day absence"
    )
  end
end
```

### **8.3 Add Trigger Mechanisms**

**Options for triggering auto-leave generation:**

1. **Daily Job** (Recommended):

   ```ruby
   # app/jobs/attendance/daily_auto_absence_job.rb
   class Attendance::DailyAutoAbsenceJob < ApplicationJob
     def perform(date = Date.current - 1.day)
       Employee.active.find_each do |employee|
         Attendance::AutoAbsenceService.process_daily_attendance(employee, date)
       end
     end
   end
   ```

2. **Period Creation Trigger**:

   ```ruby
   # In Attendance::PeriodService.calculate_periods
   after_create_periods do
     Attendance::AutoAbsenceService.process_daily_attendance(@employee, @date)
   end
   ```

3. **Manual Trigger** (for testing):
   ```ruby
   # In Rails console
   Attendance::AutoAbsenceService.process_daily_attendance(Employee.find(64), Date.parse('2025-05-01'))
   ```

---

## **Phase 9: Auto-Approval Logic** ⚠️ **MISSING IMPLEMENTATION**

### **9.1 Auto-Approval for Auto-Generated Leaves**

```ruby
# app/models/leave.rb - Add callback
class Leave < ApplicationRecord
  before_create :auto_approve_if_system_generated

  private

  def auto_approve_if_system_generated
    if auto_absence? && auto_generated?
      self.status = :approved
      # Skip approval workflow entirely
    end
  end
end
```

### **9.2 Prevent Manual Editing of Auto-Generated Leaves**

```ruby
# app/models/leave.rb - Add validations
validates :leave_type, immutability: true, if: :auto_generated?
validates :start_date, immutability: true, if: :auto_generated?
validates :end_date, immutability: true, if: :auto_generated?

def editable?
  !auto_generated?
end

def withdrawable?
  !auto_generated? && status != :approved
end
```

---

## **Phase 10: Integration and Testing** ⚠️ **NEEDS COMPLETION**

### **10.1 Update Period Service Integration**

```ruby
# app/services/attendance/period_service.rb
class Attendance::PeriodService
  def calculate_periods
    # ... existing period calculation logic ...

    # After periods are calculated, check for auto-absence
    if periods_calculated_successfully?
      Attendance::AutoAbsenceService.process_daily_attendance(@employee, @date)
    end

    periods
  end
end
```

### **10.2 Add Comprehensive Testing**

- Test daily auto-absence detection (0 hours = immediate leave)
- Test threshold conversion (9+ accumulated hours = leave)
- Test exemption handling
- Test auto-approval workflow
- Test salary calculation integration

### **10.3 Add Monitoring and Logging**

```ruby
# Log auto-absence creation for audit trail
Rails.logger.info "Auto-absence leave created for #{employee.name} on #{date}"
```

---

## **Phase 11: Configuration and Edge Cases** ⚠️ **NEEDS COMPLETION**

### **11.1 Remove Unnecessary MonthlyResetJob**

**Analysis**: Current system is self-cleaning - records marked as `converted` are excluded from future calculations.

```ruby
# REMOVE: app/jobs/attendance/monthly_reset_job.rb
# REMOVE: app/workers/attendance/monthly_reset_worker.rb
# REASON: Unnecessary complexity - system auto-cleans via 'converted' status
```

### **11.2 Add Holiday and Weekend Exclusion Settings**

```ruby
# app/models/setting/attendance_settings.rb - Add new settings:
def attendance_exclude_weekends?
  get('attendance', 'exclude_weekends', true)
end

def attendance_exclude_holidays?
  get('attendance', 'exclude_holidays', true)
end

def attendance_accumulated_hours_threshold
  get('attendance', 'accumulated_hours_threshold', 9.0).to_f
end

def attendance_minimum_work_hours
  get('attendance', 'minimum_work_hours', 5.0).to_f
end

def attendance_daily_required_hours
  get('attendance', 'daily_required_hours', 8.0).to_f
end
```

### **11.3 Implement Date Processing Logic**

```ruby
# In Attendance::AutoAbsenceService
def self.should_process_date?(date)
  # Skip weekends if configured
  return false if Setting.attendance_exclude_weekends? && date.weekend?

  # Skip holidays if configured (assumes Holiday model exists)
  return false if Setting.attendance_exclude_holidays? && Holiday.exists?(date: date)

  true
end

def self.process_daily_attendance(employee, date)
  return unless Setting.attendance_auto_leave_enabled?
  return unless should_process_date?(date)
  return if employee.exempt_from_attendance_deductions?

  # PRIORITY: Manual leave takes precedence over auto-absence
  return if has_manual_leave_for_date?(employee, date)

  # Proceed with auto-absence logic...
end

private

def self.has_manual_leave_for_date?(employee, date)
  employee.leaves.where(start_date: date, end_date: date)
                 .where.not(leave_type: :auto_absence)
                 .exists?
end
```

### **11.4 Handle Manual Leave + Auto-Absence Conflicts**

**Best Practice**: Manual leave always takes precedence over auto-generated absence.

```ruby
# Conflict Resolution Strategy:
# 1. ✅ Manual leave exists → Skip auto-absence generation
# 2. ✅ Auto-absence exists + Manual leave requested → Allow manual leave, mark auto-absence as superseded
# 3. ✅ Partial day manual leave → Calculate remaining hours for auto-absence

# In Leave model - Add conflict resolution:
before_create :handle_auto_absence_conflicts, unless: :auto_absence?

private

def handle_auto_absence_conflicts
  # If manual leave overlaps with auto-absence, mark auto-absence as superseded
  conflicting_auto_absences = employee.leaves
    .where(leave_type: :auto_absence)
    .where(start_date: start_date, end_date: end_date)

  conflicting_auto_absences.update_all(
    status: :superseded,
    notes: "Superseded by manual #{leave_type} leave"
  )
end
```

### **11.5 Data Cleanup Strategy**

```ruby
# Clean up existing leave periods in attendance_periods table
# Run this as part of Phase 11 implementation:

# 1. Remove existing leave periods from attendance_periods
Attendance::Period.where(period_type: 'leave').delete_all
puts "Cleaned up existing leave periods from attendance_periods table"

# 2. Reset any existing accumulated hours to start fresh
Attendance::AccumulatedHours.update_all(status: 'reset')
puts "Reset existing accumulated hours records"

# 3. Remove monthly reset job scheduling (if exists)
# Remove from cron jobs, sidekiq-cron, or wherever it's scheduled
```

### **11.6 Enhanced Validation and Business Rules**

```ruby
# app/models/leave.rb - Add comprehensive validations
class Leave < ApplicationRecord
  # Existing validations...

  # Auto-absence specific validations
  validates :leave_type, immutability: true, if: :auto_generated?
  validates :start_date, immutability: true, if: :auto_generated?
  validates :end_date, immutability: true, if: :auto_generated?

  # Business rule: Auto-absence leaves are always single day
  validates :start_date, equality: { to: :end_date }, if: :auto_absence?

  # Business rule: Auto-absence leaves cannot be withdrawn
  validate :auto_absence_cannot_be_withdrawn, if: :auto_absence?

  private

  def auto_absence_cannot_be_withdrawn
    if status_changed? && status_was == 'approved' && auto_absence?
      errors.add(:status, 'Auto-absence leaves cannot be withdrawn')
    end
  end
end
```

### **11.7 Monitoring and Audit Trail**

```ruby
# Add comprehensive logging for auto-absence operations
class Attendance::AutoAbsenceService
  def self.process_daily_attendance(employee, date)
    Rails.logger.info "Processing auto-absence for #{employee.name} on #{date}"

    # ... processing logic ...

    if leave_created
      Rails.logger.info "Auto-absence leave created: Employee #{employee.id}, Date #{date}, Type: #{leave_type}"

      # Optional: Create audit record
      AuditLog.create!(
        action: 'auto_absence_created',
        resource: leave_created,
        details: {
          employee_id: employee.id,
          date: date,
          work_hours: daily_work_hours,
          missing_hours: missing_hours
        }
      )
    end
  end
end
```

### **11.8 Testing Requirements for Phase 11**

```ruby
# Comprehensive test scenarios to implement:

# 1. Weekend/Holiday Exclusion Tests
it 'skips auto-absence generation on weekends when configured'
it 'skips auto-absence generation on holidays when configured'
it 'processes auto-absence on weekends when weekend exclusion disabled'

# 2. Manual Leave Precedence Tests
it 'skips auto-absence when manual leave exists for same date'
it 'supersedes existing auto-absence when manual leave is created'
it 'handles partial day manual leave with remaining auto-absence'

# 3. Configuration Tests
it 'respects custom threshold settings'
it 'respects custom minimum work hours'
it 'respects custom daily required hours'

# 4. Edge Case Tests
it 'handles employee with non-standard work schedule'
it 'handles multiple auto-absence leaves in same month'
it 'handles threshold conversion with mixed active/converted records'

# 5. Data Cleanup Tests
it 'successfully removes old leave periods from attendance_periods'
it 'resets accumulated hours without affecting other data'
```

---

## **Benefits**

✅ **Clean separation**: Attendance periods only track attendance, leaves track absences
✅ **Unified leave management**: All leaves in one table with proper typing
✅ **Simpler salary calculation**: Only query leaves table
✅ **Better business logic**: Different rules for different leave types
✅ **Clearer semantics**: `auto_absence` vs `unpaid` are distinct concepts
✅ **Easier reporting**: All leave data in one place
✅ **Better approval workflows**: Can handle auto vs manual leaves differently

---

## **Risk Mitigation**

- **Data migration**: Backup database before migration
- **Rollback plan**: Migration includes down methods
- **Testing**: Comprehensive test coverage for all changes
- **Gradual deployment**: Can be deployed in phases
- **Backward compatibility**: Legacy methods maintained during transition

---

## **Notes**

- **No database migration needed**: Since we're just adding a new enum value to an existing integer field
- **No data migration needed**: We'll start fresh with the new system going forward
- **Clean implementation**: Focus on new functionality rather than migrating old data
- **Current hybrid system**: Must be completed to avoid confusion between attendance and leave deductions
