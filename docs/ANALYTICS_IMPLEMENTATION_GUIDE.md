# Analytics Dashboard Implementation Guide

## Overview

This guide shows you how to add an analytics dashboard to your Athar EMS system with **zero frontend complexity**. Data will arrive ready for Recharts consumption.

## What You'll Build (Charts Only - Metric Cards Already Done)

✅ **Metric Cards**: Already implemented and working with statistics API

- Pending Requests, Average Daily Work Hours, Daily Attendance Rate, Total Number of Employees

🎯 **New Charts to Implement**:

- **Daily Work Hours Chart**: متوسط العمل اليومي خلال الشهر (Line chart with month selector)
- **Leave Distribution Chart**: توزيع الإجازات خلال السنة (Bar chart with status indicators)
- **Monthly Requests Chart**: نسبة الطلبات خلال السنة (Year-over-year bar chart)
- **Requests Table**: كل الطلبات (Comprehensive data table with pagination)

## Quick Setup (5 Steps)

### Step 1: Add Required Gems

```ruby
# Add to Gemfile
gem 'groupdate'  # Time series grouping - CRITICAL
gem 'redis'      # Caching for performance

# Then run
bundle install
```

### Step 2: Create Chart Data Service (Charts Only)

Since metric cards are already working, we'll focus on chart data transformation:

### Step 3: Create Chart Data Service

```bash
mkdir -p app/services/chart_data
```

Create `app/services/chart_data/base_transformer.rb`:

```ruby
module ChartData
  class BaseTransformer
    attr_reader :data, :options, :chart_type

    def initialize(data, options = {})
      @data = data
      @options = options
      @chart_type = options[:chart_type] || :line
    end

    def transform
      Rails.cache.fetch(cache_key, expires_in: 1.hour) do
        case chart_type
        when :line
          transform_line_chart
        when :bar
          transform_bar_chart
        else
          raise "Unsupported chart type: #{chart_type}"
        end
      end
    end

    private

    def transform_line_chart
      # Handle groupdate results: {"2025-01-01" => 510, "2025-01-02" => 432}
      data.map.with_index do |(date, minutes), index|
        hours = (minutes.to_f / 60).round(2)
        {
          x: Date.parse(date.to_s).day,
          y: hours,
          label: I18n.l(Date.parse(date.to_s), format: :short),
          formatted_value: "#{hours}h"
        }
      end
    end

    def transform_bar_chart
      # Handle grouped data: {["annual", "approved"] => 10, ["sick", "pending"] => 5}
      categories = data.keys.map(&:first).uniq
      series_keys = data.keys.map(&:second).uniq

      categories.map do |category|
        row = { category: category }

        series_keys.each do |series|
          row[series] = data[[category, series]] || 0
        end

        row[:total] = series_keys.sum { |series| row[series] }
        row
      end
    end

    def cache_key
      "chart_data:#{chart_type}:#{Digest::MD5.hexdigest(data.to_s)}"
    end
  end
end
```

### Step 4: Create Analytics Controller

Create `app/controllers/api/analytics_controller.rb`:

```ruby
class Api::AnalyticsController < ApplicationController
  before_action :authenticate_session!

  def daily_work_hours
    authorize!(:read, :analytics)

    # Get raw data using existing models
    raw_data = Attendance::Period.work_periods
                                 .where(date: date_range)
                                 .where(employee_filter)
                                 .group_by_day(:date, range: date_range)
                                 .sum(:duration_minutes)

    # Transform to Recharts format
    chart_data = ChartData::BaseTransformer.new(
      raw_data,
      chart_type: :line
    ).transform

    render json: { data: chart_data }
  end

  def leave_distribution
    authorize!(:read, :analytics)

    # Get raw grouped data
    raw_data = Leave.where(year_filter)
                   .where(employee_filter)
                   .group(:leave_type, :status)
                   .count

    # Transform to Recharts format
    chart_data = ChartData::BaseTransformer.new(
      raw_data,
      chart_type: :bar
    ).transform

    render json: { data: chart_data }
  end

  private

  def date_range
    start_date = params[:start_date]&.to_date || Date.current.beginning_of_month
    end_date = params[:end_date]&.to_date || Date.current.end_of_month
    start_date..end_date
  end

  def employee_filter
    employee_id = params[:employee_id]&.to_i
    employee_id ? { employee_id: employee_id } : {}
  end

  def year_filter
    year = params[:year]&.to_i || Date.current.year
    "EXTRACT(year FROM start_date) = #{year}"
  end
end
```

### Step 5: Add Routes

Add to `config/routes.rb`:

```ruby
namespace :api do
  namespace :analytics do
    get :daily_work_hours
    get :leave_distribution
    get :monthly_requests
  end
end
```

### Step 6: Create Analytics Page (Charts Only)

Create `src/app/[locale]/people/analytics/page.tsx`:

```typescript
"use client";

import React, { useState } from "react";
import { useTranslations } from "next-intl";
import { usePermission } from "@/contexts/PermissionContext";
import { PermissionEnum } from "@/enums/Permission";
import DailyWorkHoursChart from "../_modules/people/_components/analytics/daily-work-hours-chart";
import LeaveDistributionChart from "../_modules/people/_components/analytics/leave-distribution-chart";
import MonthlyRequestsChart from "../_modules/people/_components/analytics/monthly-requests-chart";
import RequestsTable from "../_modules/people/_components/analytics/requests-table";

const AnalyticsPage = () => {
  const t = useTranslations();
  const { hasPermission } = usePermission();

  const [dateRange, setDateRange] = useState({
    start: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    end: new Date(),
  });

  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  const canViewAnalytics = hasPermission(PermissionEnum.READ_STATISTICS);

  if (!canViewAnalytics) {
    return <div className="p-4 text-center">Access denied</div>;
  }

  return (
    <div className="max-md:mt-8 grid grid-cols-12 gap-4">
      {/* Note: Metric cards are already on the main dashboard page */}

      {/* Daily Work Hours Chart */}
      <div className="border rounded-[20px] border-gray-200 col-span-12 max-h-[380px] font-readex_pro">
        <DailyWorkHoursChart dateRange={dateRange} />
      </div>

      {/* Leave Distribution and Monthly Requests */}
      <div className="grid xl:grid-cols-[1.6fr_1fr] col-span-12 gap-4">
        <div className="border rounded-[20px] border-gray-200 max-h-[526px]">
          <LeaveDistributionChart year={selectedYear} />
        </div>
        <div className="border rounded-[20px] border-gray-200 max-h-[530px]">
          <MonthlyRequestsChart year={selectedYear} />
        </div>
      </div>

      {/* Requests Table */}
      <div className="border rounded-[20px] border-gray-200 col-span-12">
        <RequestsTable dateRange={dateRange} />
      </div>
    </div>
  );
};

export default AnalyticsPage;
```

### Step 7: Frontend Integration

Create `src/app/[locale]/_modules/people/hooks/analytics/useDailyWorkHours.ts`:

```typescript
import useSWR from "swr";
import { fetcher } from "@services/fetcher";
import { format } from "date-fns";

export const useDailyWorkHours = ({
  startDate,
  endDate,
}: {
  startDate: Date;
  endDate: Date;
}) => {
  const params = new URLSearchParams({
    start_date: format(startDate, "yyyy-MM-dd"),
    end_date: format(endDate, "yyyy-MM-dd"),
  });

  const { data, error, isLoading } = useSWR(
    `/api/analytics/daily_work_hours?${params.toString()}`,
    fetcher
  );

  return {
    data: data?.data || [],
    error,
    isLoading,
  };
};
```

Create chart component `src/app/[locale]/_modules/people/_components/analytics/daily-work-hours-chart.tsx`:

```typescript
"use client";

import React from "react";
import { LineChart, Line, XAxis, YAxis, ResponsiveContainer } from "recharts";
import { useDailyWorkHours } from "../../hooks/analytics/useDailyWorkHours";

interface Props {
  dateRange: { start: Date; end: Date };
}

const DailyWorkHoursChart: React.FC<Props> = ({ dateRange }) => {
  const { data, isLoading } = useDailyWorkHours({
    startDate: dateRange.start,
    endDate: dateRange.end,
  });

  if (isLoading) return <div>Loading...</div>;

  return (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={data}>
        <XAxis dataKey="x" />
        <YAxis />
        <Line dataKey="y" stroke="#10B981" strokeWidth={2} />
      </LineChart>
    </ResponsiveContainer>
  );
};

export default DailyWorkHoursChart;
```

## Sample Data Output

Your backend will return Recharts-ready data like this:

```json
{
  "data": [
    {
      "x": 1,
      "y": 8.5,
      "label": "Jan 1, 2025",
      "formatted_value": "8.5h"
    },
    {
      "x": 2,
      "y": 7.2,
      "label": "Jan 2, 2025",
      "formatted_value": "7.2h"
    }
  ]
}
```

## Database Optimizations

Add these indexes for better performance:

```ruby
# Create migration: rails generate migration AddAnalyticsIndexes
class AddAnalyticsIndexes < ActiveRecord::Migration[7.0]
  def change
    # For daily work hours queries
    add_index :attendance_periods, [:date, :period_type, :employee_id],
              name: 'idx_attendance_periods_analytics'

    # For leave distribution queries
    add_index :leaves, [:leave_type, :status, :employee_id],
              name: 'idx_leaves_analytics'

    # For year-based queries
    add_index :leaves, ["EXTRACT(year FROM start_date)", :employee_id],
              name: 'idx_leaves_year_employee'
  end
end
```

## Translation Keys

Add to `messages/ar.json` and `messages/en.json`:

```json
{
  "analytics": {
    "title": "لوحة التحليلات",
    "charts": {
      "dailyWorkHours": {
        "title": "متوسط العمل اليومي خلال الشهر",
        "hours": "ساعات",
        "day": "يوم"
      },
      "leaveDistribution": {
        "title": "توزيع الإجازات خلال السنة"
      },
      "monthlyRequests": {
        "title": "نسبة الطلبات خلال السنة"
      }
    },
    "metrics": {
      "totalWorkingHours": "ساعات العمل الإجمالي",
      "pendingRequests": "الطلبات المعلقة",
      "managementLeaves": "الوزارات المشرفة",
      "attendanceDays": "أيام الحضور"
    }
  }
}
```

## Key Benefits

1. **Zero Frontend Complexity**: Data arrives ready for Recharts
2. **Leverages Existing Infrastructure**: Uses current Statistics system
3. **Minimal Backend Changes**: Only 2 gems + extends existing patterns
4. **High Performance**: Redis caching + optimized database queries
5. **Arabic RTL Ready**: Built-in internationalization support
6. **Permission-Based**: Integrates with existing CanCan authorization
7. **Maintainable**: Follows established service layer patterns

## Architecture Overview

```
Frontend (Recharts)
    ↓ (Zero transformation)
Analytics Controller
    ↓ (Uses existing auth/permissions)
Chart Data Transformer
    ↓ (Leverages groupdate gem)
Existing Models (Attendance::Period, Leave)
    ↓ (Optimized with new indexes)
Database
```

## Next Steps

1. **Add gems**: `groupdate` and `redis`
2. **Extend statistics**: Add new metric calculators
3. **Create chart service**: Data transformation layer
4. **Build analytics controller**: Recharts-ready endpoints
5. **Add database indexes**: Performance optimization
6. **Create frontend components**: Direct Recharts integration
7. **Add translations**: Arabic RTL support
8. **Test with real data**: Verify performance and accuracy

This implementation provides a **seamless, maintainable, and performant** analytics dashboard that perfectly matches your Arabic design requirements while leveraging your existing infrastructure.
