# Timesheet Report API Documentation

## Overview

The Timesheet Report API provides functionality to generate monthly attendance reports for employees in both PDF and HTML formats. The reports show daily attendance data, leave days, weekend highlighting, and summary statistics.

## Endpoint

```
GET /api/attendance/reports/timesheet
```

## Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `employee_id` | Integer | Yes | ID of the employee for whom to generate the report |
| `year` | Integer | Yes | Year for the report (2020-2030) |
| `month` | Integer | Yes | Month for the report (1-12) |
| `format` | String | No | Output format: 'pdf' (default) or 'html' |

## Authorization

- **HR Users**: Can generate reports for any employee
- **Admin Users**: Can generate reports for any employee  
- **Manager Users**: Can generate reports for team members (if implemented)
- **Regular Employees**: Can only generate their own reports

## Response Formats

### PDF Format (Default)

**Request:**
```
GET /api/attendance/reports/timesheet?employee_id=123&year=2024&month=6
```

**Response:**
- Content-Type: `application/pdf`
- Content-Disposition: `attachment; filename="timesheet_john-doe_2024-06.pdf"`
- Body: PDF binary content

### HTML Format

**Request:**
```
GET /api/attendance/reports/timesheet?employee_id=123&year=2024&month=6&format=html
```

**Response:**
- Content-Type: `text/html`
- Body: HTML content for browser preview

## Report Structure

The generated timesheet includes:

### Header Section
- Title: "Time sheet"
- Period: Month and Year (e.g., "June 2024")

### Employee Information
- Name: Employee full name
- Position: Job title/position
- ID: Employee ID (formatted as 6-digit number)

### Attendance Table
- **Columns**: No., Day, Date, Arrival time, Leave time, Signature
- **Rows**: One row for each day of the month
- **Highlighting**:
  - Yellow background for weekends
  - Light red background for leave days
  - Gray background for absent days
  - White background for present days

### Summary Section
- Number of actual working days
- Number of leave or vacation days

### Signatures Section
- Space for supervisor signatures

## Data Sources

The report gathers data from:
- **Attendance Events**: Check-in/check-out times
- **Leave Records**: Approved and pending leaves
- **Weekend Configuration**: Configurable weekend days (default: Friday-Saturday)
- **Employee Profile**: Name, position, ID

## Error Responses

### 400 Bad Request
```json
{
  "errors": [
    {
      "detail": "Invalid parameters: Invalid year, Invalid month"
    }
  ]
}
```

**Common causes:**
- Missing required parameters
- Invalid year (outside 2020-2030 range)
- Invalid month (outside 1-12 range)
- Future date requested

### 403 Forbidden
```json
{
  "errors": [
    {
      "detail": "You are not authorized to access this employee's reports"
    }
  ]
}
```

### 404 Not Found
```json
{
  "errors": [
    {
      "detail": "Employee not found"
    }
  ]
}
```

### 422 Unprocessable Entity
```json
{
  "errors": [
    {
      "detail": "Failed to generate timesheet: HTML generation failed"
    }
  ]
}
```

**Common causes:**
- PDF generation failure
- HTML template rendering issues
- Data processing errors

### 500 Internal Server Error
```json
{
  "errors": [
    {
      "detail": "An unexpected error occurred while generating the timesheet"
    }
  ]
}
```

## Usage Examples

### Generate PDF Report
```bash
curl -X GET \
  "https://api.example.com/api/attendance/reports/timesheet?employee_id=123&year=2024&month=6" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -o timesheet.pdf
```

### Preview HTML Report
```bash
curl -X GET \
  "https://api.example.com/api/attendance/reports/timesheet?employee_id=123&year=2024&month=6&format=html" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### JavaScript Example
```javascript
// Generate PDF report
const response = await fetch('/api/attendance/reports/timesheet', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer ' + token
  },
  params: new URLSearchParams({
    employee_id: 123,
    year: 2024,
    month: 6
  })
});

if (response.ok) {
  const blob = await response.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'timesheet.pdf';
  a.click();
}
```

## Business Rules

### Working Day Calculation
- Excludes weekends (configurable, default: Friday-Saturday)
- Excludes approved leave days
- Counts days with any attendance events

### Leave Day Detection
- Includes approved leaves
- Includes pending leaves (configurable)
- Supports full-day and partial-day leaves

### Time Display
- Arrival time: First check-in of the day (or first event if no check-ins)
- Departure time: Last check-out of the day (or last event if no check-outs)
- Format: HH:MM (24-hour format)

### Weekend Configuration
- Configurable via `Setting.attendance_weekend_days`
- Default: [5, 6] (Friday=5, Saturday=6)
- Uses Ruby's Date.wday (0=Sunday, 1=Monday, etc.)

## Performance Considerations

- Reports are generated on-demand (not cached)
- Typical generation time: 2-5 seconds
- Database queries are optimized for monthly data
- PDF generation uses external Chrome service

## Security

- All requests require authentication
- Authorization checks prevent unauthorized access
- Employee data is protected by role-based access
- No sensitive data is logged

## Troubleshooting

### Common Issues

1. **PDF Generation Fails**
   - Check Chrome service availability
   - Verify Grover configuration
   - Check system memory

2. **HTML Template Errors**
   - Verify template files exist
   - Check for syntax errors in ERB templates
   - Ensure helper methods are available

3. **Authorization Errors**
   - Verify user roles and permissions
   - Check employee-user associations
   - Confirm current_user context

4. **Data Issues**
   - Verify attendance events exist for the period
   - Check leave records and statuses
   - Confirm weekend configuration

### Debug Information

Enable debug logging to troubleshoot issues:
```ruby
Rails.logger.level = :debug
```

Check logs for:
- Service initialization
- Data gathering queries
- Template rendering
- PDF generation steps
- Authorization checks

## Rate Limiting

- No specific rate limits implemented
- Consider implementing if needed for high-traffic scenarios
- Monitor server resources during bulk report generation

## Future Enhancements

Planned features:
- Bulk report generation for multiple employees
- Email delivery of reports
- Custom date ranges (not just monthly)
- Additional export formats (Excel, CSV)
- Report caching and optimization
- Advanced filtering options
