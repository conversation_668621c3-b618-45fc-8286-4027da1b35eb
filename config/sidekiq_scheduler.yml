#attendance_resolution:
#  cron: "0 0 * * *"  # Run at midnight every day
#  class: ResolutionWorker
#  queue: attendance
#  description: "Resolves undetermined attendance events from the previous day"
#  enabled: true

#attendance_sync:
#  cron: "*/30 7-19 * * 1-5"  # Run every 30 minutes from 7am to 7pm on weekdays
#  class: SyncWorker
#  queue: attendance
#  description: "Syncs attendance data from ZKTeco devices"
#  enabled: true

# Daily attendance period recalculation for yesterday
attendance_period_daily_recalculation:
  cron: "0 1 * * *"  # Run at 1am every day
  class: Attendance::BatchPeriodCalculationWorker
  queue: attendance
  description: "Recalculates attendance periods for all employees for yesterday"
  enabled: true

# Monthly salary calculation job
monthly_salary_calculation:
  cron: "0 2 1 * *"  # Run at 2am on the 1st day of every month
  class: MonthlySalaryCalculationJob
  queue: salary
  description: "Calculates salaries for all active employees for the current month"
  enabled: true

# Monthly cleanup of stale draft packages
cleanup_stale_draft_packages:
  cron: "0 3 1 * *"  # Run at 3am on the 1st day of every month
  class: CleanupStaleDraftPackagesJob
  queue: low_priority
  description: "Cleanup orphaned and inactive employee draft packages older than 30 days"
  enabled: true
