# frozen_string_literal: true

Rails.application.config.to_prepare do
  require Rails.root.join('app/models/types/avatar_attributes_type')
  require Rails.root.join('app/models/statistics/types/metric_card_type')

  # ✅ NEW: Use auth gem models instead of local models
  require 'athar_auth/models/types/user_role_collection_type'
  require 'athar_auth/models/types/project_type'
  require 'athar_auth/models/types/role_type'

  # Register the types with auth gem classes
  ActiveModel::Type.register(:avatar_attributes_type, Types::AvatarAttributesType)
  ActiveModel::Type.register(:user_role_collection, AtharAuth::Models::Types::UserRoleCollectionType)
  ActiveModel::Type.register(:project, AtharAuth::Models::Types::ProjectType)
  ActiveModel::Type.register(:role, AtharAuth::Models::Types::RoleType)
  ActiveModel::Type.register(:metric_card, Statistics::Types::MetricCardType)

  ActiveRecord::Type.register(:avatar_attributes_type, Types::AvatarAttributesType)
  ActiveRecord::Type.register(:user_role_collection, AtharAuth::Models::Types::UserRoleCollectionType)
  ActiveRecord::Type.register(:project, AtharAuth::Models::Types::ProjectType)
  ActiveRecord::Type.register(:role, AtharAuth::Models::Types::RoleType)
  ActiveRecord::Type.register(:metric_card, Statistics::Types::MetricCardType)
end
