# frozen_string_literal: true

AtharAuth.configure do |config|
  # If you stored the key at the top level in credentials:
  config.secret_key = Rails.application.credentials.devise_jwt_secret_key!

  # Or if you have environment-specific credentials:
  # config.secret_key = Rails.application.credentials.dig!(Rails.env.to_sym, :devise_jwt_secret_key)

  # Optionally, you can use a different algorithm if needed
  config.algorithm = "HS256"

  # Custom class to decode the user from the token
  config.user_class = "User"
end
