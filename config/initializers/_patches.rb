# Skip patches for certain commands
return if ARGV.any? { |command| command.include?('assets:') || command.include?('db:') }

require_all 'lib/core_extensions'

def apply_active_storage_patches
  return unless defined?(ActiveStorage)

  begin
    # Only apply patches if ActiveStorage is properly configured
    return unless Rails.application.config.active_storage.service.present?

    # Test if ActiveStorage service can be initialized
    ActiveStorage::Blob.service

    ActiveStorage::Blob.include(CoreExtensions::ActiveStorage::Blob::CdnDecorator)
    ActiveStorage::Attachment.include(CoreExtensions::ActiveStorage::Blob::CdnDecorator)
    ActiveStorage::Attached::One.include(CoreExtensions::ActiveStorage::Blob::CdnDecorator)
    ActiveStorage::Attached::Many.include(CoreExtensions::ActiveStorage::Blob::CdnDecorator)
  rescue => e
    # This is normal during Ruby LSP initialization when storage services are not available
    Rails.logger&.debug("Skipping ActiveStorage patches: #{e.message}")
  end
end

Rails.application.configure do
  # This runs once after all initializers have run
  config.after_initialize do
    apply_active_storage_patches
  end

  # Use to_prepare to reapply patches in development mode after code reloading
  # In production, this only runs once during initialization
  config.to_prepare do
    apply_active_storage_patches
  end
end
