# frozen_string_literal: true

# Grover configuration for HTML to PDF conversion
Grover.configure do |config|
  # PDF options
  config.options = {
    format: 'A4',
    margin: {
      top: '0.5in',
      bottom: '0.5in',
      left: '0.5in',
      right: '0.5in'
    },
    print_background: true,
    prefer_css_page_size: true,
    display_header_footer: false,

    # Performance and reliability options
    timeout: 30_000, # 30 seconds
    wait_until: 'networkidle0',

    # Use external Chrome service instead of launching local Chrome
    browser_ws_endpoint: ENV['CHROME_URL']&.gsub('http://', 'ws://') || 'ws://people-chrome:3000'
  }
end
