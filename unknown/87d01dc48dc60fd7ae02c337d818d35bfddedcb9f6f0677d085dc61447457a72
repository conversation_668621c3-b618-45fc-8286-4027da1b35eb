class Attendance::SyncWorker
  include Sidekiq::Worker

  sidekiq_options queue: :attendance, retry: 3

  def perform(start_date = nil, end_date = nil, options = {})
    # Convert string dates to Date objects if provided
    start_date = Date.parse(start_date) if start_date.is_a?(String)
    end_date = Date.parse(end_date) if end_date.is_a?(String)

    # Default to yesterday if no dates provided
    start_date ||= Date.yesterday
    end_date ||= Date.today

    # Initialize the integration service with options
    adapter_type = options['adapter_type'] || :zkteco
    adapter_options = options['adapter_options'] || {}

    integration = ::Attendance::IntegrationService.new(adapter_type, adapter_options)

    # Sync attendance data
    results = integration.sync(start_date, end_date)

    # Log results
    Rails.logger.info("Attendance sync completed: #{results[:success]} records imported, #{results[:failure]} failed")

    # Return results
    results
  end
end
