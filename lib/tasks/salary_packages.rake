namespace :salary_packages do
  desc "Clean up stale draft packages older than specified days (default: 30)"
  task :cleanup_stale_drafts, [ :days ] => :environment do |task, args|
    days = args[:days]&.to_i || 30

    puts "Cleaning up draft packages older than #{days} days..."
    result = CleanupStaleDraftPackagesJob.perform_now(days)

    puts "Cleanup completed:"
    puts "  - Deleted: #{result[:deleted]} packages"
    puts "  - Errors: #{result[:errors].count} packages"

    if result[:errors].any?
      puts "\nErrors:"
      result[:errors].each do |error|
        puts "  - Package #{error[:package_id]} (Employee #{error[:employee_id]}): #{error[:error]}"
      end
    end
  end

  desc "Show draft package statistics by creator"
  task draft_stats: :environment do
    stats = SalaryPackage.draft
                        .group(:created_by_id)
                        .group(:employee_id)
                        .count

    puts "Draft Package Statistics:"
    puts "Creator ID | Employee ID | Count"
    puts "-" * 35

    stats.each do |(creator_id, employee_id), count|
      puts "#{creator_id.to_s.ljust(10)} | #{employee_id.to_s.ljust(11)} | #{count}"
    end

    total_drafts = SalaryPackage.draft.count
    puts "-" * 35
    puts "Total draft packages: #{total_drafts}"
  end

  desc "Show detailed draft package information"
  task draft_details: :environment do
    drafts = SalaryPackage.draft
                         .includes(:employee, :created_by)
                         .order(:created_at)

    puts "Detailed Draft Package Information:"
    puts "ID | Employee | Creator | Created | Age (days)"
    puts "-" * 60

    drafts.each do |package|
      age_days = (Date.current - package.created_at.to_date).to_i
      employee_name = package.employee&.name || "Unknown"
      creator_name = package.created_by&.name || "Unknown"

      puts "#{package.id.to_s.ljust(3)} | #{employee_name.ljust(15)} | #{creator_name.ljust(15)} | #{package.created_at.strftime('%Y-%m-%d')} | #{age_days}"
    end

    puts "-" * 60
    puts "Total: #{drafts.count} draft packages"
  end

  desc "Show cancelled package statistics"
  task cancelled_stats: :environment do
    auto_cancelled = SalaryPackage.auto_cancelled.count
    manually_cancelled = SalaryPackage.manually_cancelled.count
    total_cancelled = SalaryPackage.cancelled.count

    puts "Cancelled Package Statistics:"
    puts "  - Auto-cancelled (superseded): #{auto_cancelled}"
    puts "  - Manually cancelled: #{manually_cancelled}"
    puts "  - Total cancelled: #{total_cancelled}"

    # Recent cancellations (last 30 days)
    recent_cancelled = SalaryPackage.cancelled
                                   .where("cancelled_at > ?", 30.days.ago)
                                   .count

    puts "  - Cancelled in last 30 days: #{recent_cancelled}"
  end

  desc "Monitor draft accumulation patterns"
  task monitor_accumulation: :environment do
    puts "Draft Accumulation Monitoring:"
    puts "=" * 40

    # Drafts by age
    age_ranges = [
      [ 0, 7, "0-7 days" ],
      [ 8, 14, "8-14 days" ],
      [ 15, 30, "15-30 days" ],
      [ 31, 60, "31-60 days" ],
      [ 61, Float::INFINITY, "60+ days" ]
    ]

    age_ranges.each do |min_days, max_days, label|
      if max_days == Float::INFINITY
        count = SalaryPackage.draft.where("created_at < ?", min_days.days.ago).count
      else
        count = SalaryPackage.draft.where(
          created_at: max_days.days.ago..min_days.days.ago
        ).count
      end
      puts "#{label.ljust(15)}: #{count} packages"
    end

    puts "\nDrafts by Creator:"
    puts "-" * 25
    creator_stats = SalaryPackage.draft
                                .includes(:created_by)
                                .group_by(&:created_by)
                                .transform_values(&:count)
                                .sort_by { |_, count| -count }

    creator_stats.first(10).each do |creator, count|
      creator_name = creator&.name || "Unknown"
      puts "#{creator_name.ljust(20)}: #{count}"
    end

    # Alert for potential issues
    old_drafts = SalaryPackage.draft.where("created_at < ?", 60.days.ago).count
    if old_drafts > 0
      puts "\n⚠️  WARNING: #{old_drafts} drafts are older than 60 days"
    end

    orphaned_drafts = SalaryPackage.draft.where(created_by_id: nil).count
    if orphaned_drafts > 0
      puts "\n⚠️  WARNING: #{orphaned_drafts} orphaned drafts (no creator)"
    end
  end
end
