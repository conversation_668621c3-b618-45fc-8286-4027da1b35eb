#!/usr/bin/env ruby


# Find the salary calculation
salary_calculation = SalaryCalculation.find_by(id: 3)

if salary_calculation.nil?
  SalaryCalculation.limit(10).each do |calc|
  end
  exit 1
end


# Check if salary calculation is in paid status
if !salary_calculation.paid?
  salary_calculation.update!(status: :paid)
end

# Check if PDF already exists
if salary_calculation.salary_slip_pdf.attached?
  salary_calculation.salary_slip_pdf.purge
end


begin
  slip_service = Salary::SlipService.new(salary_calculation)
  result = slip_service.generate

  if result

    if salary_calculation.salary_slip_pdf.attached?

      # Try to get the URL if possible
      begin
        url = Rails.application.routes.url_helpers.rails_blob_url(salary_calculation.salary_slip_pdf, host: 'localhost:1235')
      rescue => e
      end
    else
    end
  else
  end

rescue => e
end
