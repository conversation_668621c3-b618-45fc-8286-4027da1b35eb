# frozen_string_literal: true

require 'rails_helper'

RSpec.describe TimesheetReportHelper, type: :helper do
  let(:sample_data) do
    {
      employee: {
        name: '<PERSON>',
        position: 'Software Engineer',
        id: '000123',
        department: 'IT'
      },
      period: {
        month_name: 'June',
        year: 2024,
        month_year: 'June 2024',
        month_number: 6
      },
      days: [
        {
          date: Date.new(2024, 6, 1),
          day_number: 1,
          day_name: 'Sat',
          formatted_date: '1-Jun-24',
          arrival_time: nil,
          departure_time: nil,
          is_weekend: true,
          is_leave: false,
          is_present: false,
          status: 'weekend',
          events_count: 0
        },
        {
          date: Date.new(2024, 6, 3),
          day_number: 3,
          day_name: '<PERSON>',
          formatted_date: '3-Jun-24',
          arrival_time: '08:30',
          departure_time: '17:00',
          is_weekend: false,
          is_leave: false,
          is_present: true,
          status: 'present',
          events_count: 2
        },
        {
          date: Date.new(2024, 6, 5),
          day_number: 5,
          day_name: 'Wed',
          formatted_date: '5-Jun-24',
          arrival_time: nil,
          departure_time: nil,
          is_weekend: false,
          is_leave: true,
          is_present: false,
          status: 'leave',
          events_count: 0
        }
      ],
      summary: {
        total_days: 30,
        working_days: 20,
        leave_days: 2,
        weekend_days: 8,
        absent_days: 0
      },
      generated_at: Time.current
    }
  end

  describe '#render_timesheet_content' do
    it 'renders complete timesheet content' do
      content = helper.render_timesheet_content(sample_data)
      
      expect(content).to include('Time sheet')
      expect(content).to include('John Doe')
      expect(content).to include('June 2024')
      expect(content).to include('attendance-table')
      expect(content).to include('summary-section')
      expect(content).to include('signatures-section')
    end
  end

  describe '#render_timesheet_header' do
    it 'renders header with title and period' do
      header = helper.render_timesheet_header(sample_data)
      
      expect(header).to include('<h1>Time sheet</h1>')
      expect(header).to include('June 2024')
      expect(header).to include('timesheet-header')
    end
  end

  describe '#render_employee_info' do
    it 'renders employee information' do
      info = helper.render_employee_info(sample_data)
      
      expect(info).to include('Name:')
      expect(info).to include('John Doe')
      expect(info).to include('Position:')
      expect(info).to include('Software Engineer')
      expect(info).to include('ID:')
      expect(info).to include('000123')
    end
  end

  describe '#render_attendance_table' do
    it 'renders table with headers' do
      table = helper.render_attendance_table(sample_data)
      
      expect(table).to include('<table class="attendance-table">')
      expect(table).to include('<th>No.</th>')
      expect(table).to include('<th>Day</th>')
      expect(table).to include('<th>Date</th>')
      expect(table).to include('<th>Arrival time</th>')
      expect(table).to include('<th>Leave time</th>')
      expect(table).to include('<th>Signature</th>')
    end

    it 'renders attendance rows' do
      table = helper.render_attendance_table(sample_data)
      
      expect(table).to include('1-Jun-24')
      expect(table).to include('3-Jun-24')
      expect(table).to include('5-Jun-24')
      expect(table).to include('08:30')
      expect(table).to include('17:00')
    end
  end

  describe '#render_attendance_rows' do
    it 'renders all day rows' do
      rows = helper.render_attendance_rows(sample_data[:days])
      
      expect(rows).to include('weekend-row')
      expect(rows).to include('present-row')
      expect(rows).to include('leave-row')
    end

    it 'includes day numbers and names' do
      rows = helper.render_attendance_rows(sample_data[:days])
      
      expect(rows).to include('<td class="day-number">1</td>')
      expect(rows).to include('<td class="day-name">Sat</td>')
      expect(rows).to include('<td class="day-number">3</td>')
      expect(rows).to include('<td class="day-name">Mon</td>')
    end

    it 'includes attendance times when present' do
      rows = helper.render_attendance_rows(sample_data[:days])
      
      expect(rows).to include('<td class="arrival-time">08:30</td>')
      expect(rows).to include('<td class="departure-time">17:00</td>')
    end

    it 'shows empty cells for non-working days' do
      rows = helper.render_attendance_rows(sample_data[:days])
      
      # Weekend and leave days should have empty time cells
      weekend_row = rows.match(/<tr class="attendance-row weekend-row">.*?<\/tr>/m)[0]
      expect(weekend_row).to include('<td class="arrival-time"></td>')
      expect(weekend_row).to include('<td class="departure-time"></td>')
    end
  end

  describe '#get_row_class' do
    it 'returns correct class for weekend' do
      weekend_day = sample_data[:days][0] # Saturday
      classes = helper.get_row_class(weekend_day)
      expect(classes).to include('attendance-row')
      expect(classes).to include('weekend-row')
    end

    it 'returns correct class for present day' do
      present_day = sample_data[:days][1] # Monday with attendance
      classes = helper.get_row_class(present_day)
      expect(classes).to include('attendance-row')
      expect(classes).to include('present-row')
    end

    it 'returns correct class for leave day' do
      leave_day = sample_data[:days][2] # Wednesday on leave
      classes = helper.get_row_class(leave_day)
      expect(classes).to include('attendance-row')
      expect(classes).to include('leave-row')
    end

    it 'returns correct class for absent day' do
      absent_day = {
        status: 'absent',
        is_weekend: false,
        is_leave: false,
        is_present: false
      }
      classes = helper.get_row_class(absent_day)
      expect(classes).to include('attendance-row')
      expect(classes).to include('absent-row')
    end
  end

  describe '#render_summary_section' do
    it 'renders summary with working days count' do
      summary = helper.render_summary_section(sample_data)
      
      expect(summary).to include('N. of actual working days:')
      expect(summary).to include('<span class="summary-value">20</span>')
    end

    it 'renders summary with leave days count' do
      summary = helper.render_summary_section(sample_data)
      
      expect(summary).to include('N. of Leave or vacation:')
      expect(summary).to include('<span class="summary-value">2</span>')
    end
  end

  describe '#render_signatures_section' do
    it 'renders signatures section' do
      signatures = helper.render_signatures_section
      
      expect(signatures).to include('Supervisor signatures')
      expect(signatures).to include('signature-line')
      expect(signatures).to include('signatures-section')
    end
  end

  describe '#render_timesheet_styles' do
    it 'renders CSS styles' do
      styles = helper.render_timesheet_styles
      
      expect(styles).to include('.timesheet-container')
      expect(styles).to include('.attendance-table')
      expect(styles).to include('.weekend-row')
      expect(styles).to include('.leave-row')
      expect(styles).to include('@media print')
    end

    it 'includes print-specific styles' do
      styles = helper.render_timesheet_styles
      
      expect(styles).to include('@media print')
      expect(styles).to include('@page')
      expect(styles).to include('page-break-inside: avoid')
    end
  end

  describe '#format_display_time' do
    it 'returns time string as-is when present' do
      result = helper.format_display_time('08:30')
      expect(result).to eq('08:30')
    end

    it 'returns empty string when nil' do
      result = helper.format_display_time(nil)
      expect(result).to eq('')
    end

    it 'returns empty string when blank' do
      result = helper.format_display_time('')
      expect(result).to eq('')
    end
  end

  describe '#format_display_date' do
    it 'formats date correctly' do
      date = Date.new(2024, 6, 15)
      result = helper.format_display_date(date)
      expect(result).to eq('15-Jun-24')
    end

    it 'returns empty string when nil' do
      result = helper.format_display_date(nil)
      expect(result).to eq('')
    end
  end

  describe '#get_status_label' do
    it 'returns correct label for present' do
      expect(helper.get_status_label('present')).to eq('Present')
    end

    it 'returns correct label for absent' do
      expect(helper.get_status_label('absent')).to eq('Absent')
    end

    it 'returns correct label for leave' do
      expect(helper.get_status_label('leave')).to eq('Leave')
    end

    it 'returns correct label for weekend' do
      expect(helper.get_status_label('weekend')).to eq('Weekend')
    end

    it 'humanizes unknown status' do
      expect(helper.get_status_label('custom_status')).to eq('Custom status')
    end
  end
end
