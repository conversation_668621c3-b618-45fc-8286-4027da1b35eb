require 'rails_helper'

RSpec.describe Attendance::PeriodService, type: :service do
  let(:employee) { create(:employee) }
  let(:test_date) { Date.current }

  before do
    # Clear any existing events and periods
    Attendance::Event.where(employee: employee).for_date(test_date).destroy_all
    Attendance::Period.where(employee: employee, date: test_date).destroy_all

    # Create attendance settings using factory
    create(:attendance_settings)
  end

  describe "#calculate_periods" do
    it "calculates periods from attendance events" do
      # Create complete day scenario using factory
      create(:attendance_scenario, :complete_day, employee: employee, date: test_date)

      # Calculate periods
      service = Attendance::PeriodService.new(employee, test_date)
      periods = service.calculate_periods

      # Verify periods were created
      expect(periods).not_to be_empty

      # Find periods by type
      work_periods = periods.select { |p| p.period_type == Attendance::Period::PERIOD_TYPES[:work] }
      break_period = periods.find { |p| p.period_type == Attendance::Period::PERIOD_TYPES[:break] }

      # V2 System: Late periods are handled by the deduction calculator during salary processing
      # The period service focuses on work and break periods

      # Verify work periods
      expect(work_periods.length).to be >= 1
      work_periods.each do |period|
        expect(period.duration_minutes).to be > 0
      end

      # Verify break period exists (actual duration depends on business logic)
      expect(break_period).not_to be_nil
      expect(break_period.duration_minutes).to be > 0
    end

    it "handles missing check-in events" do
      # Create scenario without initial check-in using factory
      create(:attendance_scenario, :missing_check_in, employee: employee, date: test_date)

      # Calculate periods
      service = Attendance::PeriodService.new(employee, test_date)
      periods = service.calculate_periods

      # Find periods by type
      work_periods = periods.select { |p| p.period_type == Attendance::Period::PERIOD_TYPES[:work] }
      break_period = periods.find { |p| p.period_type == Attendance::Period::PERIOD_TYPES[:break] }

      # Should still create periods even without initial check-in
      expect(work_periods).not_to be_empty
      expect(break_period).not_to be_nil
    end

    it "handles missing check-out events" do
      # Create scenario without final check-out using factory
      create(:attendance_scenario, :missing_check_out, employee: employee, date: test_date)

      # Calculate periods
      service = Attendance::PeriodService.new(employee, test_date)
      periods = service.calculate_periods

      # Find periods by type
      work_periods = periods.select { |p| p.period_type == Attendance::Period::PERIOD_TYPES[:work] }
      early_departure = periods.find { |p| p.period_type == Attendance::Period::PERIOD_TYPES[:early_departure] }

      # V2 System: Focus on work periods and early departure detection
      # Late periods are handled by the deduction calculator
      expect(work_periods).not_to be_empty

      # Verify work periods have valid durations
      work_periods.each do |period|
        expect(period.duration_minutes).to be > 0
      end

      # Should detect early departure if last event is before work end time
      # This depends on the specific logic in the service
    end

    it "handles duplicate events" do
      # Create scenario with duplicate events using factory
      create(:attendance_scenario, :with_duplicates, employee: employee, date: test_date)

      # Verify events were created
      events = Attendance::Event.where(employee: employee)
      expect(events.count).to be >= 2

      # Calculate periods
      service = Attendance::PeriodService.new(employee, test_date)
      periods = service.calculate_periods

      # The service should handle duplicates gracefully, even if it doesn't flag them
      # For now, let's just verify that periods are calculated correctly
      expect(periods).not_to be_empty

      # Note: Duplicate detection might be handled elsewhere in the system
      # This test verifies the service doesn't crash with duplicate events
    end
  end
end
