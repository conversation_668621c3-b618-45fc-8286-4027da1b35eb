require 'rails_helper'

RSpec.describe Attendance::PeriodService, type: :service do
  let(:employee) { create(:employee) }
  let(:date) { Date.current }
  let(:service) { described_class.new(employee, date) }

  before do
    # Mock all the AttendanceSettings methods used by PeriodService
    Setting.stubs(:attendance_daily_work_threshold).returns(5.0)
    Setting.stubs(:attendance_weekend_days).returns([ 5, 6 ])
    Setting.stubs(:attendance_work_start_time).returns('09:00')
    Setting.stubs(:attendance_work_end_time).returns('17:00')
    Setting.stubs(:attendance_work_start_timestamp).returns(date.to_time.change(hour: 9).to_i)
    Setting.stubs(:attendance_work_end_timestamp).returns(date.to_time.change(hour: 17).to_i)
    Setting.stubs(:attendance_duplicate_threshold_seconds).returns(60)
    Setting.stubs(:attendance_daily_expected_hours).returns(8.0)
    Setting.stubs(:attendance_break_threshold_minutes).returns(120)
    Setting.stubs(:attendance_exclude_weekends?).returns(true)
    Setting.stubs(:attendance_exclude_holidays?).returns(true)
    Setting.stubs(:attendance_deductions_enabled?).returns(true)
  end

  describe 'new leave detection functionality' do
    describe '#employee_on_approved_leave?' do
      it 'returns true when employee has approved leave for date' do
        create(:leave,
               employee: employee,
               start_date: date,
               end_date: date,
               status: 'approved')

        expect(service.send(:employee_on_approved_leave?)).to be true
      end

      it 'returns false when no approved leave exists' do
        expect(service.send(:employee_on_approved_leave?)).to be false
      end

      it 'returns false for pending leave' do
        create(:leave,
               employee: employee,
               start_date: date,
               end_date: date,
               status: 'pending')

        expect(service.send(:employee_on_approved_leave?)).to be false
      end

      it 'returns false for rejected leave' do
        create(:leave,
               employee: employee,
               start_date: date,
               end_date: date,
               status: 'rejected')

        expect(service.send(:employee_on_approved_leave?)).to be false
      end
    end

    # V2 System: Auto-leave creation functionality has been completely removed
    # The V2 system handles attendance deductions during salary processing
    # instead of creating auto-generated leaves

    describe '#weekend_day?' do
      it 'returns true for Friday (day 5)' do
        friday = Date.current.beginning_of_week + 4.days
        service_friday = described_class.new(employee, friday)
        expect(service_friday.send(:weekend_day?)).to be true
      end

      it 'returns true for Saturday (day 6)' do
        saturday = Date.current.beginning_of_week + 5.days
        service_saturday = described_class.new(employee, saturday)
        expect(service_saturday.send(:weekend_day?)).to be true
      end

      it 'returns false for Monday (day 1)' do
        monday = Date.current.beginning_of_week
        service_monday = described_class.new(employee, monday)
        expect(service_monday.send(:weekend_day?)).to be false
      end

      it 'returns false for Wednesday (day 3)' do
        wednesday = Date.current.beginning_of_week + 2.days
        service_wednesday = described_class.new(employee, wednesday)
        expect(service_wednesday.send(:weekend_day?)).to be false
      end
    end

    describe '#calculate_daily_work_hours' do
      let!(:work_period1) { create(:attendance_period, employee: employee, date: date, period_type: 'work', duration_minutes: 240) }
      let!(:work_period2) { create(:attendance_period, employee: employee, date: date, period_type: 'work', duration_minutes: 120) }
      let!(:break_period) { create(:attendance_period, employee: employee, date: date, period_type: 'break', duration_minutes: 60) }
      let!(:other_employee_period) { create(:attendance_period, date: date, period_type: 'work', duration_minutes: 480) }

      it 'calculates total work hours correctly' do
        total_hours = service.send(:calculate_daily_work_hours)
        expect(total_hours).to eq(6.0) # (240 + 120) / 60 = 6 hours
      end

      it 'excludes non-work periods' do
        total_hours = service.send(:calculate_daily_work_hours)
        expect(total_hours).to eq(6.0) # Break period should not be included, only work periods counted
      end

      it 'excludes other employees periods' do
        total_hours = service.send(:calculate_daily_work_hours)
        expect(total_hours).to eq(6.0) # Should not include other employee's 8 hours
      end

      it 'returns zero when no work periods exist' do
        Attendance::Period.where(employee: employee, period_type: 'work').destroy_all
        total_hours = service.send(:calculate_daily_work_hours)
        expect(total_hours).to eq(0.0)
      end
    end

    # V1 methods removed in V2 system
    # These methods are no longer available in the service
    # describe '#create_leave_period' do
    # end

    # describe '#accumulate_missing_hours' do
    # end
  end

  describe 'integration with calculate_periods' do
    context 'when employee has approved leave' do
      before do
        create(:leave,
               employee: employee,
               start_date: date,
               end_date: date,
               status: 'approved')
      end

      it 'skips auto-leave creation when employee has approved leave (V2 behavior)' do
        # In V2, we don't create any periods automatically
        expect {
          service.calculate_periods
        }.not_to change(Attendance::Period, :count)

        # The service should detect the approved leave and skip auto-leave creation
        expect(service.send(:employee_on_approved_leave?)).to be true
      end
    end

    context 'when no events exist (V2 behavior)' do
      before do
        Attendance::Event.stubs(:daily_events).returns(Attendance::Event.none)
      end

      it 'does not create auto-generated leave (V2 behavior)' do
        expect {
          service.calculate_periods
        }.not_to change(Attendance::Period, :count)

        # V2 system handles deductions during salary processing instead
      end
    end
  end
end
