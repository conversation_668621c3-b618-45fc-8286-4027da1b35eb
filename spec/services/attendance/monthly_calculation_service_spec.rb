require 'rails_helper'

RSpec.describe Attendance::MonthlyCalculationService, type: :service do
  let(:employee) { create(:employee) }
  let(:year) { 2024 }
  let(:month) { 7 }
  let(:service) { described_class.new(employee, year, month) }

  before do
    # Set up attendance settings
    Setting.stubs(:get).with('attendance', 'monthly_expected_hours', '180').returns('180')
    Setting.stubs(:get).with('attendance', 'daily_expected_hours', 8.0).returns(8.0)
    Setting.stubs(:get).with('attendance', 'weekend_days', '5,6').returns('5,6')
    Setting.stubs(:attendance_weekend_days).returns([ 5, 6 ])
  end

  describe '#initialize' do
    it 'sets employee, year, and month' do
      expect(service.employee).to eq(employee)
      expect(service.year).to eq(year)
      expect(service.month).to eq(month)
    end
  end

  describe '#calculate_monthly_record' do
    let!(:work_period1) do
      create(:attendance_period,
             employee: employee,
             date: Date.new(2024, 7, 1),
             period_type: 'work',
             duration_minutes: 480) # 8 hours
    end

    let!(:work_period2) do
      create(:attendance_period,
             employee: employee,
             date: Date.new(2024, 7, 2),
             period_type: 'work',
             duration_minutes: 360) # 6 hours
    end

    let!(:leave_period) do
      create(:attendance_period,
             employee: employee,
             date: Date.new(2024, 7, 3),
             period_type: 'leave',
             duration_minutes: 480,
             auto_generated: true)
    end

    it 'creates monthly record with correct calculations' do
      record = service.calculate_monthly_record

      expect(record).to be_persisted
      expect(record.employee).to eq(employee)
      expect(record.year).to eq(2024)
      expect(record.month).to eq(7)
      expect(record.expected_hours).to eq(184.0) # 23 working days * 8 hours
      expect(record.actual_hours).to eq(14.0) # 8 + 6 hours (work only)
      expect(record.deficit_hours).to eq(170.0) # 184 - 14
    end

    it 'updates existing record' do
      existing = create(:attendance_monthly_record,
                       employee: employee,
                       year: 2024,
                       month: 7,
                       actual_hours: 100.0)

      record = service.calculate_monthly_record

      expect(record.id).to eq(existing.id)
      expect(record.actual_hours).to eq(14.0) # Recalculated
    end
  end

  describe '#calculate_actual_work_hours' do
    let!(:work_period1) { create(:attendance_period, employee: employee, date: Date.new(2024, 7, 1), period_type: 'work', duration_minutes: 480) }
    let!(:work_period2) { create(:attendance_period, employee: employee, date: Date.new(2024, 7, 2), period_type: 'work', duration_minutes: 360) }
    let!(:break_period) { create(:attendance_period, employee: employee, date: Date.new(2024, 7, 1), period_type: 'break', duration_minutes: 60) }
    let!(:leave_period) { create(:attendance_period, employee: employee, date: Date.new(2024, 7, 3), period_type: 'leave', duration_minutes: 480) }
    let!(:other_employee_period) { create(:attendance_period, date: Date.new(2024, 7, 1), period_type: 'work', duration_minutes: 480) }

    it 'calculates only work hours for the employee in the month' do
      total_hours = service.send(:calculate_actual_work_hours)
      expect(total_hours).to eq(14.0) # 8 + 6 hours (work only)
    end

    it 'excludes non-work periods' do
      total_hours = service.send(:calculate_actual_work_hours)
      expect(total_hours).to eq(14.0) # 8 + 6 hours, break period excluded
      expect(total_hours).to eq(14.0) # 8 + 6 hours, leave period excluded
    end

    it 'excludes other employees' do
      total_hours = service.send(:calculate_actual_work_hours)
      expect(total_hours).to eq(14.0) # Other employee's 8 hours excluded
    end
  end

  describe '#calculate_working_days' do
    it 'calculates working days correctly for July 2024' do
      working_days = service.send(:calculate_working_days)

      # July 2024 has 31 days
      # Weekends are Friday(5) and Saturday(6) - 8 weekend days total
      # Should be 23 working days
      expect(working_days).to eq(23)
    end

    it 'excludes weekend days based on settings' do
      working_days = service.send(:calculate_working_days)

      # July 2024 has 31 days, with Fridays and Saturdays as weekends
      # Should be 23 working days (31 - 8 weekend days)
      expect(working_days).to eq(23)
    end
  end

  describe '#monthly_summary' do
    let!(:work_period) { create(:attendance_period, employee: employee, date: Date.new(2024, 7, 1), period_type: 'work', duration_minutes: 480) }
    let!(:auto_leave) { create(:attendance_period, employee: employee, date: Date.new(2024, 7, 2), period_type: 'leave', auto_generated: true) }
    let!(:manual_leave) { create(:attendance_period, employee: employee, date: Date.new(2024, 7, 3), period_type: 'leave', auto_generated: false) }

    it 'returns comprehensive monthly summary' do
      summary = service.monthly_summary

      expect(summary).to include(
        :monthly_record,
        :auto_generated_leave_days,
        :manual_leave_days,
        :total_leave_days,
        :working_days_in_month,
        :attendance_percentage,
        :deficit_percentage
      )

      expect(summary[:monthly_record]).to be_a(Attendance::MonthlyRecord)
      expect(summary[:auto_generated_leave_days]).to eq(1)
      expect(summary[:manual_leave_days]).to eq(1)
      expect(summary[:total_leave_days]).to eq(2)
      expect(summary[:working_days_in_month]).to be_positive
      expect(summary[:attendance_percentage]).to be_a(Numeric)
      expect(summary[:deficit_percentage]).to be_a(Numeric)
    end
  end

  describe 'counting methods' do
    let!(:auto_leave1) { create(:attendance_period, employee: employee, date: Date.new(2024, 7, 1), period_type: 'leave', auto_generated: true) }
    let!(:auto_leave2) { create(:attendance_period, employee: employee, date: Date.new(2024, 7, 2), period_type: 'leave', auto_generated: true) }
    let!(:manual_leave) { create(:attendance_period, employee: employee, date: Date.new(2024, 7, 3), period_type: 'leave', auto_generated: false) }
    let!(:work_period) { create(:attendance_period, employee: employee, date: Date.new(2024, 7, 4), period_type: 'work') }
    let!(:other_month_leave) { create(:attendance_period, employee: employee, date: Date.new(2024, 8, 1), period_type: 'leave', auto_generated: true) }

    describe '#count_auto_generated_leave_days' do
      it 'counts only auto-generated leave days in the month' do
        count = service.send(:count_auto_generated_leave_days)
        expect(count).to eq(2)
      end
    end

    describe '#count_manual_leave_days' do
      it 'counts only manual leave days in the month' do
        count = service.send(:count_manual_leave_days)
        expect(count).to eq(1)
      end
    end

    describe '#count_total_leave_days' do
      it 'counts all leave days in the month' do
        count = service.send(:count_total_leave_days)
        expect(count).to eq(3)
      end
    end
  end

  describe 'class methods' do
    describe '.calculate_for_employees' do
      let(:employee2) { create(:employee) }
      let(:employees) { [ employee, employee2 ] }

      it 'calculates for multiple employees' do
        expect {
          described_class.calculate_for_employees(employees, year, month)
        }.to change(Attendance::MonthlyRecord, :count).by(2)
      end
    end

    describe '.calculate_for_all_employees' do
      let!(:employee1) { create(:employee) }
      let!(:employee2) { create(:employee) }

      it 'calculates for all active employees' do
        expect {
          described_class.calculate_for_all_employees(year, month)
        }.to change(Attendance::MonthlyRecord, :count).by(2)
      end
    end

    describe '.recalculate_range' do
      it 'calculates for date range' do
        start_date = Date.new(2024, 6, 1)
        end_date = Date.new(2024, 8, 31)

        expect {
          described_class.recalculate_range(employee, start_date, end_date)
        }.to change(Attendance::MonthlyRecord, :count).by(3) # June, July, August
      end
    end
  end

  describe 'error handling' do
    it 'handles invalid month gracefully' do
      invalid_service = described_class.new(employee, year, 13)
      expect {
        invalid_service.calculate_monthly_record
      }.to raise_error(ArgumentError)
    end

    it 'handles invalid year gracefully' do
      invalid_service = described_class.new(employee, 1999, month)
      expect {
        invalid_service.calculate_monthly_record
      }.to raise_error(ActiveRecord::RecordInvalid)
    end
  end

  describe 'settings integration' do
    it 'uses monthly expected hours from settings' do
      Attendance::WorkingDaysCalculator.stubs(:monthly_expected_hours).with(2024, 7).returns(200.0)

      record = service.calculate_monthly_record
      expect(record.expected_hours).to eq(200.0)
    end

    it 'uses weekend days from settings' do
      Setting.stubs(:get).with('attendance', 'weekend_days', '5,6').returns('6,0') # Saturday, Sunday

      working_days = service.send(:calculate_working_days)
      expect(working_days).to be_positive
    end
  end
end
