# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Attendance::TimesheetReportService, type: :service do
  let(:employee) { create(:employee, name: '<PERSON>') }
  let(:year) { 2024 }
  let(:month) { 6 } # June
  let(:service) { described_class.new(employee, year, month) }

  before do
    # Mock weekend days (Friday=5, Saturday=6)
    Setting.stubs(:attendance_weekend_days).returns([5, 6])
  end

  describe '#initialize' do
    context 'with valid parameters' do
      it 'initializes successfully' do
        expect(service.employee).to eq(employee)
        expect(service.year).to eq(year)
        expect(service.month).to eq(month)
      end

      it 'sets correct date range' do
        expected_range = Date.new(2024, 6, 1)..Date.new(2024, 6, 30)
        expect(service.date_range).to eq(expected_range)
      end
    end

    context 'with invalid parameters' do
      it 'raises error for invalid year' do
        expect {
          described_class.new(employee, 2050, month)
        }.to raise_error(ArgumentError, /Invalid year/)
      end

      it 'raises error for invalid month' do
        expect {
          described_class.new(employee, year, 0)
        }.to raise_error(ArgumentError, /Invalid month/)
      end

      it 'raises error for future date' do
        future_year = Date.current.year + 1
        expect {
          described_class.new(employee, future_year, 1)
        }.to raise_error(ArgumentError, /Future date not allowed/)
      end

      it 'raises error for nil employee' do
        expect {
          described_class.new(nil, year, month)
        }.to raise_error(ArgumentError, /Employee not found/)
      end
    end
  end

  describe '#generate_data' do
    let(:june_1) { Date.new(2024, 6, 1) } # Saturday
    let(:june_3) { Date.new(2024, 6, 3) } # Monday

    before do
      # Create some test attendance events
      create(:attendance_event, 
        employee: employee, 
        timestamp: june_3.beginning_of_day.to_i + 8.hours.to_i,
        event_type: :check_in
      )
      create(:attendance_event, 
        employee: employee, 
        timestamp: june_3.beginning_of_day.to_i + 17.hours.to_i,
        event_type: :check_out
      )

      # Create a leave
      create(:leave, 
        employee: employee,
        start_date: Date.new(2024, 6, 5),
        end_date: Date.new(2024, 6, 5),
        status: :approved
      )
    end

    it 'generates correct data structure' do
      data = service.generate_data

      expect(data).to have_key(:employee)
      expect(data).to have_key(:period)
      expect(data).to have_key(:days)
      expect(data).to have_key(:summary)
      expect(data).to have_key(:generated_at)
    end

    it 'includes correct employee data' do
      data = service.generate_data
      employee_data = data[:employee]

      expect(employee_data[:name]).to eq('John Doe')
      expect(employee_data[:id]).to eq(employee.employee_id)
    end

    it 'includes correct period data' do
      data = service.generate_data
      period_data = data[:period]

      expect(period_data[:month_name]).to eq('June')
      expect(period_data[:year]).to eq(2024)
      expect(period_data[:month_year]).to eq('June 2024')
    end

    it 'generates data for all days in month' do
      data = service.generate_data
      expect(data[:days].length).to eq(30) # June has 30 days
    end

    it 'correctly identifies weekend days' do
      data = service.generate_data
      saturday_data = data[:days].find { |day| day[:day_number] == 1 }
      
      expect(saturday_data[:is_weekend]).to be true
      expect(saturday_data[:status]).to eq('weekend')
    end

    it 'correctly identifies working days with attendance' do
      data = service.generate_data
      monday_data = data[:days].find { |day| day[:day_number] == 3 }
      
      expect(monday_data[:is_present]).to be true
      expect(monday_data[:status]).to eq('present')
      expect(monday_data[:arrival_time]).to eq('08:00')
      expect(monday_data[:departure_time]).to eq('17:00')
    end

    it 'correctly identifies leave days' do
      data = service.generate_data
      leave_day_data = data[:days].find { |day| day[:day_number] == 5 }
      
      expect(leave_day_data[:is_leave]).to be true
      expect(leave_day_data[:status]).to eq('leave')
    end

    it 'calculates correct summary' do
      data = service.generate_data
      summary = data[:summary]

      expect(summary[:total_days]).to eq(30)
      expect(summary[:working_days]).to eq(1) # Only June 3rd has attendance
      expect(summary[:leave_days]).to eq(1) # June 5th
      expect(summary[:weekend_days]).to be > 0
    end
  end

  describe '#generate_html' do

    it 'generates HTML content' do
      html = service.generate_html
      
      expect(html).to include('Time sheet')
      expect(html).to include('John Doe')
      expect(html).to include('June 2024')
    end

    it 'includes attendance table' do
      html = service.generate_html
      
      expect(html).to include('<table class="attendance-table">')
      expect(html).to include('Arrival time')
      expect(html).to include('Leave time')
    end

    it 'handles template rendering errors gracefully' do
      # Mock template rendering to fail and fallback to work
      service.stubs(:render_html_template).raises(StandardError.new('Template error'))
      service.stubs(:generate_html_content).returns('<html>Time sheet fallback</html>')

      html = service.generate_html
      expect(html).to include('Time sheet') # Should fallback to direct HTML generation
    end
  end

  describe '#generate_pdf' do

    it 'generates PDF content' do
      # Mock Grover to return fake PDF content
      fake_pdf = 'fake pdf content'
      grover_instance = stub('Grover')
      grover_instance.stubs(:to_pdf).returns(fake_pdf)
      Grover.stubs(:new).returns(grover_instance)

      pdf = service.generate_pdf
      expect(pdf).to eq(fake_pdf)
    end

    it 'raises error when HTML generation fails' do
      service.stubs(:generate_html).returns('')

      expect {
        service.generate_pdf
      }.to raise_error(TimesheetGenerationError, /HTML generation failed/)
    end

    it 'raises error when PDF conversion fails' do
      service.stubs(:generate_html).returns('<html>test</html>')
      service.stubs(:convert_to_pdf).returns('')

      expect {
        service.generate_pdf
      }.to raise_error(TimesheetGenerationError, /PDF conversion failed/)
    end
  end

  describe 'private methods' do

    describe '#is_weekend?' do
      it 'correctly identifies weekend days' do
        friday = Date.new(2024, 6, 7) # Friday
        saturday = Date.new(2024, 6, 8) # Saturday
        sunday = Date.new(2024, 6, 9) # Sunday
        monday = Date.new(2024, 6, 10) # Monday

        expect(service.send(:is_weekend?, friday)).to be true
        expect(service.send(:is_weekend?, saturday)).to be true
        expect(service.send(:is_weekend?, sunday)).to be false
        expect(service.send(:is_weekend?, monday)).to be false
      end
    end

    describe '#format_time' do
      it 'formats timestamp correctly' do
        timestamp = Time.new(2024, 6, 3, 8, 30, 0).to_i
        formatted = service.send(:format_time, timestamp)
        expect(formatted).to eq('08:30')
      end

      it 'returns nil for nil timestamp' do
        formatted = service.send(:format_time, nil)
        expect(formatted).to be_nil
      end
    end

    describe '#get_arrival_time' do
      it 'returns first check-in time' do
        events = [
          build(:attendance_event, timestamp: Time.new(2024, 6, 3, 8, 0).to_i, event_type: :check_in),
          build(:attendance_event, timestamp: Time.new(2024, 6, 3, 8, 30).to_i, event_type: :check_in)
        ]
        
        arrival = service.send(:get_arrival_time, events)
        expect(arrival).to eq('08:00')
      end

      it 'returns first event if no check-ins' do
        events = [
          build(:attendance_event, timestamp: Time.new(2024, 6, 3, 8, 0).to_i, event_type: :undetermined)
        ]
        
        arrival = service.send(:get_arrival_time, events)
        expect(arrival).to eq('08:00')
      end

      it 'returns nil for empty events' do
        arrival = service.send(:get_arrival_time, [])
        expect(arrival).to be_nil
      end
    end

    describe '#get_departure_time' do
      it 'returns last check-out time' do
        events = [
          build(:attendance_event, timestamp: Time.new(2024, 6, 3, 16, 0).to_i, event_type: :check_out),
          build(:attendance_event, timestamp: Time.new(2024, 6, 3, 17, 0).to_i, event_type: :check_out)
        ]
        
        departure = service.send(:get_departure_time, events)
        expect(departure).to eq('17:00')
      end

      it 'returns last event if no check-outs' do
        events = [
          build(:attendance_event, timestamp: Time.new(2024, 6, 3, 17, 0).to_i, event_type: :undetermined)
        ]
        
        departure = service.send(:get_departure_time, events)
        expect(departure).to eq('17:00')
      end

      it 'returns nil for empty events' do
        departure = service.send(:get_departure_time, [])
        expect(departure).to be_nil
      end
    end
  end

  describe 'edge cases' do

    context 'February in leap year' do
      let(:february_service) { described_class.new(employee, 2024, 2) }

      it 'handles leap year February correctly' do
        data = february_service.generate_data
        expect(data[:days].length).to eq(29) # 2024 is a leap year
      end
    end

    context 'February in non-leap year' do
      let(:february_service) { described_class.new(employee, 2023, 2) }

      it 'handles non-leap year February correctly' do
        data = february_service.generate_data
        expect(data[:days].length).to eq(28) # 2023 is not a leap year
      end
    end

    context 'month with 31 days' do
      let(:january_service) { described_class.new(employee, 2024, 1) }

      it 'handles 31-day months correctly' do
        data = january_service.generate_data
        expect(data[:days].length).to eq(31)
      end
    end
  end
end
