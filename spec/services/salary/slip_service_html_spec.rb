require 'rails_helper'

RSpec.describe Salary::SlipService, type: :service do
  let(:employee) { create(:employee, name: '<PERSON>') }
  let(:salary_package) { create(:salary_package, :approved, employee: employee, base_salary: 3000) }
  let(:salary_calculation) do
    create(:salary_calculation,
           employee: employee,
           salary_package: salary_package,
           gross_salary: 3000,
           net_salary: 2700,
           status: :paid,
           deductions: {
             'income_tax' => 200,
             'employee_social_security' => 225,
             'medical_insurance' => 75
           })
  end

  describe '#generate' do
    context 'when salary calculation is paid' do
      it 'generates PDF using Grover HTML-to-PDF' do
        service = described_class.new(salary_calculation)

        expect {
          result = service.generate
          expect(result).to be_truthy
        }.to change { salary_calculation.salary_slip_pdf.attached? }.from(false).to(true)
      end

      it 'creates a valid PDF file' do
        service = described_class.new(salary_calculation)
        service.generate

        expect(salary_calculation.salary_slip_pdf).to be_attached
        expect(salary_calculation.salary_slip_pdf.content_type).to eq('application/pdf')
        expect(salary_calculation.salary_slip_pdf.byte_size).to be > 0
      end

      it 'generates HTML content with employee data' do
        service = described_class.new(salary_calculation)
        html_content = service.send(:generate_html_content)

        expect(html_content).to include(employee.name)
        expect(html_content).to include('salary-slip')
        expect(html_content).to include('ATHAR')
      end

      it 'includes computed salary fields in HTML content' do
        service = described_class.new(salary_calculation)
        html_content = service.send(:generate_html_content)

        # Check for computed deduction fields
        expect(html_content).to include('Income Tax')
        expect(html_content).to include('Employee Social Security')
        expect(html_content).to include('JOD 200.00') # Income tax amount
        expect(html_content).to include('JOD 225.00') # Social security amount
      end

      it 'includes employer contribution information' do
        service = described_class.new(salary_calculation)
        html_content = service.send(:generate_html_content)

        # Check for employer contributions
        expect(html_content).to include('Employer Social Security')
        expect(html_content).to include('14.25% employer contribution')
        expect(html_content).to include('7.50% employer contribution')
      end

      it 'includes employee profile information' do
        service = described_class.new(salary_calculation)
        html_content = service.send(:generate_html_content)

        # Check for employee profile fields
        expect(html_content).to include(employee.employee_id)
        expect(html_content).to include(employee.staff_function_code)
        expect(html_content).to include(employee.workplace_code)
      end
    end

    context 'when salary calculation is not paid' do
      before { salary_calculation.update!(status: :approved) }

      it 'does not generate PDF' do
        service = described_class.new(salary_calculation)
        result = service.generate

        expect(result).to be_falsey
        expect(salary_calculation.salary_slip_pdf).not_to be_attached
      end
    end

    context 'when HTML generation fails' do
      before do
        Grover.stubs(:new).raises(StandardError.new('Grover error'))
      end

      it 'raises an error' do
        service = described_class.new(salary_calculation)

        Rails.logger.expects(:error).with(/Failed to generate HTML PDF/)

        expect {
          service.generate
        }.to raise_error(StandardError, 'Grover error')
      end
    end
  end
end
