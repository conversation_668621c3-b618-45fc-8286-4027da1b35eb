require 'rails_helper'

RSpec.describe Employee, type: :model do
  describe '#daily_salary_rate' do
    let(:employee) { create(:employee) }
    let(:salary_package) { create(:salary_package, employee: employee, base_salary: 2200) }

    context 'when employee has current salary package' do
      before do
        salary_package.update!(status: :approved, effective_date: Date.current)
      end

      it 'calculates daily rate correctly using simplified formula' do
        # V2: Simplified calculation = base_salary ÷ 30 days
        # 2200 ÷ 30 = 73.33
        expect(employee.daily_salary_rate.to_f).to be_within(0.01).of(73.33)
      end
    end

    context 'when employee has no current salary package' do
      # No salary package created, so employee.salary_package will be nil

      it 'returns 0' do
        expect(employee.daily_salary_rate).to eq(0)
      end
    end

    context 'with different base salary amounts' do
      before do
        salary_package.update!(base_salary: 3000, status: :approved, effective_date: Date.current)
      end

      it 'always uses base salary divided by 30' do
        # V2: Always base_salary ÷ 30, regardless of allowances or settings
        # 3000 ÷ 30 = 100
        expect(employee.daily_salary_rate.to_f).to eq(100.0)
      end
    end

    context 'with different salary package amount' do
      let(:high_salary_package) { create(:salary_package, employee: employee, base_salary: 4400) }

      before do
        high_salary_package.update!(status: :approved, effective_date: Date.current)
      end

      it 'calculates based on base salary only using V2 formula' do
        # V2: Always base_salary ÷ 30, regardless of allowances
        # 4400 ÷ 30 = 146.67
        expect(employee.daily_salary_rate.to_f).to be_within(0.01).of(146.67)
      end
    end
  end
end
