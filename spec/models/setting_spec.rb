require 'rails_helper'

RSpec.describe Setting, type: :model do
  describe 'validations' do
    it 'validates presence of required fields' do
      setting = Setting.new
      expect(setting).not_to be_valid
      expect(setting.errors[:namespace]).to include("can't be blank")
      expect(setting.errors[:key]).to include("can't be blank")
      expect(setting.errors[:value]).to include("can't be blank")
    end

    it 'validates uniqueness of key within namespace' do
      create(:setting, namespace: 'test', key: 'duplicate_key', value: 'value1')
      setting = build(:setting, namespace: 'test', key: 'duplicate_key', value: 'value2')

      expect(setting).not_to be_valid
      expect(setting.errors[:key]).to include('has already been taken')
    end

    it 'allows same key in different namespaces' do
      create(:setting, namespace: 'test1', key: 'same_key', value: 'value1')
      setting = build(:setting, namespace: 'test2', key: 'same_key', value: 'value2')

      expect(setting).to be_valid
    end
  end

  describe 'setting_type enum' do
    it 'has all expected types' do
      expected_types = %w[string integer boolean decimal time json email phone url currency array date datetime]
      expect(Setting.setting_types.keys).to match_array(expected_types)
    end

    it 'defaults to string type' do
      setting = Setting.new
      expect(setting.setting_type).to eq('string')
    end
  end

  describe '#value' do
    context 'with integer type' do
      it 'returns integer value' do
        setting = create(:setting, setting_type: :integer, value: '123')
        expect(setting.value).to eq(123)
        expect(setting.value).to be_a(Integer)
      end
    end

    context 'with boolean type' do
      it 'returns true for truthy values' do
        %w[true 1 yes on t y].each do |val|
          setting = create(:setting, setting_type: :boolean, value: val)
          expect(setting.value).to be true
        end
      end

      it 'returns false for falsy values' do
        %w[false 0 no off f n].each do |val|
          setting = create(:setting, setting_type: :boolean, value: val)
          expect(setting.value).to be false
        end
      end
    end

    context 'with decimal type' do
      it 'returns BigDecimal value' do
        setting = create(:setting, setting_type: :decimal, value: '123.45')
        expect(setting.value).to eq(BigDecimal('123.45'))
        expect(setting.value).to be_a(BigDecimal)
      end
    end

    context 'with time type' do
      it 'returns Time object' do
        setting = create(:setting, setting_type: :time, value: '14:30')
        result = setting.value
        expect(result).to be_a(Time)
        expect(result.hour).to eq(14)
        expect(result.min).to eq(30)
      end
    end

    context 'with json type' do
      it 'returns parsed JSON' do
        setting = create(:setting, setting_type: :json, value: '{"key": "value", "number": 42}')
        result = setting.value
        expect(result).to eq({ 'key' => 'value', 'number' => 42 })
      end
    end

    context 'with array type' do
      it 'returns array from comma-separated string' do
        setting = create(:setting, setting_type: :array, value: 'item1,item2,item3')
        expect(setting.value).to eq([ 'item1', 'item2', 'item3' ])
      end

      it 'handles spaces around commas' do
        setting = create(:setting, setting_type: :array, value: 'item1, item2 , item3')
        expect(setting.value).to eq([ 'item1', 'item2', 'item3' ])
      end
    end

    context 'with date type' do
      it 'returns Date object' do
        setting = create(:setting, setting_type: :date, value: '2023-12-25')
        result = setting.value
        expect(result).to be_a(Date)
        expect(result.year).to eq(2023)
        expect(result.month).to eq(12)
        expect(result.day).to eq(25)
      end
    end

    context 'with string type' do
      it 'returns string value unchanged' do
        setting = create(:setting, setting_type: :string, value: 'test value')
        expect(setting.value).to eq('test value')
        expect(setting.value).to be_a(String)
      end
    end

    context 'with invalid value for type' do
      it 'returns default value and logs error' do
        setting = create(:setting, setting_type: :integer, value: 'not_a_number')
        Rails.logger.expects(:error).with(/Error parsing setting/)
        expect(setting.value).to eq(0) # default for integer
      end
    end
  end

  describe '#value=' do
    it 'normalizes input based on type' do
      setting = Setting.new(setting_type: :boolean)
      setting.value = true
      expect(setting.raw_value).to eq('true')
    end

    it 'handles time input normalization' do
      setting = Setting.new(setting_type: :time)
      setting.value = '9:30'
      expect(setting.raw_value).to eq('09:30')
    end

    it 'handles array input normalization' do
      setting = Setting.new(setting_type: :array)
      setting.value = [ 'a', 'b', 'c' ]
      expect(setting.raw_value).to eq('a,b,c')
    end
  end

  describe 'class methods' do
    describe '.get' do
      it 'returns typed value when found' do
        create(:setting, namespace: 'test', key: 'number', value: '42', setting_type: :integer)
        expect(Setting.get('test', 'number')).to eq(42)
      end

      it 'returns string value for string type' do
        create(:setting, namespace: 'test', key: 'sample', value: 'result', setting_type: :string)
        expect(Setting.get('test', 'sample')).to eq('result')
      end

      it 'returns default when setting not found' do
        expect(Setting.get('test', 'missing', 'default')).to eq('default')
      end
    end

    describe '.set' do
      it 'creates new setting with type-aware parsing' do
        Setting.set('test', 'new_key', 123, 'Test setting', true, :integer)
        setting = Setting.find_by(namespace: 'test', key: 'new_key')

        expect(setting).to be_present
        expect(setting.setting_type).to eq('integer')
        expect(setting.value).to eq('123')  # Stored as string
        expect(setting.value).to eq(123)  # Returns as integer
        expect(setting.description).to eq('Test setting')
      end

      it 'updates existing setting with type-aware parsing' do
        create(:setting, namespace: 'test', key: 'existing', value: 'false', setting_type: :boolean)
        Setting.set('test', 'existing', true, 'Updated', true, :boolean)

        setting = Setting.find_by(namespace: 'test', key: 'existing')
        expect(setting.value).to eq('true')  # Stored as string
        expect(setting.value).to be true  # Returns as boolean
        expect(setting.description).to eq('Updated')
      end
    end
  end

  describe 'helper methods' do
    describe 'attendance settings' do
      before do
        create(:setting, namespace: 'attendance', key: 'work_start_time', value: '09:00', setting_type: :time)
        create(:setting, namespace: 'attendance', key: 'required_work_minutes', value: '480', setting_type: :integer)
        create(:setting, namespace: 'attendance', key: 'auto_leave_enabled', value: 'true', setting_type: :boolean)
      end

      it 'returns typed values for attendance settings' do
        expect(Setting.attendance_work_start_time).to be_a(Time)
        expect(Setting.attendance_required_work_minutes).to eq(480)
        expect(Setting.attendance_auto_leave_enabled?).to be true
      end
    end
  end

  describe '#validation_rules_for_type' do
    it 'returns appropriate rules for each type' do
      setting = Setting.new(setting_type: :email)
      rules = setting.validation_rules_for_type

      expect(rules[:type]).to eq('email')
      expect(rules[:example]).to be_present
    end
  end
end
