require 'rails_helper'

RSpec.describe Attendance::Exemption, type: :model do
  describe 'validations' do
    it 'requires name' do
      exemption = Attendance::Exemption.new(start_date: Date.current, end_date: Date.current)
      expect(exemption).not_to be_valid
      expect(exemption.errors[:name]).to include("can't be blank")
    end

    it 'requires start_date' do
      exemption = Attendance::Exemption.new(name: 'Test Holiday', end_date: Date.current)
      expect(exemption).not_to be_valid
      expect(exemption.errors[:start_date]).to include("can't be blank")
    end

    it 'requires end_date' do
      exemption = Attendance::Exemption.new(name: 'Test Holiday', start_date: Date.current)
      expect(exemption).not_to be_valid
      expect(exemption.errors[:end_date]).to include("can't be blank")
    end

    it 'validates end_date is after or equal to start_date' do
      exemption = Attendance::Exemption.new(
        name: 'Test Holiday',
        start_date: Date.current,
        end_date: Date.current - 1.day
      )
      expect(exemption).not_to be_valid
      expect(exemption.errors[:end_date]).to include("must be after or equal to start date")
    end
  end

  describe 'scopes' do
    let!(:active_exemption) { create(:attendance_exemption, is_active: true) }
    let!(:inactive_exemption) { create(:attendance_exemption, is_active: false) }

    it 'filters active exemptions' do
      expect(Attendance::Exemption.active).to include(active_exemption)
      expect(Attendance::Exemption.active).not_to include(inactive_exemption)
    end
  end

  describe 'helper methods' do
    let(:exemption) do
      Attendance::Exemption.new(
        name: 'Test Holiday',
        start_date: Date.new(2024, 1, 1),
        end_date: Date.new(2024, 1, 3),
        exemption_type: :holiday
      )
    end

    it 'calculates duration_days correctly' do
      expect(exemption.duration_days).to eq(3)
    end

    it 'returns exemption_type_label' do
      expect(exemption.exemption_type_label).to eq('Holiday')
    end

    it 'checks if covers_date?' do
      expect(exemption.covers_date?(Date.new(2024, 1, 2))).to be true
      expect(exemption.covers_date?(Date.new(2024, 1, 5))).to be false
    end
  end
end
