require 'rails_helper'

RSpec.describe Attendance::Period, type: :model do
  let(:employee) { create(:employee) }
  let(:date) { Date.current }

  describe 'validations' do
    it 'validates auto_generated presence' do
      period = Attendance::Period.new(
        employee: employee,
        date: date,
        period_type: 'work',
        start_timestamp: Time.current.to_i,
        end_timestamp: (Time.current + 1.hour).to_i,
        duration_minutes: 60
      )
      # auto_generated should default to false
      expect(period).to be_valid
      expect(period.auto_generated).to eq(false)
    end

    it 'validates period_type inclusion with new leave type' do
      valid_types = [ 'work', 'break', 'late', 'early_departure', 'early_arrival', 'leave' ]

      valid_types.each do |type|
        period = build(:attendance_period, period_type: type)
        expect(period).to be_valid, "Expected #{type} to be valid"
      end

      invalid_period = build(:attendance_period, period_type: 'invalid_type')
      expect(invalid_period).not_to be_valid
    end
  end

  describe 'scopes' do
    let!(:work_period) { create(:attendance_period, period_type: 'work') }
    let!(:leave_period) { create(:attendance_period, period_type: 'leave', auto_generated: true) }
    let!(:manual_leave) { create(:attendance_period, period_type: 'leave', auto_generated: false) }
    let!(:auto_work) { create(:attendance_period, period_type: 'work', auto_generated: true) }

    describe 'leave-related scopes' do
      it 'filters leave periods' do
        expect(Attendance::Period.leave_periods).to include(leave_period, manual_leave)
        expect(Attendance::Period.leave_periods).not_to include(work_period)
      end

      it 'filters auto-generated periods' do
        expect(Attendance::Period.auto_generated).to include(leave_period, auto_work)
        expect(Attendance::Period.auto_generated).not_to include(manual_leave, work_period)
      end

      it 'filters manual periods' do
        expect(Attendance::Period.manual).to include(manual_leave, work_period)
        expect(Attendance::Period.manual).not_to include(leave_period, auto_work)
      end

      it 'filters auto-generated leaves' do
        expect(Attendance::Period.auto_generated_leaves).to include(leave_period)
        expect(Attendance::Period.auto_generated_leaves).not_to include(manual_leave, work_period, auto_work)
      end

      it 'filters manual leaves' do
        expect(Attendance::Period.manual_leaves).to include(manual_leave)
        expect(Attendance::Period.manual_leaves).not_to include(leave_period, work_period, auto_work)
      end
    end

    describe 'work-related scopes' do
      it 'filters work periods' do
        expect(Attendance::Period.work_periods).to include(work_period, auto_work)
        expect(Attendance::Period.work_periods).not_to include(leave_period, manual_leave)
      end
    end
  end

  describe 'helper methods' do
    describe 'auto-generated status methods' do
      let(:auto_period) { build(:attendance_period, auto_generated: true) }
      let(:manual_period) { build(:attendance_period, auto_generated: false) }

      it 'identifies auto-generated periods' do
        expect(auto_period.auto_generated?).to be true
        expect(manual_period.auto_generated?).to be false
      end

      it 'identifies manual periods' do
        expect(auto_period.manual?).to be false
        expect(manual_period.manual?).to be true
      end
    end

    describe 'period type methods' do
      let(:leave_period) { build(:attendance_period, period_type: 'leave') }
      let(:work_period) { build(:attendance_period, period_type: 'work') }

      it 'identifies leave periods' do
        expect(leave_period.leave_period?).to be true
        expect(work_period.leave_period?).to be false
      end

      it 'identifies work periods' do
        expect(work_period.work_period?).to be true
        expect(leave_period.work_period?).to be false
      end
    end

    describe 'combined leave methods' do
      let(:auto_leave) { build(:attendance_period, period_type: 'leave', auto_generated: true) }
      let(:manual_leave) { build(:attendance_period, period_type: 'leave', auto_generated: false) }
      let(:auto_work) { build(:attendance_period, period_type: 'work', auto_generated: true) }

      it 'identifies auto-generated leaves' do
        expect(auto_leave.auto_generated_leave?).to be true
        expect(manual_leave.auto_generated_leave?).to be false
        expect(auto_work.auto_generated_leave?).to be false
      end

      it 'identifies manual leaves' do
        expect(manual_leave.manual_leave?).to be true
        expect(auto_leave.manual_leave?).to be false
        expect(auto_work.manual_leave?).to be false
      end
    end
  end

  describe '#duration_hours' do
    it 'converts minutes to hours correctly' do
      period = build(:attendance_period, duration_minutes: 480) # 8 hours
      expect(period.duration_hours).to eq(8.0)
    end

    it 'handles fractional hours' do
      period = build(:attendance_period, duration_minutes: 90) # 1.5 hours
      expect(period.duration_hours).to eq(1.5)
    end
  end

  describe '#leave_type' do
    it 'returns auto_generated for auto-generated leave' do
      period = build(:attendance_period, period_type: 'leave', auto_generated: true)
      expect(period.leave_type).to eq('auto_generated')
    end

    it 'returns manual for manual leave' do
      period = build(:attendance_period, period_type: 'leave', auto_generated: false)
      expect(period.leave_type).to eq('manual')
    end

    it 'returns nil for non-leave periods' do
      period = build(:attendance_period, period_type: 'work')
      expect(period.leave_type).to be_nil
    end
  end

  describe 'class methods' do
    describe '.calculate_for_employee_date' do
      it 'delegates to PeriodService' do
        Attendance::PeriodService.expects(:new)
          .with(employee, date, false)
          .returns(mock(calculate_periods: []))

        Attendance::Period.calculate_for_employee_date(employee, date)
      end
    end

    describe '.recalculate_for_date_range' do
      it 'calculates for each date in range' do
        start_date = Date.current
        end_date = Date.current + 2.days

        Attendance::Period.expects(:calculate_for_employee_date)
          .times(3)

        Attendance::Period.recalculate_for_date_range(employee, start_date, end_date)
      end
    end
  end

  describe 'date range scope' do
    let!(:period1) { create(:attendance_period, date: Date.current) }
    let!(:period2) { create(:attendance_period, date: Date.current + 1.day) }
    let!(:period3) { create(:attendance_period, date: Date.current + 3.days) }

    it 'filters by date range' do
      start_date = Date.current
      end_date = Date.current + 1.day

      periods = Attendance::Period.for_date_range(start_date, end_date)
      expect(periods).to include(period1, period2)
      expect(periods).not_to include(period3)
    end
  end

  describe 'integration with new attendance system' do
    context 'when creating auto-generated leave' do
      it 'creates leave period with correct attributes' do
        period = Attendance::Period.create!(
          employee: employee,
          date: date,
          period_type: 'leave',
          start_timestamp: date.beginning_of_day.to_i,
          end_timestamp: date.end_of_day.to_i,
          duration_minutes: 480,
          auto_generated: true,
          notes: 'Auto-generated: No attendance recorded'
        )

        expect(period).to be_persisted
        expect(period.leave_period?).to be true
        expect(period.auto_generated?).to be true
        expect(period.auto_generated_leave?).to be true
        expect(period.leave_type).to eq('auto_generated')
        expect(period.duration_hours).to eq(8.0)
      end
    end

    context 'when creating manual leave' do
      it 'creates leave period with correct attributes' do
        period = Attendance::Period.create!(
          employee: employee,
          date: date,
          period_type: 'leave',
          start_timestamp: date.beginning_of_day.to_i,
          end_timestamp: date.end_of_day.to_i,
          duration_minutes: 480,
          auto_generated: false,
          notes: 'Manual leave request'
        )

        expect(period).to be_persisted
        expect(period.leave_period?).to be true
        expect(period.manual?).to be true
        expect(period.manual_leave?).to be true
        expect(period.leave_type).to eq('manual')
      end
    end
  end
end
