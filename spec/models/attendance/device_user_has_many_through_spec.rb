# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'DeviceUser has_many through implementation' do
  describe 'ActiveStruct has_many through associations' do
    before do
      # Create test models to verify has_many through works
      module TestModels
        class Device < Athar::Commons::ActiveStruct::Base
          with_id(auto_generate: false)

          attribute :id, :integer
          attribute :name, :string

          has_many :employee_device_mappings, class_name: 'TestModels::EmployeeDeviceMapping'
        end

        class EmployeeDeviceMapping < Athar::Commons::ActiveStruct::Base
          with_id(auto_generate: false)

          attribute :id, :integer
          attribute :device_user_id, :string
          attribute :notes, :string

          belongs_to :employee, class_name: 'TestModels::Employee'
          belongs_to :device, class_name: 'TestModels::Device'
        end

        class Employee < Athar::Commons::ActiveStruct::Base
          with_id(auto_generate: false)

          attribute :id, :integer
          attribute :name, :string
          attribute :email, :string
        end

        class DeviceUser < Athar::Commons::ActiveStruct::Base
          with_id(auto_generate: false)

          attribute :id, :integer
          attribute :user_id, :integer
          attribute :name, :string

          belongs_to :device, class_name: 'TestModels::Device'
          has_many :employee_device_mappings, through: :device

          def mapping
            employee_device_mappings.find { |m| m.device_user_id == user_id.to_s }
          end

          def mapped?
            mapping.present?
          end

          def employee
            mapping&.employee
          end
        end

        class EmployeeDeviceMappingCollection
          include Athar::Commons::ActiveStruct::Collection
          collection_item_class EmployeeDeviceMapping
        end
      end
    end

    after do
      # Clean up test modules
      Object.send(:remove_const, :TestModels) if defined?(TestModels)
    end

    it 'supports has_many through associations' do
      # Create device with mappings
      device = TestModels::Device.new(
        id: 1,
        name: "Main Entrance",
        employee_device_mappings: [
          {
            id: 1,
            device_user_id: "123",
            notes: "Auto-created",
            employee: {
              id: 456,
              name: "John Doe",
              email: "<EMAIL>"
            }
          },
          {
            id: 2,
            device_user_id: "456",
            notes: "Manual mapping",
            employee: {
              id: 789,
              name: "Jane Smith",
              email: "<EMAIL>"
            }
          }
        ]
      )

      # Create device user
      device_user = TestModels::DeviceUser.new(
        id: 123,
        user_id: 123,
        name: "John Doe",
        device: device
      )

      # Test the through association
      expect(device_user.employee_device_mappings).to be_an(Array)
      expect(device_user.employee_device_mappings.size).to eq(2)

      # Test finding specific mapping
      mapping = device_user.mapping
      expect(mapping).to be_present
      expect(mapping.device_user_id).to eq("123")
      expect(mapping.notes).to eq("Auto-created")

      # Test mapped status
      expect(device_user.mapped?).to be true

      # Test employee access
      employee = device_user.employee
      expect(employee).to be_present
      expect(employee.name).to eq("John Doe")
      expect(employee.email).to eq("<EMAIL>")
    end

    it 'handles unmapped device users' do
      # Create device with mappings that don't match
      device = TestModels::Device.new(
        id: 1,
        name: "Main Entrance",
        employee_device_mappings: [
          {
            id: 1,
            device_user_id: "999", # Different user ID
            notes: "Other user",
            employee: {
              id: 456,
              name: "Other User",
              email: "<EMAIL>"
            }
          }
        ]
      )

      # Create device user that doesn't have a mapping
      device_user = TestModels::DeviceUser.new(
        id: 123,
        user_id: 123,
        name: "Unmapped User",
        device: device
      )

      # Test the through association still works
      expect(device_user.employee_device_mappings).to be_an(Array)
      expect(device_user.employee_device_mappings.size).to eq(1)

      # Test no mapping found
      expect(device_user.mapping).to be_nil
      expect(device_user.mapped?).to be false
      expect(device_user.employee).to be_nil
    end

    it 'reflects associations correctly' do
      # Test reflection API
      association = TestModels::DeviceUser.reflect_on_association(:employee_device_mappings)
      expect(association).to be_present
      expect(association.macro).to eq(:has_many)
      expect(association.options[:through]).to eq(:device)
    end
  end
end
