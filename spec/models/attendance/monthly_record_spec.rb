require 'rails_helper'

RSpec.describe Attendance::MonthlyRecord, type: :model do
  let(:employee) { create(:employee) }
  let(:year) { 2024 }
  let(:month) { 7 }

  describe 'validations' do
    it 'validates presence of required fields' do
      record = Attendance::MonthlyRecord.new
      expect(record).not_to be_valid
      expect(record.errors[:employee]).to include("must exist")
      expect(record.errors[:year]).to include("can't be blank")
      expect(record.errors[:month]).to include("can't be blank")
      # Note: expected_hours validation may not show "can't be blank" due to numericality validation
    end

    it 'validates year range' do
      record = build(:attendance_monthly_record, year: 1999)
      expect(record).not_to be_valid
      expect(record.errors[:year]).to include("must be greater than 2020")

      record.year = 2100
      expect(record).not_to be_valid
      expect(record.errors[:year]).to include("must be less than 2100")
    end

    it 'validates month range' do
      record = build(:attendance_monthly_record, month: 0)
      expect(record).not_to be_valid
      expect(record.errors[:month]).to include("must be in 1..12")

      record.month = 13
      expect(record).not_to be_valid
      expect(record.errors[:month]).to include("must be in 1..12")
    end

    it 'validates hours are non-negative' do
      record = build(:attendance_monthly_record, expected_hours: -1)
      expect(record).not_to be_valid
      expect(record.errors[:expected_hours]).to include("must be greater than 0")

      record = build(:attendance_monthly_record, actual_hours: -1)
      expect(record).not_to be_valid
      expect(record.errors[:actual_hours]).to include("must be greater than or equal to 0")

      record = build(:attendance_monthly_record, deficit_hours: -1)
      expect(record).not_to be_valid
      expect(record.errors[:deficit_hours]).to include("must be greater than or equal to 0")
    end

    it 'validates uniqueness of employee, year, and month' do
      create(:attendance_monthly_record, employee: employee, year: year, month: month)
      duplicate = build(:attendance_monthly_record, employee: employee, year: year, month: month)
      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:year]).to include("has already been taken")
    end
  end

  describe 'scopes' do
    let!(:record_2024) { create(:attendance_monthly_record, employee: employee, year: 2024, month: 6) }
    let!(:record_2023) { create(:attendance_monthly_record, employee: employee, year: 2023, month: 7) }
    let!(:record_july) { create(:attendance_monthly_record, employee: employee, year: 2024, month: 7) }
    let!(:record_august) { create(:attendance_monthly_record, employee: employee, year: 2024, month: 8) }

    it 'filters by year' do
      expect(Attendance::MonthlyRecord.for_year(2024)).to include(record_2024)
      expect(Attendance::MonthlyRecord.for_year(2024)).not_to include(record_2023)
    end

    it 'filters by month' do
      expect(Attendance::MonthlyRecord.for_month(7)).to include(record_july)
      expect(Attendance::MonthlyRecord.for_month(7)).not_to include(record_august)
    end

    it 'filters by employee' do
      other_employee = create(:employee)
      other_record = create(:attendance_monthly_record, employee: other_employee)

      expect(Attendance::MonthlyRecord.for_employee(employee)).to include(record_2024)
      expect(Attendance::MonthlyRecord.for_employee(employee)).not_to include(other_record)
    end

    it 'filters by year and month' do
      records = Attendance::MonthlyRecord.for_year_month(2024, 7)
      expect(records).to include(record_july)
      expect(records).not_to include(record_august)
      expect(records).not_to include(record_2023)
    end
  end

  describe '.calculate_for_employee_month' do
    let!(:work_period1) do
      create(:attendance_period,
             employee: employee,
             date: Date.new(2024, 7, 1),
             period_type: 'work',
             duration_minutes: 480) # 8 hours
    end

    let!(:work_period2) do
      create(:attendance_period,
             employee: employee,
             date: Date.new(2024, 7, 2),
             period_type: 'work',
             duration_minutes: 360) # 6 hours
    end

    it 'calculates monthly record correctly' do
      record = Attendance::MonthlyRecord.calculate_for_employee_month(employee, 2024, 7)

      # Calculate expected hours dynamically (23 working days × 8 hours = 184 hours)
      expected_hours = Attendance::WorkingDaysCalculator.monthly_expected_hours(2024, 7)

      expect(record.employee).to eq(employee)
      expect(record.year).to eq(2024)
      expect(record.month).to eq(7)
      expect(record.expected_hours).to eq(expected_hours) # Dynamic calculation based on working days
      expect(record.actual_hours).to eq(14.0) # 8 + 6 hours
      expect(record.deficit_hours).to eq(expected_hours - 14.0) # expected - actual
      expect(record).to be_persisted
    end

    it 'updates existing record' do
      existing = create(:attendance_monthly_record,
                       employee: employee,
                       year: 2024,
                       month: 7,
                       actual_hours: 100.0)

      record = Attendance::MonthlyRecord.calculate_for_employee_month(employee, 2024, 7)

      expect(record.id).to eq(existing.id)
      expect(record.actual_hours).to eq(14.0) # Recalculated
    end
  end

  describe '.get_or_calculate' do
    it 'returns existing record if found' do
      existing = create(:attendance_monthly_record, employee: employee, year: year, month: month)
      record = Attendance::MonthlyRecord.get_or_calculate(employee, year, month)
      expect(record.id).to eq(existing.id)
    end

    it 'calculates new record if not found' do
      expect {
        Attendance::MonthlyRecord.get_or_calculate(employee, year, month)
      }.to change(Attendance::MonthlyRecord, :count).by(1)
    end
  end

  describe '.bulk_calculate_for_employee' do
    it 'calculates multiple months for employee' do
      start_date = Date.new(2024, 6, 1)
      end_date = Date.new(2024, 8, 31)

      expect {
        Attendance::MonthlyRecord.bulk_calculate_for_employee(employee, start_date, end_date)
      }.to change(Attendance::MonthlyRecord, :count).by(3) # June, July, August

      records = Attendance::MonthlyRecord.for_employee(employee)
      expect(records.map(&:month)).to contain_exactly(6, 7, 8)
    end
  end

  describe '#attendance_percentage' do
    it 'calculates percentage correctly' do
      record = build(:attendance_monthly_record, expected_hours: 180, actual_hours: 144)
      expect(record.attendance_percentage).to eq(80.0)
    end

    it 'handles zero expected hours' do
      record = build(:attendance_monthly_record, expected_hours: 0, actual_hours: 100)
      expect(record.attendance_percentage).to eq(100.0)
    end

    it 'caps at 100%' do
      record = build(:attendance_monthly_record, expected_hours: 180, actual_hours: 200)
      expect(record.attendance_percentage).to eq(100.0)
    end
  end

  describe '#deficit_percentage' do
    it 'calculates deficit percentage correctly' do
      record = build(:attendance_monthly_record, expected_hours: 180, deficit_hours: 36)
      expect(record.deficit_percentage).to eq(20.0)
    end

    it 'handles zero expected hours' do
      record = build(:attendance_monthly_record, expected_hours: 0, deficit_hours: 10)
      expect(record.deficit_percentage).to eq(0.0)
    end
  end

  describe '#has_deficit?' do
    it 'returns true when deficit exists' do
      record = build(:attendance_monthly_record, deficit_hours: 10)
      expect(record.has_deficit?).to be true
    end

    it 'returns false when no deficit' do
      record = build(:attendance_monthly_record, deficit_hours: 0)
      expect(record.has_deficit?).to be false
    end
  end

  describe '#calculate_salary_deduction' do
    it 'calculates deduction based on deficit percentage' do
      record = build(:attendance_monthly_record, expected_hours: 180, deficit_hours: 36) # 20% deficit
      base_salary = 5000

      deduction = record.calculate_salary_deduction(base_salary)
      expect(deduction).to eq(1000.0) # 20% of 5000
    end

    it 'returns zero when no deficit' do
      record = build(:attendance_monthly_record, deficit_hours: 0)
      deduction = record.calculate_salary_deduction(5000)
      expect(deduction).to eq(0.0)
    end
  end

  describe '#working_days_in_month' do
    it 'calculates working days correctly' do
      record = build(:attendance_monthly_record, year: 2024, month: 7) # July 2024
      working_days = record.working_days_in_month

      # July 2024: 31 days, weekends are Friday(5) and Saturday(6)
      # Should exclude Fridays and Saturdays
      expect(working_days).to be > 0
      expect(working_days).to be < 31
    end
  end

  describe '#month_name' do
    it 'returns correct month name' do
      record = build(:attendance_monthly_record, month: 7)
      expect(record.month_name).to eq('July')
    end
  end
end
