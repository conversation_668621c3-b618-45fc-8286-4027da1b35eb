# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Attendance::DeviceCommand::ExecutionConcern, type: :concern do
  let(:test_class) do
    Class.new do
      include Attendance::DeviceCommand::ExecutionConcern
    end
  end

  let(:device) { create(:attendance_device) }
  let(:employee) { create(:employee) }
  let(:adapter) { mock('adapter') }
  let(:execution) { create(:attendance_command_execution, device: device) }
  let(:command_result) { Attendance::CommandResult.success('Command executed successfully') }

  before do
    device.stubs(:create_adapter).returns(adapter)
    Attendance::CommandExecution.stubs(:create!).returns(execution)
    execution.stubs(:update!)
  end

  describe '.execute_for_device' do
    it 'creates execution record with correct attributes' do
      adapter.stubs(:supports_commands?).returns(true)
      adapter.stubs(:execute_command).returns(command_result)

      test_class.execute_for_device(device, 'test_command', { param: 'value' }, employee)

      Attendance::CommandExecution.expects(:create!).with({
                                                                             device: device,
                                                                             command_name: 'test_command',
                                                                             parameters: { param: 'value' },
                                                                             status: :running,
                                                                             executed_by: employee,
                                                                             started_at: be_within(1.second).of(Time.current)
                                                                           })
    end

    context 'when device does not support commands' do
      before do
        adapter.stubs(:supports_commands?).returns(false)
      end

      it 'marks execution as failed and returns failure result' do
        result = test_class.execute_for_device(device, 'test_command', {}, employee)

        expect(result.success).to be false
        expect(result.error).to eq("Device does not support commands")
        execution.expects(:update!).with({
                                                            status: :failed,
                                                            result: result.as_json,
                                                            completed_at: be_within(1.second).of(Time.current)
                                                          })
      end
    end

    context 'when command executes successfully' do
      before do
        adapter.stubs(:supports_commands?).returns(true)
        adapter.stubs(:execute_command).with('test_command', {}).returns(command_result)
      end

      it 'marks execution as completed and returns success result' do
        result = test_class.execute_for_device(device, 'test_command', {}, employee)

        expect(result).to eq(command_result)
        execution.expects(:update!).with({
                                                            status: :completed,
                                                            result: command_result.as_json,
                                                            completed_at: be_within(1.second).of(Time.current)
                                                          })
      end
    end

    context 'when command execution fails' do
      let(:failed_result) { Attendance::CommandResult.failure('Command failed') }

      before do
        adapter.stubs(:supports_commands?).returns(true)
        adapter.stubs(:execute_command).returns(failed_result)
      end

      it 'marks execution as failed based on result' do
        result = test_class.execute_for_device(device, 'test_command', {}, employee)

        expect(result).to eq(failed_result)
        execution.expects(:update!).with({
                                                            status: :failed,
                                                            result: failed_result.as_json,
                                                            completed_at: be_within(1.second).of(Time.current)
                                                          })
      end
    end

    context 'when command execution raises exception' do
      before do
        adapter.stubs(:supports_commands?).returns(true)
        adapter.stubs(:execute_command).raises(StandardError, 'Connection timeout')
      end

      it 'catches exception and marks execution as failed' do
        result = test_class.execute_for_device(device, 'test_command', {}, employee)

        expect(result.success).to be false
        expect(result.error).to eq('Connection timeout')
        execution.expects(:update!).with({
                                                            status: :failed,
                                                            result: result.as_json,
                                                            completed_at: be_within(1.second).of(Time.current)
                                                          })
      end
    end
  end
end
