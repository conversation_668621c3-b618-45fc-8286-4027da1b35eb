# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Attendance::DeviceCommand::ValidationConcern, type: :concern do
  let(:test_class) do
    Class.new do
      include Attendance::DeviceCommand::ValidationConcern
    end
  end

  let(:device) { create(:attendance_device) }
  let(:adapter) { mock('adapter') }
  let(:command_class) { mock('command_class') }
  let(:command_instance) { mock('command_instance') }

  before do
    device.stubs(:create_adapter).returns(adapter)
  end

  describe '.validate_for_device' do
    context 'when device does not support commands' do
      before do
        adapter.stubs(:supports_commands?).returns(false)
      end

      it 'returns validation error for unsupported device' do
        result = test_class.validate_for_device(device, 'test_command', {})

        expect(result).to eq({
                               valid: false,
                               error: "Device does not support commands"
                             })
      end
    end

    context 'when command class is not found' do
      before do
        adapter.stubs(:supports_commands?).returns(true)
        adapter.stubs(:get_command_class).with('unknown_command').returns(nil)
        device.stubs(:adapter_type).returns('TestAdapter')
      end

      it 'returns validation error for unknown command' do
        result = test_class.validate_for_device(device, 'unknown_command', {})

        expect(result).to eq({
                               valid: false,
                               error: "Command 'unknown_command' not supported by TestAdapter adapter"
                             })
      end
    end

    context 'when command validation fails' do
      before do
        adapter.stubs(:supports_commands?).returns(true)
        adapter.stubs(:get_command_class).with('test_command').returns(command_class)
        command_class.stubs(:new).with({ param: 'invalid' }).returns(command_instance)
        command_instance.stubs(:validation_result).returns({
                                                                            valid: false,
                                                                            error: "Parameter 'param' is required"
                                                                          })
      end

      it 'returns command validation error' do
        result = test_class.validate_for_device(device, 'test_command', { param: 'invalid' })

        expect(result).to eq({
                               valid: false,
                               error: "Parameter 'param' is required"
                             })
      end
    end

    context 'when command validation succeeds' do
      before do
        adapter.stubs(:supports_commands?).returns(true)
        adapter.stubs(:get_command_class).with('test_command').returns(command_class)
        command_class.stubs(:new).with({ param: 'valid' }).returns(command_instance)
        command_instance.stubs(:validation_result).returns({ valid: true })
      end

      it 'returns successful validation' do
        result = test_class.validate_for_device(device, 'test_command', { param: 'valid' })

        expect(result).to eq({ valid: true })
      end
    end
  end
end
