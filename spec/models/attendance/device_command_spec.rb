# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Attendance::DeviceCommand, type: :model do
  let(:device) { create(:attendance_device) }
  let(:adapter) { mock('adapter') }
  let(:command_class) { mock('command_class') }
  let(:command_instance) { mock('command_instance') }

  before do
    device.stubs(:create_adapter).returns(adapter)
  end

  describe '.for_device' do
    context 'when device supports commands' do
      before do
        adapter.stubs(:supports_commands?).returns(true)
        adapter.stubs(:available_commands).returns([ 'test_command' ])
        adapter.stubs(:get_command_class).with('test_command').returns(command_class)
        command_class.stubs(:command_name).returns('Test Command')
        command_class.stubs(:description).returns('Test description')
        command_class.stubs(:attribute_names).returns([ 'param1' ])
        command_class.stubs(:attribute_types).returns({ 'param1' => mock(type: :string) })
        command_class.stubs(:required_parameters).returns([ 'param1' ])
      end

      it 'returns array of DeviceCommand instances' do
        commands = described_class.for_device(device)

        expect(commands).to be_an(Array)
        expect(commands.first).to be_a(described_class)
        expect(commands.first.name).to eq('test_command')
        expect(commands.first.display_name).to eq('Test Command')
      end
    end

    context 'when device does not support commands' do
      before do
        adapter.stubs(:supports_commands?).returns(false)
      end

      it 'returns empty array' do
        commands = described_class.for_device(device)
        expect(commands).to eq([])
      end
    end
  end

  describe '.validate_for_device' do
    context 'when device does not support commands' do
      before do
        adapter.stubs(:supports_commands?).returns(false)
      end

      it 'returns validation error' do
        result = described_class.validate_for_device(device, 'test_command', {})

        expect(result[:valid]).to be false
        expect(result[:error]).to eq("Device does not support commands")
      end
    end

    context 'when command is not supported' do
      before do
        adapter.stubs(:supports_commands?).returns(true)
        adapter.stubs(:get_command_class).with('unknown_command').returns(nil)
      end

      it 'returns validation error' do
        result = described_class.validate_for_device(device, 'unknown_command', {})

        expect(result[:valid]).to be false
        expect(result[:error]).to include("Command 'unknown_command' not supported")
      end
    end

    context 'when command is valid' do
      before do
        adapter.stubs(:supports_commands?).returns(true)
        adapter.stubs(:get_command_class).with('test_command').returns(command_class)
        command_class.stubs(:new).with({ param: 'value' }).returns(command_instance)
        command_instance.stubs(:validation_result).returns({ valid: true })
      end

      it 'returns successful validation' do
        result = described_class.validate_for_device(device, 'test_command', { param: 'value' })

        expect(result[:valid]).to be true
      end
    end
  end

  describe '.execute_for_device' do
    let(:employee) { create(:employee) }
    let(:execution) { create(:attendance_command_execution, device: device) }
    let(:command_result) { Attendance::CommandResult.success('Command executed') }

    before do
      Attendance::CommandExecution.stubs(:create!).returns(execution)
      execution.stubs(:update!)
    end

    context 'when device does not support commands' do
      before do
        adapter.stubs(:supports_commands?).returns(false)
      end

      it 'returns failure result and marks execution as failed' do
        result = described_class.execute_for_device(device, 'test_command', {}, employee)

        expect(result.success).to be false
        expect(result.error).to eq("Device does not support commands")
        execution.expects(:update!).with(
          status: :failed,
          result: result.as_json,
          completed_at: be_within(1.second).of(Time.current)
        )
      end
    end

    context 'when command executes successfully' do
      before do
        adapter.stubs(:supports_commands?).returns(true)
        adapter.stubs(:execute_command).with('test_command', {}).returns(command_result)
      end

      it 'returns success result and marks execution as completed' do
        result = described_class.execute_for_device(device, 'test_command', {}, employee)

        expect(result.success).to be true
        execution.expects(:update!).with(
          status: :completed,
          result: command_result.as_json,
          completed_at: be_within(1.second).of(Time.current)
        )
      end
    end

    context 'when command execution raises exception' do
      before do
        adapter.stubs(:supports_commands?).returns(true)
        adapter.stubs(:execute_command).raises(StandardError, 'Connection failed')
      end

      it 'returns failure result and marks execution as failed' do
        result = described_class.execute_for_device(device, 'test_command', {}, employee)

        expect(result.success).to be false
        expect(result.error).to eq('Connection failed')
        execution.expects(:update!).with(
          status: :failed,
          result: result.as_json,
          completed_at: be_within(1.second).of(Time.current)
        )
      end
    end
  end
end
