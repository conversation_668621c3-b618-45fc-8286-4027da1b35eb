require 'rails_helper'

RSpec.describe SalaryPackage, type: :model do
  let(:employee) { create(:employee) }
  let(:other_employee) { create(:employee) }

  describe "validations" do
    subject { build(:salary_package, employee: employee) }

    it { should validate_presence_of(:base_salary) }
    it { should validate_presence_of(:effective_date) }
    it { should validate_numericality_of(:base_salary).is_greater_than(0) }

    describe "date validations" do
      it "allows end_date to be nil" do
        package = build(:salary_package, employee: employee, effective_date: Date.current, end_date: nil)
        expect(package).to be_valid
      end

      it "allows end_date to be the same as effective_date" do
        package = build(:salary_package, employee: employee, effective_date: Date.current, end_date: Date.current)
        expect(package).to be_valid
      end

      it "allows end_date to be after effective_date" do
        package = build(:salary_package, employee: employee, effective_date: Date.current, end_date: Date.current + 1.month)
        expect(package).to be_valid
      end

      it "prevents end_date from being before effective_date" do
        package = build(:salary_package, employee: employee, effective_date: Date.current, end_date: Date.current - 1.day)
        expect(package).not_to be_valid
        expect(package.errors[:end_date]).to include("cannot be before effective date")
      end

      it "prevents end_date from being significantly before effective_date" do
        package = build(:salary_package, employee: employee, effective_date: Date.current, end_date: Date.current - 1.month)
        expect(package).not_to be_valid
        expect(package.errors[:end_date]).to include("cannot be before effective date")
      end
    end

    describe "self-package validation" do
      it "prevents users from creating packages for themselves" do
        package = build(:salary_package, employee: employee, actor_user_id: employee.user_id)

        expect(package).not_to be_valid
        expect(package.errors[:base]).to include("Cannot create salary packages for yourself")
      end

      it "allows users to create packages for others" do
        package = build(:salary_package, employee: employee, actor_user_id: other_employee.user_id)

        expect(package).to be_valid
      end
    end

    describe "creator-specific draft package validation" do
      let(:hr_user1) { create(:employee) }
      let(:hr_user2) { create(:employee) }

      it "allows only one draft package per employee per creator" do
        create(:salary_package, employee: employee, status: :draft, created_by: hr_user1)
        duplicate_draft = build(:salary_package, employee: employee, status: :draft, created_by: hr_user1)

        expect(duplicate_draft).not_to be_valid
        expect(duplicate_draft.errors[:base]).to include("You can only have one draft salary package per employee at a time")
      end

      it "allows multiple creators to have drafts for same employee" do
        create(:salary_package, employee: employee, status: :draft, created_by: hr_user1)
        second_draft = build(:salary_package, employee: employee, status: :draft, created_by: hr_user2)

        expect(second_draft).to be_valid
      end

      it "allows multiple non-draft packages per employee" do
        create(:salary_package, employee: employee, status: :approved, created_by: hr_user1, effective_date: Date.current)
        second_package = build(:salary_package, employee: employee, status: :approved, created_by: hr_user2, effective_date: Date.current + 1.month)

        expect(second_package).to be_valid
      end

      it "prevents new drafts when package is pending approval" do
        create(:salary_package, employee: employee, status: :pending_approval, created_by: hr_user1)
        new_draft = build(:salary_package, employee: employee, status: :draft, created_by: hr_user2)

        expect(new_draft).not_to be_valid
        expect(new_draft.errors[:employee_id]).to include("already has a package pending approval")
      end
    end
  end

  describe "associations" do
    it { should belong_to(:employee) }
    it { should have_one(:approval_request) }
    it { should belong_to(:created_by).class_name('Employee') }
  end

  describe "enums" do
    it { should define_enum_for(:status).with_values(draft: 0, pending_approval: 1, approved: 2, rejected: 3, cancelled: 4) }
  end

  describe "scopes" do
    let!(:test_employee) { create(:employee) }
    let!(:test_creator) { create(:employee) }

    let!(:draft_package) { create(:salary_package, :draft, employee: test_employee, created_by: test_creator) }
    let!(:pending_package) { create(:salary_package, :pending_approval, employee: test_employee, created_by: test_creator) }
    let!(:approved_package) { create(:salary_package, :approved, employee: test_employee, created_by: test_creator) }
    let!(:rejected_package) { create(:salary_package, :rejected, employee: test_employee, created_by: test_creator) }

    describe ".approved" do
      it "returns only approved packages" do
        expect(SalaryPackage.approved).to contain_exactly(approved_package)
      end
    end

    describe ".visible_to_employee" do
      it "returns only approved packages (excludes drafts and pending)" do
        visible_packages = SalaryPackage.visible_to_employee
        expect(visible_packages).to contain_exactly(approved_package)
        expect(visible_packages).not_to include(draft_package, pending_package, rejected_package)
      end
    end

    describe "creator-specific scopes" do
      let(:creator1) { create(:employee) }
      let(:creator2) { create(:employee) }
      let!(:creator1_draft) { create(:salary_package, employee: employee, created_by: creator1, status: :draft) }
      let!(:creator2_draft) { create(:salary_package, employee: employee, created_by: creator2, status: :draft) }
      let!(:cancelled_package) { create(:salary_package, employee: employee, created_by: creator1, status: :cancelled, cancellation_reason: "Superseded by package #123") }

      describe ".created_by_employee" do
        it "returns packages created by specific employee" do
          creator1_packages = SalaryPackage.created_by_employee(creator1.id)
          expect(creator1_packages).to include(creator1_draft, cancelled_package)
          expect(creator1_packages).not_to include(creator2_draft)
        end
      end

      describe ".draft_by_creator" do
        it "returns only draft packages by specific creator" do
          creator1_drafts = SalaryPackage.draft_by_creator(creator1.id)
          expect(creator1_drafts).to contain_exactly(creator1_draft)
          expect(creator1_drafts).not_to include(creator2_draft, cancelled_package)
        end
      end

      describe ".cancelled" do
        it "returns only cancelled packages" do
          cancelled_packages = SalaryPackage.cancelled
          expect(cancelled_packages).to contain_exactly(cancelled_package)
        end
      end

      describe ".auto_cancelled" do
        it "returns packages cancelled due to superseding" do
          auto_cancelled = SalaryPackage.auto_cancelled
          expect(auto_cancelled).to contain_exactly(cancelled_package)
        end
      end

      describe ".manually_cancelled" do
        let!(:manual_cancelled) { create(:salary_package, employee: employee, created_by: creator2, status: :cancelled, cancellation_reason: "Manually cancelled by user") }

        it "returns packages manually cancelled by users" do
          manual_cancelled_packages = SalaryPackage.manually_cancelled
          expect(manual_cancelled_packages).to contain_exactly(manual_cancelled)
          expect(manual_cancelled_packages).not_to include(cancelled_package)
        end
      end
    end
  end

  describe "business logic methods" do
    describe "#active?" do
      it "returns true for approved packages with current effective date" do
        package = create(:salary_package,
                         employee: employee,
                         status: :approved,
                         effective_date: Date.current - 1.month
        )

        expect(package.active?).to be true
      end

      it "returns false for non-approved packages" do
        package = create(:salary_package, employee: employee, status: :draft)

        expect(package.active?).to be false
      end
    end

    describe "#editable?" do
      it "returns true for draft packages" do
        package = create(:salary_package, employee: employee, status: :draft)

        expect(package.editable?).to be true
      end

      it "returns false for non-draft packages" do
        package = create(:salary_package, employee: employee, status: :approved)

        expect(package.editable?).to be false
      end
    end

    describe "#submittable?" do
      it "returns true for draft packages" do
        package = create(:salary_package, employee: employee, status: :draft)

        expect(package.submittable?).to be true
      end

      it "returns false for non-draft packages" do
        package = create(:salary_package, employee: employee, status: :pending_approval)

        expect(package.submittable?).to be false
      end
    end

    describe "#total_package_value" do
      it "calculates total package value correctly" do
        package = build(:salary_package,
                        base_salary: 5000,
                        transportation_allowance: 500,
                        other_allowances: 300
        )

        expect(package.total_package_value).to eq(5800)
      end

      it "handles nil allowances" do
        package = build(:salary_package,
                        base_salary: 5000,
                        transportation_allowance: nil,
                        other_allowances: nil
        )

        expect(package.total_package_value).to eq(5000)
      end
    end

    describe "uniqueness validations" do
      let(:creator) { create(:employee) }
      let(:other_creator) { create(:employee) }

      it "allows multiple draft packages for same employee by different creators" do
        create(:salary_package, :draft, employee: employee, created_by: creator)
        second_draft = build(:salary_package, :draft, employee: employee, created_by: other_creator)

        expect(second_draft).to be_valid
      end

      it "prevents multiple pending packages for same employee" do
        create(:salary_package, :pending_approval, employee: employee, created_by: creator)
        duplicate_pending = build(:salary_package, :pending_approval, employee: employee, created_by: other_creator)

        expect(duplicate_pending).not_to be_valid
        expect(duplicate_pending.errors[:employee_id]).to include("already has a package pending approval")
      end

      it "prevents creating draft when package is pending approval" do
        create(:salary_package, :pending_approval, employee: employee, created_by: creator)
        new_draft = build(:salary_package, :draft, employee: employee, created_by: other_creator)

        expect(new_draft).not_to be_valid
        expect(new_draft.errors[:employee_id]).to include("already has a package pending approval")
      end

      it "allows creating draft after pending package is approved/rejected" do
        pending = create(:salary_package, :pending_approval, employee: employee, created_by: creator)
        pending.update!(status: :approved)

        new_draft = build(:salary_package, :draft, employee: employee, created_by: other_creator)
        expect(new_draft).to be_valid
      end
    end

    describe "creator-specific methods" do
      let(:creator) { create(:employee) }
      let(:other_user) { create(:employee) }
      let(:admin_user) { create(:employee) }
      let(:package) { create(:salary_package, employee: employee, created_by: creator, status: :draft) }

      describe "#editable_by?" do
        it "returns true for creator" do
          expect(package.editable_by?(creator)).to be true
        end

        it "returns false for other users" do
          expect(package.editable_by?(other_user)).to be false
        end

        it "returns false for non-draft packages" do
          package.update!(status: :approved)
          expect(package.editable_by?(creator)).to be false
        end
      end

      describe "#visible_to?" do
        it "returns true for creator of draft package" do
          expect(package.visible_to?(creator)).to be true
        end

        it "returns false for other users viewing draft package" do
          expect(package.visible_to?(other_user)).to be false
        end

        it "returns true for approved packages regardless of creator" do
          package.update!(status: :approved)
          expect(package.visible_to?(creator)).to be true
          expect(package.visible_to?(other_user)).to be true
        end

        it "returns true for pending packages to users with approval permissions" do
          package.update!(status: :pending_approval)
          expect(package.visible_to?(admin_user, { can_approve_salary_packages: true })).to be true
          expect(package.visible_to?(other_user, { can_approve_salary_packages: false })).to be false
        end

        it "returns true for admin users with read_all permission" do
          expect(package.visible_to?(admin_user, { read_all: true })).to be true
        end

        it "returns true for cancelled packages to creator only" do
          package.update!(status: :cancelled, cancelled_at: Time.current, cancellation_reason: "Test")
          expect(package.visible_to?(creator)).to be true
          expect(package.visible_to?(other_user)).to be false
        end
      end

      describe "#cancellable_by?" do
        it "returns true for creator of draft package" do
          expect(package.cancellable_by?(creator)).to be true
        end

        it "returns false for other users" do
          expect(package.cancellable_by?(other_user)).to be false
        end

        it "returns false for non-draft packages" do
          package.update!(status: :pending_approval)
          expect(package.cancellable_by?(creator)).to be false
        end
      end

      describe "#cancel_draft!" do
        it "cancels draft package with reason" do
          expect(package.cancel_draft!("No longer needed")).to be true

          package.reload
          expect(package.status).to eq("cancelled")
          expect(package.cancelled_at).to be_present
          expect(package.cancellation_reason).to include("No longer needed")
        end

        it "fails to cancel non-draft package" do
          package.update!(status: :approved)
          expect(package.cancel_draft!).to be false
        end
      end
    end
  end

  describe "approval workflow" do
    let(:package) { create(:salary_package, employee: employee, status: :draft) }

    describe "#submit!" do
      it "changes status to pending_approval" do
        package.actor_user_id = other_employee.user_id

        expect { package.submit! }.to change { package.status }.from("draft").to("pending_approval")
      end

      it "sets submitted_at timestamp" do
        package.actor_user_id = other_employee.user_id

        expect { package.submit! }.to change { package.submitted_at }.from(nil)
        expect(package.submitted_at).to be_within(1.second).of(Time.current)
      end

      it "fails if package is not submittable" do
        package.update!(status: :approved)

        expect { package.submit! }.to raise_error(ActiveRecord::RecordInvalid)
      end
    end

    describe "#approval_context" do
      it "generates correct approval context" do
        package.actor_user_id = other_employee.user_id
        context = package.approval_context

        expect(context).to include(
                             employee_name: employee.name,
                             base_salary: package.base_salary.to_s,
                             total_package: package.total_package_value.to_s,
                             creator_user_id: other_employee.user_id,
                             target_user_id: employee.user_id
                           )
      end

      it "includes effective date in context" do
        context = package.approval_context

        expect(context[:effective_date]).to eq(package.effective_date.to_s)
      end
    end

    describe "auto-cancellation on submission" do
      let(:hr_user1) { create(:employee) }
      let(:hr_user2) { create(:employee) }
      let(:finance_user) { create(:employee) }

      it "auto-cancels competing drafts when package is submitted" do
        draft1 = create(:salary_package, employee: employee, created_by: hr_user1, status: :draft)
        draft2 = create(:salary_package, employee: employee, created_by: hr_user2, status: :draft)
        draft3 = create(:salary_package, employee: employee, created_by: finance_user, status: :draft)

        # HR User 1 submits first
        draft1.actor_user_id = hr_user1.user_id
        draft1.submit!

        # Check results
        expect(draft1.reload.status).to eq("pending_approval")
        expect(draft2.reload.status).to eq("cancelled")
        expect(draft3.reload.status).to eq("cancelled")

        # Check cancellation reasons include submitter info
        expect(draft2.cancellation_reason).to include("Superseded by package ##{draft1.id}")
        expect(draft3.cancellation_reason).to include("Superseded by package ##{draft1.id}")
      end

      it "does not cancel drafts for different employees" do
        draft1 = create(:salary_package, employee: employee, created_by: hr_user1, status: :draft)
        draft2 = create(:salary_package, employee: other_employee, created_by: hr_user2, status: :draft)

        # Submit draft1
        draft1.actor_user_id = hr_user1.user_id
        draft1.submit!

        # draft2 should not be affected (different employee)
        expect(draft1.reload.status).to eq("pending_approval")
        expect(draft2.reload.status).to eq("draft")
      end
    end
  end
end
