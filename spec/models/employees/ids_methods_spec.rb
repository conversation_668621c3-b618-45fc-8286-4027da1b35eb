# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'ActiveStruct IDs Methods' do
  describe 'collection ids methods' do
    it 'provides an ids method for collections' do
      collection = AtharAuth::Models::UserRoleCollection.new
      collection << { id: '1', role: { name: 'Admin' } }
      collection << { id: '2', role: { name: 'Manager' } }

      expect(collection.ids).to eq([ '1', '2' ])
    end

    it 'provides a user_role_ids method for collections' do
      collection = AtharAuth::Models::UserRoleCollection.new
      collection << { id: '1', role: { name: 'Admin' } }
      collection << { id: '2', role: { name: 'Manager' } }

      expect(collection.user_role_ids).to eq([ '1', '2' ])
    end

    it 'allows setting ids for collections' do
      collection = AtharAuth::Models::UserRoleCollection.new
      collection.ids = [ '3', '4' ]

      expect(collection.size).to eq(2)
      expect(collection.ids).to eq([ '3', '4' ])
    end

    it 'allows setting user_role_ids for collections' do
      collection = AtharAuth::Models::UserRoleCollection.new
      collection.user_role_ids = [ '5', '6' ]

      expect(collection.size).to eq(2)
      expect(collection.user_role_ids).to eq([ '5', '6' ])
    end

    it 'handles nil and empty values when setting ids' do
      collection = AtharAuth::Models::UserRoleCollection.new
      collection << { id: '1', role: { name: 'Admin' } }

      collection.ids = nil
      expect(collection.size).to eq(0)

      collection << { id: '1', role: { name: 'Admin' } }
      collection.ids = []
      expect(collection.size).to eq(0)
    end

    it 'ignores nil and blank values in ids array' do
      collection = AtharAuth::Models::UserRoleCollection.new
      collection.ids = [ '1', nil, '', '2' ]

      expect(collection.size).to eq(2)
      expect(collection.ids).to eq([ '1', '2' ])
    end
  end

  describe 'has_many association with ids methods' do
    # Define a test class with has_many association
    before do
      # Define a test role class that accepts test_model_id
      module Employees
        class TestRole < Athar::Commons::ActiveStruct::Base
          attribute :name, :string
          attribute :test_model_id, :string
        end

        class TestUserRoleCollection
          include Athar::Commons::ActiveStruct::Collection
          collection_item_class TestRole
        end

        class TestModel < Athar::Commons::ActiveStruct::Base
          # Define a custom method to get the collection
          def user_roles
            @user_roles ||= TestUserRoleCollection.new(id, 'test_model')
          end

          # Define the ids methods manually
          def user_role_ids
            user_roles.ids
          end

          def user_role_ids=(ids)
            user_roles.ids = ids
          end
        end
      end
    end

    after do
      Employees.send(:remove_const, :TestModel) if Employees.const_defined?(:TestModel)
      Employees.send(:remove_const, :TestUserRoleCollection) if Employees.const_defined?(:TestUserRoleCollection)
      Employees.send(:remove_const, :TestRole) if Employees.const_defined?(:TestRole)
    end

    it 'provides a user_role_ids method' do
      model = Employees::TestModel.new

      # Add some user roles
      model.user_roles << { id: '1', name: 'Admin' }
      model.user_roles << { id: '2', name: 'Manager' }

      expect(model.user_role_ids).to eq([ '1', '2' ])
    end

    it 'allows setting user_role_ids' do
      model = Employees::TestModel.new
      model.user_role_ids = [ '3', '4' ]

      expect(model.user_roles.size).to eq(2)
      expect(model.user_role_ids).to eq([ '3', '4' ])
    end
  end

  describe 'has_one association with id method' do
    # Define a test class with has_one association
    before do
      module Employees
        class SingleRole < Athar::Commons::ActiveStruct::Base
          attribute :name, :string
        end

        class TestModelWithOne < Athar::Commons::ActiveStruct::Base
          # Define a custom method to get/set the association
          def single_role
            @single_role
          end

          def single_role=(value)
            @single_role = value
          end

          # Define the id methods manually
          def single_role_id
            single_role&.id
          end

          def single_role_id=(id)
            if id.present?
              self.single_role = Employees::SingleRole.new(id: id)
            else
              self.single_role = nil
            end
          end
        end
      end
    end

    after do
      Employees.send(:remove_const, :SingleRole) if Employees.const_defined?(:SingleRole)
      Employees.send(:remove_const, :TestModelWithOne) if Employees.const_defined?(:TestModelWithOne)
    end

    it 'provides a single_role_id method' do
      model = Employees::TestModelWithOne.new
      model.single_role = Employees::SingleRole.new(id: '1', name: 'Admin')

      expect(model.single_role_id).to eq('1')
    end

    it 'allows setting single_role_id' do
      model = Employees::TestModelWithOne.new
      model.single_role_id = '2'

      expect(model.single_role).to be_a(Employees::SingleRole)
      expect(model.single_role.id).to eq('2')
    end
  end
end
