# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AtharAuth::Models::UserRole do
  describe 'initialization' do
    it 'can be initialized with basic attributes' do
      role = AtharAuth::Models::Role.new(name: 'Admin', scope: 'global_role')
      project = AtharAuth::Models::Project.new(id: 1, name: 'Test Project')

      user_role = AtharAuth::Models::UserRole.new(
        role: role,
        project: project,
        is_default: true
      )

      expect(user_role.role.name).to eq('Admin')
      expect(user_role.project.id).to eq(1)
      expect(user_role.is_default).to eq(true)
    end

    it 'can be initialized with associations' do
      role = AtharAuth::Models::Role.new(name: 'Admin', scope: 'global_role')
      project = AtharAuth::Models::Project.new(id: 1, name: 'Project X', description: 'A test project')

      user_role = AtharAuth::Models::UserRole.new(
        role: role,
        project: project,
        is_default: true
      )

      expect(user_role.role).to eq(role)
      expect(user_role.project).to eq(project)
      expect(user_role.is_default).to eq(true)
    end

    it 'provides convenience methods' do
      role = AtharAuth::Models::Role.new(name: 'Admin', scope: 'global_role')
      project = AtharAuth::Models::Project.new(id: 1, name: 'Test Project')

      user_role = AtharAuth::Models::UserRole.new(
        role: role,
        project: project,
        is_default: true
      )

      expect(user_role.default?).to eq(true)
      expect(user_role.is_default).to eq(true)
    end
  end

  describe '#as_json' do
    it 'returns a hash with basic attributes' do
      role = AtharAuth::Models::Role.new(name: 'Admin', scope: 'global_role')
      project = AtharAuth::Models::Project.new(id: 1, name: 'Test Project')

      user_role = AtharAuth::Models::UserRole.new(
        role: role,
        project: project,
        is_default: true
      )

      json = user_role.as_json

      expect(json['role']['name']).to eq('Admin')
      expect(json['project']['id']).to eq(1)
      expect(json['is_default']).to eq(true)
    end
  end

  describe '#global?' do
    it 'returns true when role scope is global' do
      role = AtharAuth::Models::Role.new(name: 'Admin', scope: 'global_role')
      user_role = AtharAuth::Models::UserRole.new(role: role)

      expect(user_role.global?).to be true
    end

    it 'returns false when role scope is project-based' do
      role = AtharAuth::Models::Role.new(name: 'Project Manager', scope: 'project_based')
      user_role = AtharAuth::Models::UserRole.new(role: role)

      expect(user_role.global?).to be false
    end
  end
end
