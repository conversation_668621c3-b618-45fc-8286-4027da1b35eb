# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AtharAuth::Models::UserRole do
  describe 'associations' do
    it 'can be associated with a project using an object' do
      project = AtharAuth::Models::Project.new(id: 1, name: 'Test Project', description: 'A test project')
      user_role = AtharAuth::Models::UserRole.new(project: project)

      expect(user_role.project).to eq(project)
      expect(user_role.project.name).to eq('Test Project')
    end

    it 'can be associated with a role using an object' do
      role = AtharAuth::Models::Role.new(name: 'Developer', scope: 'project_based')
      user_role = AtharAuth::Models::UserRole.new(role: role)

      expect(user_role.role).to eq(role)
      expect(user_role.role.name).to eq('Developer')
    end

    it 'can be initialized with basic attributes' do
      role = AtharAuth::Models::Role.new(name: 'Developer', scope: 'project_based')
      project = AtharAuth::Models::Project.new(id: 1, name: 'Test Project')

      user_role = AtharAuth::Models::UserRole.new(
        role: role,
        project: project,
        is_default: false
      )

      expect(user_role.role.name).to eq('Developer')
      expect(user_role.project.id).to eq(1)
      expect(user_role.is_default).to eq(false)
    end

    it 'provides convenience methods' do
      role = AtharAuth::Models::Role.new(name: 'Admin', scope: 'global_role')
      project = AtharAuth::Models::Project.new(id: 1, name: 'Test Project')

      user_role = AtharAuth::Models::UserRole.new(
        role: role,
        project: project,
        is_default: true
      )

      expect(user_role.default?).to eq(true)
    end

    it 'serializes basic attributes in as_json' do
      role = AtharAuth::Models::Role.new(name: 'Developer', scope: 'project_based')
      project = AtharAuth::Models::Project.new(id: 1, name: 'Test Project')

      user_role = AtharAuth::Models::UserRole.new(
        role: role,
        project: project,
        is_default: true
      )

      json = user_role.as_json

      expect(json['role']['name']).to eq('Developer')
      expect(json['project']['id']).to eq(1)
      expect(json['is_default']).to eq(true)
    end
  end
end
