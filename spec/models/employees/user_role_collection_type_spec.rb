# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AtharAuth::Models::Types::UserRoleCollectionType, type: :model do
  it 'properly tracks changes in the Employee model' do
    # Create an employee
    employee = Employee.new(
      name: 'Test Employee',
      email: '<EMAIL>',
      phone: '+962790000000',
      start_date: Date.today
    )

    # Save to reset changes
    employee.save(validate: false)
    employee.clear_changes_information

    # Verify no changes initially
    expect(employee.changed?).to be_falsey
    expect(employee.changed_attributes).to be_empty

    # Modify user_roles_list
    employee.user_roles_list = [ { role: { name: 'Admin', global: true } } ]

    # Verify changes are tracked
    expect(employee.changed?).to be_truthy
    expect(employee.changed).to include('user_roles_list')
    expect(employee.changed_attributes).to have_key('user_roles_list')

    # This is the key test - verify that changed_attributes contains the user_roles_list
    expect(employee.changed_attributes.keys).to include('user_roles_list')
  end

  it 'tracks changes when modifying the collection' do
    # Create an employee
    employee = Employee.new(
      name: 'Test Employee',
      email: '<EMAIL>',
      phone: '+962790000000',
      start_date: Date.today,
      user_roles_list: [ { role: { name: 'Admin', global: true } } ]
    )

    # Save to reset changes
    employee.save(validate: false)
    employee.clear_changes_information

    # Verify no changes initially
    expect(employee.changed?).to be_falsey
    expect(employee.changed_attributes).to be_empty

    # Add a role to the collection
    employee.user_roles_list << { role: { name: 'Editor', global: false } }

    # Verify changes are tracked
    expect(employee.changed?).to be_truthy
    expect(employee.changed).to include('user_roles_list')
    expect(employee.changed_attributes).to have_key('user_roles_list')

    # This is the key test - verify that changed_attributes contains the user_roles_list
    expect(employee.changed_attributes.keys).to include('user_roles_list')
  end

  it 'tracks changes when clearing the collection' do
    # Create an employee
    employee = Employee.new(
      name: 'Test Employee',
      email: '<EMAIL>',
      phone: '+962790000000',
      start_date: Date.today,
      user_roles_list: [ { role: { name: 'Admin', global: true } } ]
    )

    # Save to reset changes
    employee.save(validate: false)
    employee.clear_changes_information

    # Verify no changes initially
    expect(employee.changed?).to be_falsey
    expect(employee.changed_attributes).to be_empty

    # Clear the collection
    employee.user_roles_list.clear

    # Verify changes are tracked
    expect(employee.changed?).to be_truthy
    expect(employee.changed).to include('user_roles_list')
    expect(employee.changed_attributes).to have_key('user_roles_list')

    # This is the key test - verify that changed_attributes contains the user_roles_list
    expect(employee.changed_attributes.keys).to include('user_roles_list')
  end
end
