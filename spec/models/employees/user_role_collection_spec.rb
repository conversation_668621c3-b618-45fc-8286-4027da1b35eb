# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AtharAuth::Models::UserRoleCollection do
  let(:admin_role) do
    global_role = AtharAuth::Models::Role.new(name: 'Admin', scope: 'global_role')
    AtharAuth::Models::UserRole.new(
      role: global_role,
      project: nil,
      is_default: true
    )
  end

  let(:project_role) do
    project_based_role = AtharAuth::Models::Role.new(name: 'Project Manager', scope: 'project_based')
    project = AtharAuth::Models::Project.new(id: 1, name: 'Test Project')
    AtharAuth::Models::UserRole.new(
      role: project_based_role,
      project: project,
      is_default: false
    )
  end

  describe 'initialization' do
    it 'can be initialized with an array of roles' do
      collection = AtharAuth::Models::UserRoleCollection.new(nil, nil, [ admin_role, project_role ])

      expect(collection.size).to eq(2)
      expect(collection.first.role.name).to eq('Admin')
      expect(collection.last.role.name).to eq('Project Manager')
    end

    it 'can be initialized with owner information' do
      collection = AtharAuth::Models::UserRoleCollection.new(1, :employee)

      expect(collection.owner_id).to eq(1)
      expect(collection.owner_type).to eq(:employee)
    end
  end

  describe '#<<' do
    it 'adds a role to the collection' do
      collection = AtharAuth::Models::UserRoleCollection.new
      collection << admin_role

      expect(collection.size).to eq(1)
      expect(collection.first).to eq(admin_role)
    end

    it 'converts a hash to a role' do
      collection = AtharAuth::Models::UserRoleCollection.new
      collection << admin_role

      expect(collection.size).to eq(1)
      expect(collection.first.role.name).to eq('Admin')
      expect(collection.first.default?).to eq(true)
    end

    it 'handles an array input' do
      collection = AtharAuth::Models::UserRoleCollection.new
      [ admin_role ].each { |role| collection << role }

      expect(collection.size).to eq(1)
      expect(collection.first.role.name).to eq('Admin')
      expect(collection.first.default?).to eq(true)
    end
  end

  describe 'filtering methods' do
    let(:collection) do
      collection = AtharAuth::Models::UserRoleCollection.new
      collection << admin_role
      collection << project_role
      collection
    end

    describe '#global_roles' do
      it 'returns only global roles' do
        global_roles = collection.global_roles

        expect(global_roles.size).to eq(1)
        expect(global_roles.first.role.name).to eq('Admin')
      end
    end

    describe '#project_roles' do
      it 'returns only project roles (alias for project_based_roles)' do
        project_roles = collection.project_roles

        expect(project_roles.size).to eq(1)
        expect(project_roles.first.role.name).to eq('Project Manager')
      end
    end

    describe '#default_role' do
      it 'returns the default role' do
        default_role = collection.default_role

        expect(default_role).to eq(admin_role)
      end
    end

    describe '#find_by_role_id' do
      it 'returns nil for backward compatibility' do
        # This method exists for backward compatibility but returns nil
        role = collection.find_by_role_id('some_id')

        expect(role).to be_nil
      end
    end

    describe '#find_by_project_id' do
      it 'finds roles by project ID' do
        roles = collection.find_by_project_id(1)

        expect(roles.size).to eq(1)
        expect(roles.first).to eq(project_role)
      end
    end
  end

  describe '#as_json' do
    it 'returns an array of role hashes' do
      collection = AtharAuth::Models::UserRoleCollection.new
      collection << admin_role
      collection << project_role

      json = collection.as_json

      expect(json.size).to eq(2)
      expect(json.first['role']['name']).to eq('Admin')
      expect(json.last['role']['name']).to eq('Project Manager')
    end
  end
end
