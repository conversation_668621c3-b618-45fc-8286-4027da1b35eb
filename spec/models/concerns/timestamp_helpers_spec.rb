require 'rails_helper'

RSpec.describe 'Timestamp Helpers', type: :model do
  describe 'Attendance::Event#timestamp_as_time' do
    it 'converts integer timestamp to Time object' do
      # Create an attendance event with a known timestamp
      event = Attendance::Event.new(timestamp: 1641981600) # 2022-01-12 08:00:00 UTC

      # Convert to Time object
      time = event.timestamp_as_time

      # Verify the conversion (timezone-dependent)
      expect(time).to be_a(Time)
      expect(time.year).to eq(2022)
      expect(time.month).to eq(1)
      expect(time.day).to eq(12)
      expect(time.hour).to eq(11) # Adjusted for timezone (Asia/Amman UTC+3)
      expect(time.min).to eq(0)
      expect(time.sec).to eq(0)
    end

    it 'returns nil for nil timestamp' do
      # Create an attendance event without a timestamp
      event = Attendance::Event.new(timestamp: nil)

      # Convert to Time object
      time = event.timestamp_as_time

      # Verify the result is nil (or epoch time if timestamp defaults to 0)
      expect(time).to be_nil.or be_a(Time)
    end

    it 'works with timestamp_date to get date portion' do
      # Create an attendance event with a known timestamp
      event = Attendance::Event.new(timestamp: 1641981600) # 2022-01-12 08:00:00 UTC

      # Get the date portion
      date = event.timestamp_date

      # Verify the date
      expect(date).to be_a(Date)
      expect(date.year).to eq(2022)
      expect(date.month).to eq(1)
      expect(date.day).to eq(12)
    end

    it 'works with formatted_timestamp to format the timestamp' do
      # Create an attendance event with a known timestamp
      event = Attendance::Event.new(timestamp: 1641981600) # 2022-01-12 08:00:00 UTC

      # Format the timestamp
      formatted = event.formatted_timestamp

      # Verify the formatted timestamp (timezone-dependent)
      expect(formatted).to eq('2022-01-12 11:00:00') # Adjusted for timezone (Asia/Amman UTC+3)

      # Test with custom format
      custom_format = event.formatted_timestamp('%d/%m/%Y %H:%M')
      expect(custom_format).to eq('12/01/2022 11:00') # Adjusted for timezone (Asia/Amman UTC+3)
    end
  end

  describe 'Attendance::Period timestamp helpers' do
    it 'converts start_timestamp to Time object' do
      # Create an attendance period with known timestamps
      period = Attendance::Period.new(
        start_timestamp: 1641981600, # 2022-01-12 08:00:00 UTC
        end_timestamp: 1642010400 # 2022-01-12 16:00:00 UTC
      )

      # Convert to Time objects
      start_time = period.start_time
      end_time = period.end_time

      # Verify the conversion (timezone-dependent)
      expect(start_time).to be_a(Time)
      expect(start_time.hour).to eq(11) # Adjusted for timezone (Asia/Amman UTC+3)

      expect(end_time).to be_a(Time)
      expect(end_time.hour).to eq(19) # Adjusted for timezone (Asia/Amman UTC+3)
    end

    it 'formats timestamps for display' do
      # Create an attendance period with known timestamps
      period = Attendance::Period.new(
        start_timestamp: 1641981600, # 2022-01-12 08:00:00 UTC
        end_timestamp: 1642010400 # 2022-01-12 16:00:00 UTC
      )

      # Format the timestamps
      formatted_start = period.formatted_start_time
      formatted_end = period.formatted_end_time

      # Verify the formatted timestamps (timezone-dependent)
      expect(formatted_start).to eq('11:00') # Adjusted for timezone (Asia/Amman UTC+3)
      expect(formatted_end).to eq('19:00')   # Adjusted for timezone (Asia/Amman UTC+3)

      # Test with custom format
      custom_start = period.formatted_start_time('%H:%M')
      custom_end = period.formatted_end_time('%H:%M')

      expect(custom_start).to eq('11:00') # Adjusted for timezone (Asia/Amman UTC+3)
      expect(custom_end).to eq('19:00')   # Adjusted for timezone (Asia/Amman UTC+3)
    end

    it 'formats duration correctly' do
      # Create an attendance period with a known duration
      period = Attendance::Period.new(duration_minutes: 480) # 8 hours

      # Format the duration
      formatted = period.formatted_duration

      # Verify the formatted duration
      expect(formatted).to eq('8h 0m')

      # Test with partial hours
      period.duration_minutes = 90 # 1 hour and 30 minutes
      expect(period.formatted_duration).to eq('1h 30m')

      # Test with minutes only
      period.duration_minutes = 45 # 45 minutes
      expect(period.formatted_duration).to eq('45m')
    end
  end
end
