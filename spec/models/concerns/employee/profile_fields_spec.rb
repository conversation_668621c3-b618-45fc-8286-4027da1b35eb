require 'rails_helper'

RSpec.describe Employee::ProfileFields, type: :concern do
  let(:employee) { create(:employee, department: 'engineering') }

  describe '#employee_id' do
    it 'formats the id with leading zeros' do
      employee.update!(id: 123)
      expect(employee.employee_id).to eq('000123')
    end

    it 'handles single digit ids' do
      employee.update!(id: 5)
      expect(employee.employee_id).to eq('000005')
    end
  end

  describe '#job_title' do
    context 'with engineering department' do
      it 'returns Software Engineer' do
        employee.update!(department: 'engineering')
        expect(employee.job_title).to eq('Software Engineer')
      end
    end

    context 'with development department' do
      it 'returns Software Engineer' do
        employee.update!(department: 'development')
        expect(employee.job_title).to eq('Software Engineer')
      end
    end

    context 'with hr department' do
      it 'returns HR Specialist' do
        employee.update!(department: 'hr')
        expect(employee.job_title).to eq('HR Specialist')
      end
    end

    context 'with finance department' do
      it 'returns Financial Analyst' do
        employee.update!(department: 'finance')
        expect(employee.job_title).to eq('Financial Analyst')
      end
    end

    context 'with marketing department' do
      it 'returns Marketing Specialist' do
        employee.update!(department: 'marketing')
        expect(employee.job_title).to eq('Marketing Specialist')
      end
    end

    context 'with sales department' do
      it 'returns Sales Representative' do
        employee.update!(department: 'sales')
        expect(employee.job_title).to eq('Sales Representative')
      end
    end

    context 'with unknown department' do
      it 'returns Employee as default' do
        employee.update!(department: 'unknown')
        expect(employee.job_title).to eq('Employee')
      end
    end

    context 'with nil department' do
      it 'returns Employee as default' do
        employee.update!(department: nil)
        expect(employee.job_title).to eq('Employee')
      end
    end
  end

  describe '#position' do
    it 'returns the same as job_title' do
      expect(employee.position).to eq(employee.job_title)
    end
  end

  describe '#address' do
    it 'returns nil as addresses are not available in the system' do
      expect(employee.address).to be_nil
    end
  end

  describe '#staff_function_code' do
    context 'with engineering department' do
      it 'returns ENG' do
        employee.update!(department: 'engineering')
        expect(employee.staff_function_code).to eq('ENG')
      end
    end

    context 'with hr department' do
      it 'returns HR' do
        employee.update!(department: 'hr')
        expect(employee.staff_function_code).to eq('HR')
      end
    end

    context 'with finance department' do
      it 'returns FIN' do
        employee.update!(department: 'finance')
        expect(employee.staff_function_code).to eq('FIN')
      end
    end

    context 'with marketing department' do
      it 'returns MKT' do
        employee.update!(department: 'marketing')
        expect(employee.staff_function_code).to eq('MKT')
      end
    end

    context 'with sales department' do
      it 'returns SAL' do
        employee.update!(department: 'sales')
        expect(employee.staff_function_code).to eq('SAL')
      end
    end

    context 'with unknown department' do
      it 'returns 000 as default' do
        employee.update!(department: 'unknown')
        expect(employee.staff_function_code).to eq('000')
      end
    end
  end

  describe '#workplace_code' do
    it 'returns default workplace code' do
      expect(employee.workplace_code).to eq('HQ001')
    end
  end

  describe '#contract_end_date' do
    it 'returns nil by default' do
      expect(employee.contract_end_date).to be_nil
    end
  end

  describe '#profile_summary' do
    it 'returns a hash with all profile information' do
      summary = employee.profile_summary

      expect(summary).to include(
        :employee_id,
        :name,
        :email,
        :phone,
        :job_title,
        :department,
        :staff_function_code,
        :workplace_code,
        :start_date,
        :contract_end_date,
        :status
      )
    end
  end

  describe '#contact_display_lines' do
    before do
      employee.stubs(:phone).returns('+962123456789')
      employee.stubs(:email).returns('<EMAIL>')
    end

    it 'returns contact information lines without address' do
      lines = employee.contact_display_lines

      expect(lines).to include('+962123456789 | <EMAIL>')
      expect(lines).to include(Date.current.strftime('%d/%m/%Y'))
      expect(lines.length).to eq(2) # Contact line + date line only
    end

    context 'when only phone is present' do
      before do
        employee.stubs(:phone).returns('+962123456789')
        employee.stubs(:email).returns(nil)
      end

      it 'includes only phone and date' do
        lines = employee.contact_display_lines
        expect(lines).to include('+962123456789')
        expect(lines).to include(Date.current.strftime('%d/%m/%Y'))
        expect(lines.length).to eq(2)
      end
    end

    context 'when only email is present' do
      before do
        employee.stubs(:phone).returns(nil)
        employee.stubs(:email).returns('<EMAIL>')
      end

      it 'includes only email and date' do
        lines = employee.contact_display_lines
        expect(lines).to include('<EMAIL>')
        expect(lines).to include(Date.current.strftime('%d/%m/%Y'))
        expect(lines.length).to eq(2)
      end
    end
  end
end
