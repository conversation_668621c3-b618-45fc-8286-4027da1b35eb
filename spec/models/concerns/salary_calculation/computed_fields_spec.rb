require 'rails_helper'

RSpec.describe SalaryCalculation::ComputedFields, type: :concern do
  let(:employee) { create(:employee) }
  let(:salary_package) { create(:salary_package, :approved, employee: employee, base_salary: 3000) }
  let(:salary_calculation) do
    create(:salary_calculation,
           employee: employee,
           salary_package: salary_package,
           gross_salary: 3000,
           net_salary: 2500,
           deductions: {
             'employee_social_security' => 225,
             'income_tax' => 150,
             'medical_insurance' => 75,
             'salary_advances' => 50,
             'other_deductions' => 25
           })
  end

  describe '#employee_social_security' do
    it 'returns the employee social security deduction from deductions JSONB' do
      expect(salary_calculation.employee_social_security).to eq(225)
    end

    it 'returns 0 when not present in deductions' do
      salary_calculation.update!(deductions: {})
      expect(salary_calculation.employee_social_security).to eq(0)
    end
  end

  describe '#income_tax' do
    it 'returns the income tax deduction from deductions JSONB' do
      expect(salary_calculation.income_tax).to eq(150)
    end

    it 'returns 0 when not present in deductions' do
      salary_calculation.update!(deductions: {})
      expect(salary_calculation.income_tax).to eq(0)
    end
  end

  describe '#medical_insurance' do
    it 'returns the medical insurance deduction from deductions JSONB' do
      expect(salary_calculation.medical_insurance).to eq(75)
    end
  end

  describe '#salary_advances' do
    it 'returns the salary advances deduction from deductions JSONB' do
      expect(salary_calculation.salary_advances).to eq(50)
    end
  end

  describe '#other_deductions' do
    it 'returns the other deductions from deductions JSONB' do
      expect(salary_calculation.other_deductions).to eq(25)
    end
  end

  describe '#leaves_to_pay' do
    let!(:leave_detail) do
      create(:salary_calculation_detail,
             salary_calculation: salary_calculation,
             detail_type: 'deduction',
             category: 'leave_unpaid',
             amount: 100)
    end

    let!(:additional_unpaid_detail) do
      create(:salary_calculation_detail,
             salary_calculation: salary_calculation,
             detail_type: 'deduction',
             category: 'leave_unpaid',
             amount: 75)
    end

    before do
      # Enable attendance deductions globally
      Setting.stubs(:attendance_deductions_enabled?).returns(true)
    end

    context 'when attendance deductions are enabled and employee is not exempt' do
      before do
        employee.update!(exempt_from_attendance_deductions: false)
      end

      it 'includes all deduction types' do
        expect(salary_calculation.leaves_to_pay).to eq(175) # 100 + 75
      end
    end

    context 'when employee is exempt from attendance deductions' do
      before do
        employee.update!(exempt_from_attendance_deductions: true)
      end

      it 'only includes leave deductions' do
        expect(salary_calculation.leaves_to_pay).to eq(100) # Only leave_unpaid
      end
    end

    context 'when attendance deductions are globally disabled' do
      before do
        Setting.stubs(:attendance_deductions_enabled?).returns(false)
        employee.update!(exempt_from_attendance_deductions: false)
      end

      it 'only includes leave deductions' do
        expect(salary_calculation.leaves_to_pay).to eq(100) # Only leave_unpaid
      end
    end
  end

  describe '#employer_social_security' do
    it 'calculates employer social security based on database configuration' do
      # Mock SocialSecurityConfig to return known rates
      config = mock('SocialSecurityConfig')
      config.stubs(:employer_rate).returns(14.25)
      config.stubs(:max_salary).returns(nil)
      SocialSecurityConfig.stubs(:current).returns(config)

      # 14.25% of 3000 = 427.5
      expect(salary_calculation.employer_social_security).to eq(427.5)
    end
  end

  describe '#net_to_pay' do
    it 'returns the net salary' do
      expect(salary_calculation.net_to_pay).to eq(2500)
    end
  end

  describe '#payment_method' do
    it 'returns Transfer as default' do
      expect(salary_calculation.payment_method).to eq('Transfer')
    end
  end

  describe '#total_deductions' do
    let!(:leave_detail) do
      create(:salary_calculation_detail,
             salary_calculation: salary_calculation,
             detail_type: 'deduction',
             category: 'leave_unpaid',
             amount: 100)
    end

    it 'calculates total deductions including leaves to pay' do
      # deductions JSONB: 225 + 150 + 75 + 50 + 25 = 525
      # leaves_to_pay: 100
      # total: 625
      expect(salary_calculation.total_deductions).to eq(625)
    end
  end

  describe '#deduction_breakdown' do
    it 'returns a hash with all deduction types' do
      breakdown = salary_calculation.deduction_breakdown

      expect(breakdown).to include(
        employee_social_security: 225,
        income_tax: 150,
        medical_insurance: 75,
        salary_advances: 50,
        other_deductions: 25,
        leaves_to_pay: 0
      )
    end
  end

  describe '#employer_contribution_breakdown' do
    it 'returns a hash with unified employer contributions' do
      # Mock SocialSecurityConfig to return known rates
      config = mock('SocialSecurityConfig')
      config.stubs(:employer_rate).returns(14.25)
      config.stubs(:max_salary).returns(nil)
      SocialSecurityConfig.stubs(:current).returns(config)

      breakdown = salary_calculation.employer_contribution_breakdown

      expect(breakdown).to include(
        employer_social_security: 427.5  # 14.25% of 3000 = 427.5 (unified rate)
      )
    end
  end
end
