require 'rails_helper'

RSpec.describe Attendance::PeriodCalculationWorker, type: :worker do
  describe "#perform" do
    it "passes incomplete_day=true for today and incomplete_day=false for other days" do
      # Create a mock employee
      employee = mock('Employee')
      employee.stubs(:id).returns(1)

      # Stub Employee.find to return our mock
      Employee.stubs(:find).returns(employee)

      # Create a worker instance
      worker = PeriodCalculationWorker.new

      # For today, we expect incomplete_day to be true
      today = Date.today
      service_for_today = mock('PeriodService')
      service_for_today.expects(:calculate_periods).returns([])
      Attendance::PeriodService.expects(:new).with(employee, today, true).returns(service_for_today)

      # Run the worker for today
      worker.perform(1, today.to_s)

      # For yesterday, we expect incomplete_day to be false
      yesterday = Date.yesterday
      service_for_yesterday = mock('PeriodService')
      service_for_yesterday.expects(:calculate_periods).returns([])
      Attendance::PeriodService.expects(:new).with(employee, yesterday, false).returns(service_for_yesterday)

      # Run the worker for yesterday
      worker.perform(1, yesterday.to_s)
    end
  end
end
