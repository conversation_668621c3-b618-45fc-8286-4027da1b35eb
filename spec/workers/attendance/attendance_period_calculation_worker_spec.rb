require 'rails_helper'

RSpec.describe Attendance::PeriodCalculationWorker, type: :worker do
  before do
    # Create a test employee
    @employee = FactoryBot.create(:employee)

    # Set test date
    @date = Date.new(2023, 1, 1)

    # Stub the PeriodService to prevent actual calculations during tests
    @mock_service = mock('period_service')
    @mock_periods = [ mock('period') ]
    @mock_service.stubs(:calculate_periods).returns(@mock_periods)
    Attendance::PeriodService.stubs(:new).returns(@mock_service)

    # Stub the logger
    Rails.logger.stubs(:info)
    Rails.logger.stubs(:error)
  end

  describe "#perform" do
    it "calculates periods for the specified employee and date" do
      # Expect the PeriodService to be initialized with the correct parameters (including incomplete_day flag)
      Attendance::PeriodService.expects(:new).with(@employee, @date, false).returns(@mock_service)
      @mock_service.expects(:calculate_periods).returns(@mock_periods)

      # Execute the worker
      worker = PeriodCalculationWorker.new
      worker.perform(@employee.id, @date.to_s)
    end

    it "handles string date parameter correctly" do
      # Expect the PeriodService to be initialized with the correct parameters (including incomplete_day flag)
      Attendance::PeriodService.expects(:new).with(@employee, @date, false).returns(@mock_service)

      # Execute the worker with string date
      worker = PeriodCalculationWorker.new
      worker.perform(@employee.id, "2023-01-01")
    end

    it "logs error when employee not found but continues execution" do
      # Expect error logging
      Rails.logger.expects(:error).with(includes("Failed to find employee")).once

      # Expect the PeriodService to still be called with a stub employee
      Attendance::PeriodService.expects(:new).returns(@mock_service)

      # Execute the worker with non-existent employee ID
      worker = PeriodCalculationWorker.new
      worker.perform(999999, @date.to_s)
    end

    it "logs error when date parsing fails" do
      # Expect error logging
      Rails.logger.expects(:error).with(includes("Error calculating attendance periods")).once

      # Execute the worker with invalid date
      worker = PeriodCalculationWorker.new

      # This should raise an error that gets caught and logged
      expect {
        worker.perform(@employee.id, "not-a-date")
      }.to raise_error(ArgumentError)
    end

    it "logs completion after successful execution" do
      # Expect completion logging
      Rails.logger.expects(:info).with(includes("Calculated attendance periods")).once

      # Execute the worker
      worker = PeriodCalculationWorker.new
      worker.perform(@employee.id, @date.to_s)
    end
  end
end
