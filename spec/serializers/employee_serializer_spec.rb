require 'rails_helper'

RSpec.describe EmployeeSerializer, type: :serializer do
  describe 'serialization' do
    it 'serializes basic employee attributes' do
      # Create an employee with test data
      employee = build(:employee,
                       id: 1,
                       user_id: 12345,
                       department: :hr,
                       start_date: Date.new(2023, 1, 15),
                       status: :active,
                       phone: '+962790123456'
      )

      # Mock the user data methods
      employee.stubs(:name).returns('<PERSON>')
      employee.stubs(:email).returns('<EMAIL>')
      employee.stubs(:avatar_url).returns('https://example.com/avatar.jpg')
      employee.stubs(:permissions).returns(%w[read:employee update:employee])
      employee.stubs(:department_name).returns('Human Resources')
      employee.stubs(:phone_intl).returns('+962 79 012 3456')
      employee.stubs(:user_roles_list).returns([
                                                 {
                                                   'role' => {
                                                     'id' => 1,
                                                     'name' => 'HR Manager',
                                                     'global' => true
                                                   },
                                                   'project' => nil
                                                 },
                                                 {
                                                   'role' => {
                                                     'id' => 2,
                                                     'name' => 'Team Lead',
                                                     'global' => false
                                                   },
                                                   'project' => {
                                                     'id' => 100,
                                                     'name' => 'Project Alpha'
                                                   }
                                                 }
                                               ])

      # Serialize the employee
      serializer = described_class.new(employee)
      json = serializer.serializable_hash

      # Verify the serialized data
      expect(json[:data][:id]).to eq(employee.id.to_s)
      expect(json[:data][:type]).to eq(:employee)

      # Verify attributes
      attributes = json[:data][:attributes]
      expect(attributes[:name]).to eq('John Doe')
      expect(attributes[:email]).to eq('<EMAIL>')
      expect(attributes[:user_id]).to eq(12345)
      expect(attributes[:department]).to eq('hr')
      expect(attributes[:department_name]).to eq('Human Resources')
      expect(attributes[:start_date].to_s).to eq('2023-01-15')
      expect(attributes[:status]).to eq('active')
      expect(attributes[:phone]).to eq('07 9012 3456')
      expect(attributes[:phone_intl]).to eq('+962 79 012 3456')
      expect(attributes[:avatar_url]).to eq('https://example.com/avatar.jpg')
      expect(attributes[:permissions]).to eq(%w[read:employee update:employee])

      # Verify user_roles_list (if it exists)
      user_roles_list = attributes[:user_roles_list]
      if user_roles_list
        expect(user_roles_list.length).to eq(2)
      end

      # Verify role details (if user_roles_list exists and has content)
      if user_roles_list && user_roles_list.length >= 2
        # Verify first role (global role without project)
        expect(user_roles_list[0][:role][:id]).to eq(1)
        expect(user_roles_list[0][:role][:name]).to eq('HR Manager')
        expect(user_roles_list[0][:role][:global]).to eq(true)
        expect(user_roles_list[0][:project]).to be_nil

        # Verify second role (project-specific role)
        expect(user_roles_list[1][:role][:id]).to eq(2)
        expect(user_roles_list[1][:role][:name]).to eq('Team Lead')
        expect(user_roles_list[1][:role][:global]).to eq(false)
        expect(user_roles_list[1][:project][:id]).to eq(100)
        expect(user_roles_list[1][:project][:name]).to eq('Project Alpha')
      end
    end

    it 'handles nil user_roles_list gracefully' do
      # Create an employee with test data
      employee = build(:employee)

      # Mock the user data methods with nil user_roles_list
      employee.stubs(:name).returns('Jane Smith')
      employee.stubs(:email).returns('<EMAIL>')
      employee.stubs(:user_roles_list).returns(nil)

      # Serialize the employee
      serializer = described_class.new(employee)
      json = serializer.serializable_hash

      # Verify the serialized data
      attributes = json[:data][:attributes]
      expect(attributes[:user_roles_list]).to be_nil
    end
  end

  describe 'salary package associations' do
    let(:employee) { create(:employee) }
    let(:hr_manager) { create(:employee) }
    let(:other_manager) { create(:employee) }

    before do
      # Mock basic employee data
      employee.stubs(:name).returns('Target Employee')
      employee.stubs(:email).returns('<EMAIL>')
      hr_manager.stubs(:name).returns('HR Manager')
      hr_manager.stubs(:email).returns('<EMAIL>')
      other_manager.stubs(:name).returns('Other Manager')
      other_manager.stubs(:email).returns('<EMAIL>')
    end

    describe 'draft_salary_package association' do
      context 'when current_employee has created a draft for the target employee' do
        let!(:draft_package) do
          create(:salary_package,
                 employee: employee,
                 created_by: hr_manager,
                 status: :draft,
                 base_salary: 5000)
        end

        it 'returns the draft package created by current_employee' do
          # Serialize with current_employee parameter
          serializer = described_class.new(employee, { params: { current_employee: hr_manager } })
          json = serializer.serializable_hash

          # Verify the draft_salary_package relationship is present
          relationships = json[:data][:relationships]
          expect(relationships[:draft_salary_package][:data]).to be_present
          expect(relationships[:draft_salary_package][:data][:id]).to eq(draft_package.id.to_s)
          expect(relationships[:draft_salary_package][:data][:type]).to eq(:salary_package)
        end
      end

      context 'when current_employee has not created a draft for the target employee' do
        let!(:other_draft_package) do
          create(:salary_package,
                 employee: employee,
                 created_by: other_manager,
                 status: :draft,
                 base_salary: 6000)
        end

        it 'returns null when no draft exists for current_employee' do
          # Serialize with current_employee parameter (hr_manager has no draft)
          serializer = described_class.new(employee, { params: { current_employee: hr_manager } })
          json = serializer.serializable_hash

          # Verify the draft_salary_package relationship is null
          relationships = json[:data][:relationships]
          expect(relationships[:draft_salary_package][:data]).to be_nil
        end
      end

      context 'when multiple drafts exist from different creators' do
        let!(:hr_draft) do
          create(:salary_package,
                 employee: employee,
                 created_by: hr_manager,
                 status: :draft,
                 base_salary: 5000)
        end

        let!(:other_draft) do
          create(:salary_package,
                 employee: employee,
                 created_by: other_manager,
                 status: :draft,
                 base_salary: 6000)
        end

        it 'returns only the draft created by current_employee' do
          # Serialize as HR manager
          serializer = described_class.new(employee, { params: { current_employee: hr_manager } })
          json = serializer.serializable_hash

          # Should return HR manager's draft, not the other one
          relationships = json[:data][:relationships]
          expect(relationships[:draft_salary_package][:data]).to be_present
          expect(relationships[:draft_salary_package][:data][:id]).to eq(hr_draft.id.to_s)

          # Serialize as other manager
          serializer2 = described_class.new(employee, { params: { current_employee: other_manager } })
          json2 = serializer2.serializable_hash

          # Should return other manager's draft
          relationships2 = json2[:data][:relationships]
          expect(relationships2[:draft_salary_package][:data]).to be_present
          expect(relationships2[:draft_salary_package][:data][:id]).to eq(other_draft.id.to_s)
        end
      end

      context 'when no current_employee parameter is provided' do
        let!(:draft_package) do
          create(:salary_package,
                 employee: employee,
                 created_by: hr_manager,
                 status: :draft)
        end

        it 'returns null when current_employee is not provided' do
          # Serialize without current_employee parameter
          serializer = described_class.new(employee)
          json = serializer.serializable_hash

          # Should return null since no current_employee context
          relationships = json[:data][:relationships]
          expect(relationships[:draft_salary_package][:data]).to be_nil
        end
      end
    end

    describe 'pending_salary_package association' do
      context 'when employee has a pending approval package' do
        let!(:pending_package) do
          create(:salary_package,
                 employee: employee,
                 status: :pending_approval,
                 base_salary: 7000)
        end

        it 'returns the pending package' do
          serializer = described_class.new(employee)
          json = serializer.serializable_hash

          relationships = json[:data][:relationships]
          expect(relationships[:pending_salary_package][:data]).to be_present
          expect(relationships[:pending_salary_package][:data][:id]).to eq(pending_package.id.to_s)
          expect(relationships[:pending_salary_package][:data][:type]).to eq(:salary_package)
        end
      end

      context 'when employee has no pending package' do
        it 'returns null' do
          serializer = described_class.new(employee)
          json = serializer.serializable_hash

          relationships = json[:data][:relationships]
          expect(relationships[:pending_salary_package][:data]).to be_nil
        end
      end
    end

    describe 'salary_package association (current)' do
      context 'when employee has a current approved package' do
        let!(:current_package) do
          create(:salary_package,
                 employee: employee,
                 status: :approved,
                 effective_date: 1.month.ago,
                 base_salary: 8000)
        end

        it 'returns the current package' do
          serializer = described_class.new(employee)
          json = serializer.serializable_hash

          relationships = json[:data][:relationships]
          expect(relationships[:salary_package][:data]).to be_present
          expect(relationships[:salary_package][:data][:id]).to eq(current_package.id.to_s)
          expect(relationships[:salary_package][:data][:type]).to eq(:salary_package)
        end
      end

      context 'when employee has no current package' do
        it 'returns null' do
          serializer = described_class.new(employee)
          json = serializer.serializable_hash

          relationships = json[:data][:relationships]
          expect(relationships[:salary_package][:data]).to be_nil
        end
      end
    end
  end
end
