require 'rails_helper'

RSpec.describe SettingSerializer, type: :serializer do
  describe 'serialization' do
    it 'serializes setting attributes' do
      # Create a setting
      setting = Setting.new(
        id: 1,
        namespace: 'attendance',
        key: 'work_start_time',
        value: '09:00',
        description: 'Default work start time',
        is_editable: true,
        setting_type: :time
      )

      # Serialize the setting
      serializer = described_class.new(setting)
      json = serializer.serializable_hash

      # Verify the serialized data
      expect(json[:data][:id]).to eq(setting.id.to_s)
      expect(json[:data][:type]).to eq(:setting)

      # Verify attributes
      attributes = json[:data][:attributes]
      expect(attributes[:namespace]).to eq('attendance')
      expect(attributes[:key]).to eq('work_start_time')
      expect(attributes[:value]).to be_a(Time)
      expect(attributes[:description]).to eq('Default work start time')
      expect(attributes[:is_editable]).to eq(true)
      expect(attributes[:setting_type]).to eq('time')
      expect(attributes[:logical_key]).to eq('attendance/work_start_time')
      expect(attributes[:type_info]).to be_a(Hash)
      expect(attributes[:type_info][:type]).to eq('time')
    end

    it 'serializes non-editable settings correctly' do
      # Create a non-editable setting
      setting = Setting.new(
        namespace: 'system',
        key: 'version',
        value: '1.0.0',
        description: 'System version',
        is_editable: false
      )

      # Serialize the setting
      serializer = described_class.new(setting)
      json = serializer.serializable_hash

      # Verify the is_editable attribute
      attributes = json[:data][:attributes]
      expect(attributes[:is_editable]).to eq(false)
    end

    it 'handles complex values correctly' do
      # Create a setting with a complex value
      setting = Setting.new(
        namespace: 'attendance',
        key: 'work_days',
        value: '["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]',
        description: 'Default work days',
        is_editable: true
      )

      # Serialize the setting
      serializer = described_class.new(setting)
      json = serializer.serializable_hash

      # Verify the value attribute
      attributes = json[:data][:attributes]
      expect(attributes[:value]).to eq('["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]')
    end

    it 'serializes type-aware attributes correctly' do
      # Create a boolean setting
      setting = Setting.new(
        namespace: 'attendance',
        key: 'auto_leave_enabled',
        value: 'true',
        description: 'Enable automatic leave detection',
        is_editable: true,
        setting_type: :boolean
      )

      # Serialize the setting
      serializer = described_class.new(setting)
      json = serializer.serializable_hash

      # Verify type-aware attributes
      attributes = json[:data][:attributes]
      expect(attributes[:setting_type]).to eq('boolean')
      expect(attributes[:value]).to be true
      expect(attributes[:type_info][:type]).to eq('boolean')
      expect(attributes[:type_info][:validation_rules]).to be_a(Hash)
    end

    it 'includes logical_key attribute' do
      setting = Setting.new(
        namespace: 'company',
        key: 'name',
        value: 'ATHAR',
        setting_type: :string
      )

      serializer = described_class.new(setting)
      json = serializer.serializable_hash

      attributes = json[:data][:attributes]
      expect(attributes[:logical_key]).to eq('company/name')
    end
  end
end
