# frozen_string_literal: true

# Support module for mocking approval workflow calls in tests
# This prevents RPC calls to the core service during testing
module ApprovalWorkflowMocking
  # Mock the submit_for_approval method to return a successful response
  # This prevents actual RPC calls to the core service for approval workflow creation
  def mock_approval_workflow_submission!
    # Stub submit_for_approval for all approvable models
    [ SalaryPackage, Leave, SalaryCalculation ].each do |model_class|
      model_class.any_instance.stubs(:submit_for_approval).returns(
        OpenStruct.new(
          success?: true,
          id: "approval-request-#{SecureRandom.hex(6)}",
          workflow_id: "workflow-#{SecureRandom.hex(6)}",
          workflow_name: "#{model_class.name} Approval Workflow"
        )
      )
    end
  end

  # Mock approval workflow submission for a specific model class
  def mock_approval_workflow_submission_for!(model_class)
    model_class.any_instance.stubs(:submit_for_approval).returns(
      OpenStruct.new(
        success?: true,
        id: "approval-request-#{SecureRandom.hex(6)}",
        workflow_id: "workflow-#{SecureRandom.hex(6)}",
        workflow_name: "#{model_class.name} Approval Workflow"
      )
    )
  end

  # Mock approval workflow submission with custom response
  def mock_approval_workflow_submission_with!(response)
    SalaryPackage.any_instance.stubs(:submit_for_approval).returns(response)
  end

  # Mock approval workflow submission failure
  def mock_approval_workflow_submission_failure!
    SalaryPackage.any_instance.stubs(:submit_for_approval).returns(false)
  end

  # Helper method to create a mock approval request
  def mock_approval_request(attributes = {})
    default_attributes = {
      id: "approval-request-#{SecureRandom.hex(6)}",
      workflow_id: "workflow-#{SecureRandom.hex(6)}",
      workflow_name: "Test Approval Workflow",
      status: "pending",
      success?: true
    }

    OpenStruct.new(default_attributes.merge(attributes))
  end
end

# Include in RSpec configuration
RSpec.configure do |config|
  config.include ApprovalWorkflowMocking

  # Automatically mock approval workflows for all tests to prevent RPC calls
  config.before(:each) do
    mock_approval_workflow_submission!
  end
end
