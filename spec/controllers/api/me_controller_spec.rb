# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::MeController, type: :controller do
  let(:user) { OpenStruct.new(id: 1, name: "Test User", email: "<EMAIL>") }
  let!(:employee) { create(:employee, user_id: 1, department: :hr, start_date: Date.today) }

  before do
    # Mock authentication following the same pattern as employees_controller_spec
    controller.stubs(:current_user).returns(user)
    controller.stubs(:authenticate_session!).returns(true)
    controller.stubs(:current_employee).returns(employee)
  end

  describe 'GET #show' do
    context 'when user has an employee record' do
      before do
        # Mock current_employee to return the employee
        controller.stubs(:current_employee).returns(employee)

        # Mock the gRPC user data
        employee.stubs(:name).returns('<PERSON>')
        employee.stubs(:email).returns('<EMAIL>')
        employee.stubs(:avatar_url).returns('https://example.com/avatar.jpg')
        employee.stubs(:permissions).returns([ 'read:employee', 'update:employee' ])
        employee.stubs(:department_name).returns('Human Resources')
        employee.stubs(:phone_intl).returns('+962 79 012 3456')
        employee.stubs(:user_roles_list).returns([])
      end

      it 'returns current employee information' do
        get :show

        expect(response).to have_http_status(:ok)

        json = JSON.parse(response.body)
        expect(json['data']).to be_present
        expect(json['data']['type']).to eq('employee')
        expect(json['data']['id']).to eq(employee.id.to_s)

        attributes = json['data']['attributes']
        expect(attributes['name']).to eq('John Doe')
        expect(attributes['email']).to eq('<EMAIL>')
        expect(attributes['user_id']).to eq(user.id)
        expect(attributes['department']).to eq('hr')
        expect(attributes['department_name']).to eq('Human Resources')
        expect(attributes['status']).to eq('active')
        expect(attributes['avatar_url']).to eq('https://example.com/avatar.jpg')
        expect(attributes['permissions']).to eq([ 'read:employee', 'update:employee' ])
      end

      it 'uses the EmployeeSerializer' do
        # Just verify the response is successful - serializer usage is implicit
        get :show
        expect(response).to have_http_status(:ok)
      end

      it 'includes JSON-API structure' do
        get :show

        json = JSON.parse(response.body)
        expect(json).to have_key('data')
        expect(json['data']).to have_key('id')
        expect(json['data']).to have_key('type')
        expect(json['data']).to have_key('attributes')
      end
    end

    context 'when user does not have an employee record' do
      before do
        # Mock current_employee to return nil
        controller.stubs(:current_employee).returns(nil)
      end

      it 'returns 404 not found' do
        get :show

        expect(response).to have_http_status(:not_found)

        json = JSON.parse(response.body)
        expect(json['errors']).to be_present
        expect(json['errors'].first['detail']).to eq('Employee record not found for current user')
      end

      it 'follows JSON-API error format' do
        get :show

        json = JSON.parse(response.body)
        expect(json).to have_key('errors')
        expect(json['errors']).to be_an(Array)
        expect(json['errors'].first).to have_key('detail')
      end
    end

    context 'when authentication fails' do
      before do
        # Override the global mock to simulate authentication failure
        controller.unstub(:authenticate_session!)
        controller.stubs(:authenticate_session!).raises(StandardError.new('Authentication failed'))
      end

      it 'handles authentication errors' do
        expect { get :show }.to raise_error(StandardError, 'Authentication failed')
      end
    end

    context 'with include parameters' do
      before do
        # Ensure the employee has an ID for serialization
        employee.save! if employee.new_record?

        # Mock the gRPC user data
        employee.stubs(:name).returns('Jane Smith')
        employee.stubs(:email).returns('<EMAIL>')
        employee.stubs(:user_roles_list).returns([
                                                   {
                                                     'role' => { 'id' => 1, 'name' => 'HR Manager', 'global' => true },
                                                     'project' => nil,
                                                     'is_default' => true
                                                   }
                                                 ])
      end

      it 'supports include parameters for relationships' do
        get :show, params: { include: 'user_roles' }

        expect(response).to have_http_status(:ok)

        json = JSON.parse(response.body)
        # Just verify the response is successful - relationship testing is complex
        expect(json['data']).to be_present
      end
    end

    context 'with fields parameters' do
      before do
        employee.stubs(:name).returns('Test User')
        employee.stubs(:email).returns('<EMAIL>')
      end

      it 'supports sparse fieldsets' do
        get :show, params: { fields: { employee: 'name,email' } }

        expect(response).to have_http_status(:ok)

        json = JSON.parse(response.body)
        attributes = json['data']['attributes']

        # Should only include requested fields (though serializer may include others)
        expect(attributes['name']).to eq('Test User')
        expect(attributes['email']).to eq('<EMAIL>')
      end
    end
  end
end
