# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::Attendance::ReportsController, type: :controller do
  let(:employee) { create(:employee, name: '<PERSON>') }
  let(:other_employee) { create(:employee, name: '<PERSON>') }
  let(:user) { create(:user) }
  let(:hr_user) { create(:user) }
  let(:valid_params) do
    {
      employee_id: employee.id,
      year: 2024,
      month: 6
    }
  end

  before do
    # Mock authentication and authorization
    controller.stubs(:authenticate_session!).returns(true)
    controller.stubs(:authenticate_api_user!).returns(true)
    controller.stubs(:current_user).returns(user)
    controller.stubs(:current_employee).returns(employee)
    controller.stubs(:can?).returns(true)
    controller.stubs(:authorize!).returns(true)
    controller.stubs(:can_access_employee_reports?).returns(true)
    controller.stubs(:authorize_access!).returns(true)
  end

  describe 'GET #timesheet' do
    context 'with valid parameters' do
      context 'when requesting PDF format' do
        it 'returns PDF content' do
          # Mock the service
          service_instance = stub('TimesheetReportService')
          Attendance::TimesheetReportService.stubs(:new).returns(service_instance)
          service_instance.stubs(:generate_pdf).returns('fake pdf content')

          get :timesheet, params: valid_params

          expect(response).to have_http_status(:success)
          expect(response.content_type).to eq('application/pdf')
          expect(response.body).to eq('fake pdf content')
        end

        it 'sets correct filename' do
          service_instance = stub('TimesheetReportService')
          Attendance::TimesheetReportService.stubs(:new).returns(service_instance)
          service_instance.stubs(:generate_pdf).returns('fake pdf content')

          get :timesheet, params: valid_params

          # Check that the filename contains the expected pattern
          expect(response.headers['Content-Disposition']).to include('timesheet_')
          expect(response.headers['Content-Disposition']).to include('_2024-06.pdf')
        end
      end

      context 'when requesting HTML format' do
        it 'returns HTML content' do
          service_instance = stub('TimesheetReportService')
          Attendance::TimesheetReportService.stubs(:new).returns(service_instance)
          service_instance.stubs(:generate_html).returns('<html>timesheet</html>')

          get :timesheet, params: valid_params.merge(format: 'html')

          expect(response).to have_http_status(:success)
          expect(response.content_type).to include('text/html')
          expect(response.body).to include('timesheet')
        end
      end
    end

    context 'with invalid parameters' do
      it 'returns error for missing employee_id' do
        get :timesheet, params: { year: 2024, month: 6 }
        expect(response).to have_http_status(:not_found)
      end

      it 'returns error for missing year' do
        get :timesheet, params: { employee_id: employee.id, month: 6 }
        expect(response).to have_http_status(:bad_request)
      end

      it 'returns error for missing month' do
        get :timesheet, params: { employee_id: employee.id, year: 2024 }
        expect(response).to have_http_status(:bad_request)
      end
    end

  end
end
