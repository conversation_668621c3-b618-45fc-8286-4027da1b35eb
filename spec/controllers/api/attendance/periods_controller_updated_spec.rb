require 'rails_helper'

RSpec.describe Api::Attendance::PeriodsController, type: :controller do
  let(:employee) { create(:employee) }
  let(:user) { create(:user, employee: employee) }
  let(:date) { Date.current }

  before do
    sign_in user
    controller.stubs(:current_employee).returns(employee)
  end

  describe 'GET #index' do
    let!(:work_period) { create(:attendance_period, employee: employee, period_type: 'work', auto_generated: false) }
    let!(:auto_leave) { create(:attendance_period, employee: employee, period_type: 'leave', auto_generated: true) }
    let!(:manual_leave) { create(:attendance_period, employee: employee, period_type: 'leave', auto_generated: false) }
    let!(:other_employee_period) { create(:attendance_period, period_type: 'work') }

    context 'without filters' do
      it 'returns all periods for the employee' do
        get :index

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['data'].length).to eq(3)
      end
    end

    context 'with auto_generated filter' do
      it 'filters auto-generated periods when auto_generated=true' do
        get :index, params: { auto_generated: 'true' }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['data'].length).to eq(1)

        period_data = json_response['data'].first
        expect(period_data['attributes']['auto_generated']).to be true
        expect(period_data['attributes']['period_type']).to eq('leave')
      end

      it 'filters manual periods when auto_generated=false' do
        get :index, params: { auto_generated: 'false' }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['data'].length).to eq(2)

        json_response['data'].each do |period_data|
          expect(period_data['attributes']['auto_generated']).to be false
        end
      end
    end

    context 'with leave_type filter' do
      it 'filters auto-generated leaves when leave_type=auto_generated' do
        get :index, params: { leave_type: 'auto_generated' }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['data'].length).to eq(1)

        period_data = json_response['data'].first
        expect(period_data['attributes']['period_type']).to eq('leave')
        expect(period_data['attributes']['auto_generated']).to be true
        expect(period_data['attributes']['leave_type']).to eq('auto_generated')
      end

      it 'filters manual leaves when leave_type=manual' do
        get :index, params: { leave_type: 'manual' }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['data'].length).to eq(1)

        period_data = json_response['data'].first
        expect(period_data['attributes']['period_type']).to eq('leave')
        expect(period_data['attributes']['auto_generated']).to be false
        expect(period_data['attributes']['leave_type']).to eq('manual')
      end

      it 'returns empty when leave_type filter applied to non-leave periods' do
        # Delete leave periods, keep only work period
        Attendance::Period.where(period_type: 'leave').destroy_all

        get :index, params: { leave_type: 'auto_generated' }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['data']).to be_empty
      end
    end

    context 'with combined filters' do
      it 'applies both auto_generated and leave_type filters' do
        get :index, params: { auto_generated: 'true', leave_type: 'auto_generated' }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['data'].length).to eq(1)

        period_data = json_response['data'].first
        expect(period_data['attributes']['auto_generated']).to be true
        expect(period_data['attributes']['leave_type']).to eq('auto_generated')
      end
    end

    context 'with date range filters' do
      let!(:july_period) { create(:attendance_period, employee: employee, date: Date.new(2024, 7, 15)) }
      let!(:august_period) { create(:attendance_period, employee: employee, date: Date.new(2024, 8, 15)) }

      it 'filters by year and month' do
        get :index, params: { year: 2024, month: 7 }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)

        # Should include july_period and any other periods from July 2024
        july_periods = json_response['data'].select do |period|
          Date.parse(period['attributes']['date']).month == 7
        end
        expect(july_periods).not_to be_empty
      end
    end
  end

  describe 'GET #monthly_summary' do
    let!(:work_period) do
      create(:attendance_period,
             employee: employee,
             date: Date.new(2024, 7, 1),
             period_type: 'work',
             duration_minutes: 480)
    end

    let!(:auto_leave) do
      create(:attendance_period,
             employee: employee,
             date: Date.new(2024, 7, 2),
             period_type: 'leave',
             auto_generated: true)
    end

    let!(:manual_leave) do
      create(:attendance_period,
             employee: employee,
             date: Date.new(2024, 7, 3),
             period_type: 'leave',
             auto_generated: false)
    end

    before do
      # Mock MonthlyCalculationService
      Attendance::MonthlyCalculationService.stubs(:new).returns(
        mock(monthly_summary: {
          monthly_record: double(
            year: 2024,
            month: 7,
            expected_hours: 180.0,
            actual_hours: 8.0,
            deficit_hours: 172.0,
            attendance_percentage: 4.4,
            deficit_percentage: 95.6
          ),
          auto_generated_leave_days: 1,
          manual_leave_days: 1,
          total_leave_days: 2,
          working_days_in_month: 22,
          attendance_percentage: 4.4,
          deficit_percentage: 95.6
        })
      )
    end

    it 'returns monthly summary for specified year and month' do
      get :monthly_summary, params: { year: 2024, month: 7 }

      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)

      expect(json_response).to include(
        'monthly_record',
        'auto_generated_leave_days',
        'manual_leave_days',
        'total_leave_days',
        'working_days_in_month',
        'attendance_percentage',
        'deficit_percentage'
      )

      expect(json_response['auto_generated_leave_days']).to eq(1)
      expect(json_response['manual_leave_days']).to eq(1)
      expect(json_response['total_leave_days']).to eq(2)
    end

    it 'requires year and month parameters' do
      get :monthly_summary

      expect(response).to have_http_status(:bad_request)
      json_response = JSON.parse(response.body)
      expect(json_response['error']).to include('Year and month are required')
    end

    it 'validates year parameter' do
      get :monthly_summary, params: { year: 'invalid', month: 7 }

      expect(response).to have_http_status(:bad_request)
    end

    it 'validates month parameter' do
      get :monthly_summary, params: { year: 2024, month: 13 }

      expect(response).to have_http_status(:bad_request)
    end
  end

  describe 'serializer attributes' do
    let!(:auto_leave) do
      create(:attendance_period,
             employee: employee,
             period_type: 'leave',
             auto_generated: true,
             notes: 'Auto-generated leave test')
    end

    it 'includes new attributes in serialized response' do
      get :index

      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)
      period_data = json_response['data'].first

      # Check new attributes are present
      expect(period_data['attributes']).to include(
        'auto_generated',
        'notes',
        'leave_type',
        'is_auto_generated',
        'is_manual',
        'is_leave_period'
      )

      # Check values are correct
      expect(period_data['attributes']['auto_generated']).to be true
      expect(period_data['attributes']['leave_type']).to eq('auto_generated')
      expect(period_data['attributes']['is_auto_generated']).to be true
      expect(period_data['attributes']['is_manual']).to be false
      expect(period_data['attributes']['is_leave_period']).to be true
      expect(period_data['attributes']['notes']).to eq('Auto-generated leave test')
    end

    it 'includes correct status badge for auto-generated leave' do
      get :index

      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)
      period_data = json_response['data'].first

      status_badge = period_data['attributes']['status_badge']
      expect(status_badge['color']).to eq('orange')
      expect(status_badge['text']).to eq('Auto Leave')
    end

    it 'includes correct period type label for leave' do
      get :index

      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)
      period_data = json_response['data'].first

      expect(period_data['attributes']['period_type_label']).to eq('Leave Period')
    end
  end

  describe 'filter methods' do
    describe '#apply_auto_generated_filter' do
      let!(:auto_period) { create(:attendance_period, employee: employee, auto_generated: true) }
      let!(:manual_period) { create(:attendance_period, employee: employee, auto_generated: false) }

      it 'filters correctly when auto_generated is true' do
        collection = Attendance::Period.where(employee: employee)
        filtered = controller.send(:apply_auto_generated_filter, collection, 'true')

        expect(filtered).to include(auto_period)
        expect(filtered).not_to include(manual_period)
      end

      it 'filters correctly when auto_generated is false' do
        collection = Attendance::Period.where(employee: employee)
        filtered = controller.send(:apply_auto_generated_filter, collection, 'false')

        expect(filtered).to include(manual_period)
        expect(filtered).not_to include(auto_period)
      end

      it 'returns original collection when parameter is nil' do
        collection = Attendance::Period.where(employee: employee)
        filtered = controller.send(:apply_auto_generated_filter, collection, nil)

        expect(filtered).to eq(collection)
      end
    end

    describe '#apply_leave_type_filter' do
      let!(:auto_leave) { create(:attendance_period, employee: employee, period_type: 'leave', auto_generated: true) }
      let!(:manual_leave) { create(:attendance_period, employee: employee, period_type: 'leave', auto_generated: false) }
      let!(:work_period) { create(:attendance_period, employee: employee, period_type: 'work') }

      it 'filters auto-generated leaves when leave_type is auto_generated' do
        collection = Attendance::Period.where(employee: employee)
        filtered = controller.send(:apply_leave_type_filter, collection, 'auto_generated')

        expect(filtered).to include(auto_leave)
        expect(filtered).not_to include(manual_leave)
        expect(filtered).not_to include(work_period)
      end

      it 'filters manual leaves when leave_type is manual' do
        collection = Attendance::Period.where(employee: employee)
        filtered = controller.send(:apply_leave_type_filter, collection, 'manual')

        expect(filtered).to include(manual_leave)
        expect(filtered).not_to include(auto_leave)
        expect(filtered).not_to include(work_period)
      end

      it 'returns original collection when parameter is nil' do
        collection = Attendance::Period.where(employee: employee)
        filtered = controller.send(:apply_leave_type_filter, collection, nil)

        expect(filtered).to eq(collection)
      end
    end
  end
end
