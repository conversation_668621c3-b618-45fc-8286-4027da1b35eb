# frozen_string_literal: true

require 'rails_helper'

# Configure AtharRpc for test environment
begin
  AtharRpc.configuration.local_mode = true
rescue NameError
  # AtharRpc not available in test environment
end


RSpec.describe "Pagination Bug Investigation", type: :request do
  let(:admin_employee) { create(:employee, department: :admin) }

  let(:auth_headers) do
    {
      'Content-Type' => 'application/json',
      'Accept' => 'application/json',
      'Authorization' => 'Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOjcsInRva2VuX3R5cGUiOiJzZXNzaW9uIiwic2NvcGUiOiJwZW9wbGUiLCJ1c2VyX3R5cGUiOiJnbG9iYWwiLCJwcm9qZWN0X2lkIjpudWxsLCJwcm9qZWN0X25hbWUiOm51bGwsInJvbGUiOiIjPFJvbGU6MHgwMDAwZmZmZmE2ZDU5ODkwPiIsInBlcm1pc3Npb25zIjpbIm1hbmFnZTpwYXlyb2xsIiwicmVhZDpwYXlyb2xsIiwiY3JlYXRlOnBheXJvbGwiLCJ1cGRhdGU6cGF5cm9sbCIsImRlc3Ryb3k6cGF5cm9sbCIsInJlYWQ6YXBwcm92YWxfcmVxdWVzdCIsInJlYWRfb3duOmFwcHJvdmFsX3JlcXVlc3QiLCJhcHByb3ZlOmFwcHJvdmFsX3JlcXVlc3QiLCJyZWplY3Q6YXBwcm92YWxfcmVxdWVzdCIsImNhbmNlbDphcHByb3ZhbF9yZXF1ZXN0IiwiY2FuY2VsX293bjphcHByb3ZhbF9yZXF1ZXN0IiwicmVhZF9vd246ZW1wbG95ZWUiLCJ1cGRhdGVfb3duOmVtcGxveWVlIiwiY3JlYXRlOmxlYXZlIiwic3VibWl0OmxlYXZlIiwid2l0aGRyYXc6bGVhdmUiLCJyZWFkX293bjpsZWF2ZSIsIm1hbmFnZV9vd246bGVhdmUiLCJyZWNvcmQ6YXR0ZW5kYW5jZV9ldmVudCIsInJlYWRfb3duOmF0dGVuZGFuY2VfZXZlbnQiLCJyZWFkOmVtcGxveWVlIiwicmVhZDpzYWxhcnlfcGFja2FnZSIsImNyZWF0ZTpzYWxhcnlfcGFja2FnZSIsImFwcHJvdmU6c2FsYXJ5X3BhY2thZ2UiLCJyZWFkOnNhbGFyeV9jYWxjdWxhdGlvbiIsImNhbGN1bGF0ZTpzYWxhcnlfY2FsY3VsYXRpb24iLCJ1cGRhdGU6c2FsYXJ5X2NhbGN1bGF0aW9uIiwic3VibWl0OnNhbGFyeV9jYWxjdWxhdGlvbiIsImFwcHJvdmU6c2FsYXJ5X2NhbGN1bGF0aW9uIiwibWFuYWdlOnRheF9jb25maWciLCJtYW5hZ2U6c29jaWFsX3NlY3VyaXR5X2NvbmZpZyIsInJlYWRfb3duOnNhbGFyeV9wYWNrYWdlIiwicmVhZF9vd246c2FsYXJ5X2NhbGN1bGF0aW9uIl0sImlhdCI6MTc1MTU3NzE4NywiZXhwIjoxNzUxNjYzNTg3fQ.8xyFl8MKSEavDWwN2UhP1W-RdrGH_3aToZDpRCwNDH4'
    }
  end

  describe "pagination bug investigation" do
    let!(:employees) do
      # Create multiple employees to have enough data
      [
        admin_employee,
        create(:employee, department: :hr),
        create(:employee, department: :finance),
        create(:employee, department: :it),
        create(:employee, department: :admin),
        create(:employee, department: :hr)
      ]
    end

    # Use the EXACT data from the development environment that shows the bug
    let!(:salary_calculations) do
      # Clear any existing data to ensure clean test
      SalaryCalculation.delete_all

      # Create the exact same data structure as in development
      # Based on the database query results

      calculations = []

      # Create employees with specific IDs to match development data
      emp_17 = create(:employee, id: 17, department: :admin)
      emp_20 = create(:employee, id: 20, department: :hr)
      emp_1 = create(:employee, id: 1, department: :finance)
      emp_56 = create(:employee, id: 56, department: :it)
      emp_61 = create(:employee, id: 61, department: :admin)
      emp_62 = create(:employee, id: 62, department: :hr)
      emp_64 = create(:employee, id: 64, department: :finance)
      emp_6 = create(:employee, id: 6, department: :it)

      # July 2025 calculations (IDs: 1, 2, 7, 8, 9, 10, 11)
      calculations << create(:salary_calculation, id: 1, period_start_date: '2025-07-01', employee: emp_17, status: :draft, created_at: '2025-07-01 18:08:14')
      calculations << create(:salary_calculation, id: 2, period_start_date: '2025-07-01', employee: emp_20, status: :draft, created_at: '2025-07-01 18:08:14')
      calculations << create(:salary_calculation, id: 7, period_start_date: '2025-07-01', employee: emp_1, status: :draft, created_at: '2025-07-03 19:09:19')
      calculations << create(:salary_calculation, id: 8, period_start_date: '2025-07-01', employee: emp_56, status: :draft, created_at: '2025-07-03 19:09:20')
      calculations << create(:salary_calculation, id: 9, period_start_date: '2025-07-01', employee: emp_61, status: :draft, created_at: '2025-07-03 19:09:20')
      calculations << create(:salary_calculation, id: 10, period_start_date: '2025-07-01', employee: emp_62, status: :approved, created_at: '2025-07-03 19:09:20')
      calculations << create(:salary_calculation, id: 11, period_start_date: '2025-07-01', employee: emp_64, status: :submitted, created_at: '2025-07-03 19:09:20')

      # June 2025 calculations (IDs: 3, 4, 5, 6)
      calculations << create(:salary_calculation, id: 3, period_start_date: '2025-06-01', employee: emp_56, status: :draft, created_at: '2025-07-03 19:06:43')
      calculations << create(:salary_calculation, id: 4, period_start_date: '2025-06-01', employee: emp_61, status: :draft, created_at: '2025-07-03 19:06:43')
      calculations << create(:salary_calculation, id: 5, period_start_date: '2025-06-01', employee: emp_62, status: :draft, created_at: '2025-07-03 19:06:43')
      calculations << create(:salary_calculation, id: 6, period_start_date: '2025-06-01', employee: emp_64, status: :draft, created_at: '2025-07-03 19:06:43')

      # May 2025 calculations (IDs: 12, 13, 14, 15, 16)
      calculations << create(:salary_calculation, id: 12, period_start_date: '2025-05-01', employee: emp_6, status: :draft, created_at: '2025-07-03 19:44:39')
      calculations << create(:salary_calculation, id: 13, period_start_date: '2025-05-01', employee: emp_56, status: :draft, created_at: '2025-07-03 19:44:39')
      calculations << create(:salary_calculation, id: 14, period_start_date: '2025-05-01', employee: emp_61, status: :draft, created_at: '2025-07-03 19:44:39')
      calculations << create(:salary_calculation, id: 15, period_start_date: '2025-05-01', employee: emp_62, status: :draft, created_at: '2025-07-03 19:44:39')
      calculations << create(:salary_calculation, id: 16, period_start_date: '2025-05-01', employee: emp_64, status: :approved, created_at: '2025-07-03 19:44:39')

      calculations
    end

    it "reproduces the pagination bug by making actual controller calls" do
      # Show the data we created (should match development exactly)
      SalaryCalculation.order(period_start_date: :desc, id: :asc).each do |calc|
      end


      sql_queries = []

      # Subscribe to SQL events to capture queries
      subscription = ActiveSupport::Notifications.subscribe('sql.active_record') do |name, start, finish, id, payload|
        if payload[:sql].include?('SELECT') && payload[:sql].include?('salary_calculations')
          sql_queries << payload[:sql]
        end
      end

      # Test 1: Call WITHOUT pagination (like your second curl command)

      sql_queries.clear
      get '/api/finance/salary_calculations',
          params: { sort: '-period_start_date' },
          headers: auth_headers

      unpaginated_sql = sql_queries.dup

      if response.status == 200
        unpaginated_response = JSON.parse(response.body)
        unpaginated_ids = unpaginated_response['data'].map { |item| item['id'].to_i }
      else
        unpaginated_ids = []
      end

      # Test 2: Call WITH pagination (like your first curl command)

      sql_queries.clear
      get '/api/finance/salary_calculations',
          params: { sort: '-period_start_date', 'page[size]' => '5' },
          headers: auth_headers

      paginated_sql = sql_queries.dup

      if response.status == 200
        paginated_response = JSON.parse(response.body)
        paginated_ids = paginated_response['data'].map { |item| item['id'].to_i }
      else
        paginated_ids = []
      end

      # Compare the results
      first_five_unpaginated = unpaginated_ids.first(5)



      if unpaginated_sql.any? && paginated_sql.any?
        if unpaginated_sql.last == paginated_sql.last
        else
        end
      end

      if paginated_ids != first_five_unpaginated

        # Fail the test to show the bug
        fail "🎯 PAGINATION BUG CONFIRMED: #{paginated_ids} != #{first_five_unpaginated}"
      else
      end
    ensure
      # Unsubscribe from SQL notifications
      ActiveSupport::Notifications.unsubscribe(subscription) if defined?(subscription)
    end

    it "tests if attendance events controller has the same pagination bug" do
      # Create some attendance events
      employee = employees.first

      # Create attendance events
      5.times do |i|
        create(:attendance_event,
               employee: employee,
               event_type: :check_in,
               timestamp: (Time.current - i.hours).to_i)
      end


      # Test attendance events controller

      # Test WITHOUT pagination
      get '/api/attendance/events',
          params: { sort: '-timestamp' },
          headers: auth_headers

      if response.status == 200
        unpaginated_response = JSON.parse(response.body)
        unpaginated_ids = unpaginated_response['data'].map { |item| item['id'].to_i }

        # Test WITH pagination
        get '/api/attendance/events',
            params: { sort: '-timestamp', 'page[size]' => '3' },
            headers: auth_headers

        if response.status == 200
          paginated_response = JSON.parse(response.body)
          paginated_ids = paginated_response['data'].map { |item| item['id'].to_i }

          first_three_unpaginated = unpaginated_ids.first(3)

          if paginated_ids != first_three_unpaginated
          else
          end
        else
        end
      else
      end

      expect(true).to be true # Test always passes, we're just investigating
    end
  end
end
