require 'rails_helper'

RSpec.describe Api::Employees<PERSON><PERSON>roller, type: :controller do
  let(:user) { create(:user_with_role, role_name: 'admin') }

  before do
    # Mock authentication
    controller.stubs(:current_user).returns(user)
    controller.stubs(:authenticate_api_user!).returns(true)
    controller.stubs(:authenticate_session!).returns(true)
    controller.stubs(:authorize!).returns(true)
    controller.stubs(:can?).returns(true)
    controller.stubs(:authorize_create).returns(true)
    controller.stubs(:authorize_update).returns(true)
    controller.stubs(:authorize_read_all_or_own).returns(true)
    controller.stubs(:authorize_read_specific).returns(true)
    controller.stubs(:authorize_destroy).returns(true)
  end

  describe 'POST #create' do
    let(:valid_attributes) do
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'password123',
        department: 'hr',
        start_date: Date.today,
        phone: '+962790000000'
      }
    end

    context 'with valid parameters' do
      it 'creates a new employee' do
        # Mock the RPC client to avoid actual RPC calls
        Employee.any_instance.stubs(:create_and_associate_user).returns(true)
        Employee.any_instance.stubs(:user_id).returns(1)

        expect {
          post :create, params: { employee: valid_attributes }
        }.to change(Employee, :count).by(1)

        expect(response).to have_http_status(:created)
      end

      it 'creates a new employee with user_roles_list' do
        # Mock the RPC client to avoid actual RPC calls
        Employee.any_instance.stubs(:create_and_associate_user).returns(true)
        Employee.any_instance.stubs(:user_id).returns(1)

        user_roles_list = [
          { role_id: '1', project_id: '1', is_default: true },
          { role_id: '2', project_id: '2' }
        ]

        post :create, params: { employee: valid_attributes.merge(user_roles_list: user_roles_list) }

        expect(response).to have_http_status(:created)
      end

      it 'automatically sets first role as default when no default is specified' do
        # Mock the RPC client to avoid actual RPC calls
        Employee.any_instance.stubs(:create_and_associate_user).returns(true)
        Employee.any_instance.stubs(:user_id).returns(1)

        user_roles_list = [
          { role_id: '1', project_id: '1' },  # No is_default specified
          { role_id: '2', project_id: '2' }
        ]

        post :create, params: { employee: valid_attributes.merge(user_roles_list: user_roles_list) }

        expect(response).to have_http_status(:created)

        # Parse the response to check if first role was set as default
        response_data = JSON.parse(response.body)
        user_roles = response_data.dig('data', 'attributes', 'user_roles_list') || []

        # First role should be default
        expect(user_roles.first['is_default']).to be true if user_roles.any?
      end
    end

    context 'with invalid parameters' do
      it 'does not create a new employee' do
        # Invalid attributes (missing required fields)
        invalid_attributes = { name: 'John Doe' }

        expect {
          post :create, params: { employee: invalid_attributes }
        }.not_to change(Employee, :count)

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe 'PUT #update' do
    let!(:employee) { create(:employee) }

    before do
      # Mock the RPC client to avoid actual RPC calls
      Employee.any_instance.stubs(:update_user).returns(true)
    end

    it 'updates the employee with user_roles_list' do
      user_roles_list = [
        { role_id: '1', project_id: '1', is_default: true },
        { role_id: '2', project_id: '2' }
      ]

      put :update, params: { id: employee.id, employee: { user_roles_list: user_roles_list } }

      expect(response).to have_http_status(:ok)
    end

    it 'automatically sets first role as default when updating with no default specified' do
      user_roles_list = [
        { role_id: '1', project_id: '1' },  # No is_default specified
        { role_id: '2', project_id: '2' }
      ]

      put :update, params: { id: employee.id, employee: { user_roles_list: user_roles_list } }

      expect(response).to have_http_status(:ok)

      # Parse the response to check if first role was set as default
      response_data = JSON.parse(response.body)
      user_roles = response_data.dig('data', 'attributes', 'user_roles_list') || []

      # First role should be default
      expect(user_roles.first['is_default']).to be true if user_roles.any?
    end
  end
end
