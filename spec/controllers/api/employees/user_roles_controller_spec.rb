require 'rails_helper'

RSpec.describe Api::Employees::UserRolesController, type: :controller do
  let(:user) { create(:user_with_role, role_name: 'admin') }
  let!(:employee) { create(:employee) }

  before do
    # Mock authentication
    controller.stubs(:current_user).returns(user)
    controller.stubs(:authenticate_session!).returns(true)
    controller.stubs(:authorize!).returns(true)
  end

  describe 'POST #create' do
    context 'when adding the first role to an employee' do
      it 'automatically sets the role as default' do
        # Mock the RPC client to avoid actual RPC calls
        Employee.any_instance.stubs(:add_role).returns(true)
        Employee.any_instance.stubs(:save).returns(true)

        # Mock user_roles_list to simulate empty list initially
        empty_collection = AtharAuth::Models::UserRoleCollection.new
        employee.stubs(:user_roles_list).returns(empty_collection)

        # Mock the new role being added
        new_role = AtharAuth::Models::UserRole.new(
          id: 1,
          role_id: 1,
          project_id: 1,
          is_default: false
        )

        # Simulate adding the role
        Employee.any_instance.stubs(:add_role).with(anything).returns do
          empty_collection << new_role
          true
        end

        post :create, params: {
          employee_id: employee.id,
          user_role: { role_id: 1, project_id: 1 }
        }

        expect(response).to have_http_status(:created)

        # Verify that the role was set as default
        expect(new_role.is_default).to be true
      end
    end

    context 'when adding a role to an employee with existing roles' do
      it 'does not change the default if one already exists' do
        # Mock the RPC client to avoid actual RPC calls
        Employee.any_instance.stubs(:add_role).returns(true)
        Employee.any_instance.stubs(:save).returns(true)

        # Mock user_roles_list with existing roles
        existing_collection = AtharAuth::Models::UserRoleCollection.new
        existing_role = AtharAuth::Models::UserRole.new(
          id: 1,
          role_id: 1,
          project_id: 1,
          is_default: true
        )
        existing_collection << existing_role

        employee.stubs(:user_roles_list).returns(existing_collection)

        # Mock the new role being added
        new_role = AtharAuth::Models::UserRole.new(
          id: 2,
          role_id: 2,
          project_id: 2,
          is_default: false
        )

        # Simulate adding the role
        Employee.any_instance.stubs(:add_role).with(anything).returns do
          existing_collection << new_role
          true
        end

        post :create, params: {
          employee_id: employee.id,
          user_role: { role_id: 2, project_id: 2 }
        }

        expect(response).to have_http_status(:created)

        # Verify that the new role was not set as default
        expect(new_role.is_default).to be false
        # Verify that the existing default role remains default
        expect(existing_role.is_default).to be true
      end
    end
  end
end
