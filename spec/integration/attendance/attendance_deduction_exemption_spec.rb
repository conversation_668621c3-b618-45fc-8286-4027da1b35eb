require 'rails_helper'

RSpec.describe 'Attendance Deduction Exemption Integration', type: :integration do
  describe 'Employee exemption functionality' do
    let(:regular_employee) { build(:employee, exempt_from_attendance_deductions: false) }
    let(:exempt_employee) { build(:employee, exempt_from_attendance_deductions: true) }

    it 'allows creating employees with exemption status' do
      # Test creating a regular employee
      regular_employee.save(validate: false)
      expect(regular_employee.exempt_from_attendance_deductions).to be false

      # Test creating an exempt employee
      exempt_employee.save(validate: false)
      expect(exempt_employee.exempt_from_attendance_deductions).to be true
    end

    it 'provides scopes for filtering employees by exemption status' do
      regular_employee.save(validate: false)
      exempt_employee.save(validate: false)

      expect(Employee.exempt_from_attendance_deductions).to include(exempt_employee)
      expect(Employee.exempt_from_attendance_deductions).not_to include(regular_employee)

      expect(Employee.not_exempt_from_attendance_deductions).to include(regular_employee)
      expect(Employee.not_exempt_from_attendance_deductions).not_to include(exempt_employee)
    end

    it 'provides helper method for checking exemption status' do
      regular_employee.save(validate: false)
      exempt_employee.save(validate: false)

      expect(regular_employee.exempt_from_attendance_deductions?).to be false
      expect(exempt_employee.exempt_from_attendance_deductions?).to be true
    end
  end

  describe 'Database constraints' do
    it 'has proper default value' do
      employee = Employee.new
      expect(employee.exempt_from_attendance_deductions).to be false
    end

    it 'validates inclusion of boolean values' do
      employee = Employee.new(
        user_id: 999,
        department: :hr,
        start_date: Date.today,
        status: :active,
        phone: '+962790000000'
      )
      
      # Test valid values
      employee.exempt_from_attendance_deductions = true
      expect(employee.valid?).to be true
      
      employee.exempt_from_attendance_deductions = false
      expect(employee.valid?).to be true
      
      # Test invalid value
      employee.exempt_from_attendance_deductions = nil
      expect(employee.valid?).to be false
      expect(employee.errors[:exempt_from_attendance_deductions]).to include('is not included in the list')
    end
  end
end
