require 'rails_helper'

RSpec.describe "Salary Package Approval Workflow", type: :request do
  let(:hr_manager) { create(:employee, department: :hr) }
  let(:financial_manager) { create(:employee, department: :finance) }
  let(:admin_user) { create(:employee, department: :admin) }
  let(:regular_employee) { create(:employee, department: :it) }

  # Mock authentication and authorization using Mocha
  before do
    # Use the helper method from rails_helper.rb
    mock_authenticate_session!

    # Override with our specific user
    ApplicationController.any_instance.stubs(:current_user).returns(stub(id: hr_manager.user_id))
    ApplicationController.any_instance.stubs(:current_employee).returns(hr_manager)

    # Also mock authenticate_api_user! since that's what ApplicationController uses
    ApplicationController.any_instance.stubs(:authenticate_api_user!)
  end

  describe "complete approval workflow" do
    it "handles full approval cycle" do
      # Step 1: HR creates draft package
      Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).returns(true)

      post "/api/finance/salary_packages", params: {
        employee_id: regular_employee.id,
        salary_package: {
          base_salary: 6000,
          transportation_allowance: 600,
          other_allowances: 400,
          effective_date: Date.current + 1.month,
          notes: "Promotion package"
        }
      }

      expect(response).to have_http_status(:created)
      package = SalaryPackage.last
      expect(package.status).to eq("draft")
      expect(package.actor_user_id).to eq(hr_manager.user_id)

      # Step 2: Submit for approval
      patch "/api/finance/salary_packages/#{package.id}/submit", params: {
        employee_id: regular_employee.id
      }

      expect(response).to have_http_status(:ok)
      package.reload
      expect(package.status).to eq("pending_approval")

      # Verify approval request was created (mocked)
      # In real implementation, this would create actual approval request
      # expect(package.approval_request).to be_present

      # Step 3: Verify package status through show endpoint
      get "/api/finance/salary_packages/#{package.id}", params: {
        employee_id: regular_employee.id
      }

      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)
      expect(json_response['data']['attributes']['status']).to eq('pending_approval')
      expect(json_response['data']['attributes']['submittable']).to be false
      expect(json_response['data']['attributes']['editable']).to be false
    end

    it "handles sequential package approvals with automatic end dating" do
      Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).returns(true)

      # Step 1: Create and approve first package
      post "/api/finance/salary_packages", params: {
        salary_package: {
          employee_id: regular_employee.id,
          base_salary: 5000,
          transportation_allowance: 500,
          other_allowances: 300,
          effective_date: Date.current,
          notes: "Initial package"
        }
      }, headers: { 'Authorization' => 'Bearer test-token' }

      expect(response).to have_http_status(:created)
      first_package = SalaryPackage.last
      expect(first_package.status).to eq("draft")

      # Submit first package for approval
      patch "/api/finance/salary_packages/#{first_package.id}/submit", params: {
        employee_id: regular_employee.id
      }, headers: { 'Authorization' => 'Bearer test-token' }

      expect(response).to have_http_status(:ok)
      first_package.reload
      expect(first_package.status).to eq("pending_approval")

      # Simulate approval completion for first package (using the callback directly)
      first_package.on_approval_status_change("approved", "pending_approval")
      first_package.reload
      expect(first_package.status).to eq("approved")
      expect(first_package.end_date).to be_nil # No end date yet

      # Step 2: Create and approve second package
      post "/api/finance/salary_packages", params: {
        salary_package: {
          employee_id: regular_employee.id,
          base_salary: 6000,
          transportation_allowance: 600,
          other_allowances: 400,
          effective_date: Date.current + 1.month,
          notes: "Promotion package"
        }
      }, headers: { 'Authorization' => 'Bearer test-token' }

      expect(response).to have_http_status(:created)
      second_package = SalaryPackage.last
      expect(second_package.status).to eq("draft")

      # Submit second package for approval
      patch "/api/finance/salary_packages/#{second_package.id}/submit", params: {
        employee_id: regular_employee.id
      }, headers: { 'Authorization' => 'Bearer test-token' }

      expect(response).to have_http_status(:ok)
      second_package.reload
      expect(second_package.status).to eq("pending_approval")

      # Simulate approval completion for second package
      second_package.on_approval_status_change("approved", "pending_approval")
      second_package.reload
      expect(second_package.status).to eq("approved")

      # Step 3: Verify first package was automatically end-dated
      first_package.reload
      expect(first_package.end_date).to eq(second_package.effective_date - 1.day)
      expect(first_package.status).to eq("approved") # Still approved, just end-dated

      # Step 4: Verify both packages exist and are properly configured
      employee_packages = regular_employee.salary_packages.approved.order(:effective_date)
      expect(employee_packages.count).to eq(2)
      expect(employee_packages.first).to eq(first_package)
      expect(employee_packages.last).to eq(second_package)

      # Verify the first package is currently active (since second package is effective tomorrow)
      current_packages = regular_employee.salary_packages.current
      expect(current_packages.count).to eq(1)
      expect(current_packages.first).to eq(first_package)

      # Verify that tomorrow, the second package will be current
      future_current_packages = regular_employee.salary_packages.current(Date.current + 1.month)
      expect(future_current_packages.count).to eq(1)
      expect(future_current_packages.first).to eq(second_package)

      # Verify the approval workflow was simulated correctly
      # (Note: We're using mocked approval system, so no real ApprovalRequest records are created)
      # The important thing is that the packages went through the approval lifecycle correctly
    end

    it "reproduces bug: second package fails to transition to approved state" do
      Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).returns(true)

      # Step 1: Create and fully approve first package
      post "/api/finance/salary_packages", params: {
        salary_package: {
          employee_id: regular_employee.id,
          base_salary: 5000,
          transportation_allowance: 500,
          other_allowances: 300,
          effective_date: Date.current,
          notes: "First package"
        }
      }, headers: { 'Authorization' => 'Bearer test-token' }

      expect(response).to have_http_status(:created)
      first_package = SalaryPackage.last
      expect(first_package.status).to eq("draft")

      # Submit first package for approval
      patch "/api/finance/salary_packages/#{first_package.id}/submit", params: {
        employee_id: regular_employee.id
      }, headers: { 'Authorization' => 'Bearer test-token' }

      expect(response).to have_http_status(:ok)
      first_package.reload
      expect(first_package.status).to eq("pending_approval")

      # Fully approve first package
      first_package.on_approval_status_change("approved", "pending_approval")
      first_package.reload
      expect(first_package.status).to eq("approved")
      puts "✅ First package successfully approved: #{first_package.status}"

      # Step 2: Create and fully approve second package
      post "/api/finance/salary_packages", params: {
        salary_package: {
          employee_id: regular_employee.id,
          base_salary: 6000,
          transportation_allowance: 600,
          other_allowances: 400,
          effective_date: Date.current + 1.month,
          notes: "Second package"
        }
      }, headers: { 'Authorization' => 'Bearer test-token' }

      expect(response).to have_http_status(:created)
      second_package = SalaryPackage.last
      expect(second_package.status).to eq("draft")

      # Submit second package for approval
      patch "/api/finance/salary_packages/#{second_package.id}/submit", params: {
        employee_id: regular_employee.id
      }, headers: { 'Authorization' => 'Bearer test-token' }

      expect(response).to have_http_status(:ok)
      second_package.reload
      expect(second_package.status).to eq("pending_approval")

      # Attempt to fully approve second package
      puts "🔄 Attempting to approve second package..."
      puts "Second package before approval: status=#{second_package.status}, id=#{second_package.id}"

      second_package.on_approval_status_change("approved", "pending_approval")
      second_package.reload

      puts "Second package after approval callback: status=#{second_package.status}"

      # This is where the bug manifests - the second package should be approved but might not be
      if second_package.status != "approved"
        puts "🐛 BUG REPRODUCED: Second package failed to transition to approved state!"
        puts "Expected: approved, Got: #{second_package.status}"

        # Let's check if there were any errors or issues
        puts "Second package errors: #{second_package.errors.full_messages}" if second_package.errors.any?
        puts "First package status after second approval: #{first_package.reload.status}"
        puts "First package end_date: #{first_package.end_date}"
      else
        puts "✅ Second package successfully approved: #{second_package.status}"
      end

      # The test expectation that should pass but might fail due to the bug
      expect(second_package.status).to eq("approved"),
        "Second package should be approved but is #{second_package.status}. This indicates a bug in the approval workflow."

      # Verify both packages are approved
      expect(first_package.reload.status).to eq("approved")
      expect(second_package.status).to eq("approved")

      # Verify automatic end dating worked
      expect(first_package.end_date).to eq(second_package.effective_date - 1.day)
    end

    it "tests sequential package creation following proper business workflow" do
      Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).returns(true)

      puts "🧪 Testing sequential package creation with proper workflow..."

      # Step 1: Create, submit, and approve first package
      first_package = create(:salary_package,
        employee: regular_employee,
        created_by: hr_manager,
        status: :draft,
        effective_date: Date.current
      )

      first_package.submit!
      expect(first_package.status).to eq("pending_approval")

      first_package.on_approval_status_change("approved", "pending_approval")
      first_package.reload
      expect(first_package.status).to eq("approved")
      puts "✅ First package approved successfully"

      # Step 2: Now create second package (should work since first is no longer draft)
      second_package = create(:salary_package,
        employee: regular_employee,
        created_by: hr_manager,
        status: :draft,
        effective_date: Date.current + 1.month
      )

      second_package.submit!
      expect(second_package.status).to eq("pending_approval")
      puts "✅ Second package submitted successfully"

      # Step 3: Approve second package - this is where the bug might manifest
      puts "🔄 Attempting to approve second package..."
      puts "Before approval: second_package.status = #{second_package.status}"
      puts "Before approval: first_package.end_date = #{first_package.reload.end_date}"

      second_package.on_approval_status_change("approved", "pending_approval")
      second_package.reload

      puts "After approval callback: second_package.status = #{second_package.status}"
      puts "After approval callback: first_package.end_date = #{first_package.reload.end_date}"

      if second_package.status != "approved"
        puts "🐛 BUG REPRODUCED: Second package failed to transition to approved!"
        puts "Expected: approved, Got: #{second_package.status}"
        puts "Second package errors: #{second_package.errors.full_messages}" if second_package.errors.any?

        # Check if the issue is in the approve_and_activate method
        puts "Checking approve_and_activate method..."
        begin
          second_package.send(:approve_and_activate)
          second_package.reload
          puts "After manual approve_and_activate: #{second_package.status}"
        rescue => e
          puts "Error in approve_and_activate: #{e.message}"
          puts "Backtrace: #{e.backtrace.first(3)}"
        end
      else
        puts "✅ Second package approved successfully"
      end

      expect(second_package.status).to eq("approved")

      # Step 3: Test third package to see if the pattern continues
      third_package = create(:salary_package,
        employee: regular_employee,
        created_by: hr_manager,
        status: :draft,
        effective_date: Date.current + 2.months
      )

      third_package.submit!
      expect(third_package.status).to eq("pending_approval")

      puts "🔄 Attempting to approve third package..."
      third_package.on_approval_status_change("approved", "pending_approval")
      third_package.reload

      if third_package.status != "approved"
        puts "🐛 BUG REPRODUCED: Third package also failed to transition to approved!"
        puts "This suggests a systematic issue with subsequent package approvals"
      end

      expect(third_package.status).to eq("approved")
      puts "✅ All packages approved successfully - no bug reproduced in test environment"
    end

    it "tests for potential transaction and database-level issues during approval" do
      Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).returns(true)

      puts "🧪 Testing for transaction and database-level issues..."

      # Create and approve first package
      first_package = create(:salary_package,
        employee: regular_employee,
        created_by: hr_manager,
        status: :approved,  # Start with approved to skip the workflow
        effective_date: Date.current
      )

      # Create second package
      second_package = create(:salary_package,
        employee: regular_employee,
        created_by: hr_manager,
        status: :pending_approval,  # Start in pending state
        effective_date: Date.current + 1.month
      )

      puts "Initial state:"
      puts "  First package: #{first_package.status}, end_date: #{first_package.end_date}"
      puts "  Second package: #{second_package.status}, end_date: #{second_package.end_date}"

      # Test the approve_and_activate method directly
      puts "🔄 Testing approve_and_activate method directly..."

      begin
        # This should:
        # 1. Update second package status to approved
        # 2. Handle overlapping packages (set end_date on first package)
        second_package.send(:approve_and_activate)
        second_package.reload
        first_package.reload

        puts "After approve_and_activate:"
        puts "  First package: #{first_package.status}, end_date: #{first_package.end_date}"
        puts "  Second package: #{second_package.status}, end_date: #{second_package.end_date}"

        if second_package.status != "approved"
          puts "🐛 BUG FOUND: approve_and_activate failed to set status to approved!"
          puts "Second package errors: #{second_package.errors.full_messages}" if second_package.errors.any?
        end

        if first_package.end_date.nil?
          puts "🐛 POTENTIAL ISSUE: First package end_date not set during overlapping package handling"
        end

        expect(second_package.status).to eq("approved")
        expect(first_package.end_date).to eq(second_package.effective_date - 1.day)

      rescue => e
        puts "🐛 EXCEPTION in approve_and_activate: #{e.message}"
        puts "Backtrace: #{e.backtrace.first(5)}"
        raise e
      end

      # Test the full callback chain
      puts "🔄 Testing full on_approval_status_change callback..."

      third_package = create(:salary_package,
        employee: regular_employee,
        created_by: hr_manager,
        status: :pending_approval,
        effective_date: Date.current + 2.months
      )

      begin
        third_package.on_approval_status_change("approved", "pending_approval")
        third_package.reload

        if third_package.status != "approved"
          puts "🐛 BUG FOUND: on_approval_status_change callback failed!"
          puts "Third package status: #{third_package.status}"
          puts "Third package errors: #{third_package.errors.full_messages}" if third_package.errors.any?
        end

        expect(third_package.status).to eq("approved")

      rescue => e
        puts "🐛 EXCEPTION in on_approval_status_change: #{e.message}"
        puts "Backtrace: #{e.backtrace.first(5)}"
        raise e
      end

      puts "✅ All transaction and database tests passed"
    end

    it "tests the REAL approval workflow with actual approval steps and actions" do
      Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).returns(true)

      puts "🧪 Testing REAL approval workflow with actual steps..."

      # Create approvers
      financial_manager = create(:employee, department: :finance)
      hr_manager_approver = create(:employee, department: :hr)

      # Use a simpler approach - let the global mock work but create approval requests manually
      # Don't override the global mock, just let it work normally

      # Step 1: Create and submit first package
      first_package = create(:salary_package,
        employee: regular_employee,
        created_by: hr_manager,
        status: :draft,
        effective_date: Date.current
      )

      puts "🔄 Setting actor_user_id and calling submit! on first package..."
      first_package.actor_user_id = hr_manager.user_id
      puts "Set actor_user_id to: #{first_package.actor_user_id}"

      first_package.submit!
      expect(first_package.status).to eq("pending_approval")
      puts "✅ First package submitted successfully"

      # Manually create the approval request with steps (simulating what the real workflow would do)
      first_approval_request = ApprovalRequest.create!(
        workflow_id: "salary_package_approval_workflow",
        workflow_name: "Salary Package Approval",
        requestor_id: hr_manager.id,
        approvable: first_package,
        status: :pending,
        steps_data: [
          {
            'step_id' => '1',
            'name' => 'Financial Manager Approval',
            'sequence' => 1,
            'approval_type' => 'any',
            'approver_ids' => [ financial_manager.user_id.to_s ]
          },
          {
            'step_id' => '2',
            'name' => 'HR Manager Approval',
            'sequence' => 2,
            'approval_type' => 'any',
            'approver_ids' => [ hr_manager_approver.user_id.to_s ]
          }
        ]
      )

      # The association should already be set through the polymorphic relationship
      # Let's verify it works
      first_package.reload

      expect(first_approval_request.approval_steps.count).to eq(2)

      step1 = first_approval_request.approval_steps.find_by(sequence: 1)
      step2 = first_approval_request.approval_steps.find_by(sequence: 2)

      expect(step1.name).to eq("Financial Manager Approval")
      expect(step2.name).to eq("HR Manager Approval")

      puts "✅ First package submitted with approval steps created"

      # Step 2: Process approval steps for first package
      puts "🔄 Processing approval steps for first package..."

      # Financial manager approves (step 1)
      ApprovalAction.create!(
        approval_request: first_approval_request,
        approval_step: step1,
        user_id: financial_manager.user_id.to_s,
        action: "approve",
        comment: "Financial approval granted"
      )

      expect(step1.complete?).to be true
      expect(step2.complete?).to be false
      puts "  ✅ Step 1 (Financial) completed"

      # HR manager approves (step 2)
      ApprovalAction.create!(
        approval_request: first_approval_request,
        approval_step: step2,
        user_id: hr_manager_approver.user_id.to_s,
        action: "approve",
        comment: "HR approval granted"
      )

      expect(step2.complete?).to be true
      puts "  ✅ Step 2 (HR) completed"

      # Check if all steps are complete
      all_steps_complete = first_approval_request.approval_steps.all?(&:complete?)
      expect(all_steps_complete).to be true
      puts "  ✅ All steps completed"

      # Update approval request status and trigger callback
      first_approval_request.update!(status: :approved)
      first_package.on_approval_status_change("approved", "pending_approval")
      first_package.reload

      expect(first_package.status).to eq("approved")
      puts "✅ First package fully approved through real workflow"

      # Step 3: Create and submit second package
      second_package = create(:salary_package,
        employee: regular_employee,
        created_by: hr_manager,
        status: :draft,
        effective_date: Date.current + 1.month
      )

      second_package.actor_user_id = hr_manager.user_id
      second_package.submit!
      expect(second_package.status).to eq("pending_approval")

      # Manually create the approval request for second package
      second_approval_request = ApprovalRequest.create!(
        workflow_id: "salary_package_approval_workflow",
        workflow_name: "Salary Package Approval",
        requestor_id: hr_manager.id,
        approvable: second_package,
        status: :pending,
        steps_data: [
          {
            'step_id' => '1',
            'name' => 'Financial Manager Approval',
            'sequence' => 1,
            'approval_type' => 'any',
            'approver_ids' => [ financial_manager.user_id.to_s ]
          },
          {
            'step_id' => '2',
            'name' => 'HR Manager Approval',
            'sequence' => 2,
            'approval_type' => 'any',
            'approver_ids' => [ hr_manager_approver.user_id.to_s ]
          }
        ]
      )

      # The association should already be set through the polymorphic relationship
      second_package.reload

      puts "✅ Second package submitted with approval workflow"

      # Step 4: Process approval steps for second package
      puts "🔄 Processing approval steps for second package..."

      second_step1 = second_approval_request.approval_steps.find_by(sequence: 1)
      second_step2 = second_approval_request.approval_steps.find_by(sequence: 2)

      # Financial manager approves
      ApprovalAction.create!(
        approval_request: second_approval_request,
        approval_step: second_step1,
        user_id: financial_manager.user_id.to_s,
        action: "approve",
        comment: "Second package financial approval"
      )

      # HR manager approves
      ApprovalAction.create!(
        approval_request: second_approval_request,
        approval_step: second_step2,
        user_id: hr_manager_approver.user_id.to_s,
        action: "approve",
        comment: "Second package HR approval"
      )

      # Check if all steps are complete
      all_second_steps_complete = second_approval_request.approval_steps.all?(&:complete?)
      expect(all_second_steps_complete).to be true

      # Update approval request status and trigger callback
      second_approval_request.update!(status: :approved)

      puts "🔄 Triggering approval callback for second package..."
      puts "Before callback: second_package.status = #{second_package.status}"
      puts "Before callback: first_package.status = #{first_package.reload.status}"
      puts "Before callback: first_package.end_date = #{first_package.end_date}"

      begin
        second_package.on_approval_status_change("approved", "pending_approval")
        second_package.reload
        puts "After callback: second_package.status = #{second_package.status}"
      rescue => e
        puts "🐛 BUG REPRODUCED: Exception during approval callback!"
        puts "Exception: #{e.class}: #{e.message}"
        puts "Second package errors: #{second_package.errors.full_messages}" if second_package.errors.any?
        puts "Backtrace: #{e.backtrace.first(5)}"

        # Check the state of related records
        puts "Debug info:"
        puts "  Second approval request status: #{second_approval_request.reload.status}"
        puts "  First package status: #{first_package.reload.status}"
        puts "  First package end_date: #{first_package.end_date}"
        puts "  Second package status: #{second_package.reload.status}"

        # Re-raise to see the full error
        raise e
      end

      if second_package.status != "approved"
        puts "🐛 BUG REPRODUCED: Second package failed to approve with real workflow!"
        puts "Second package errors: #{second_package.errors.full_messages}" if second_package.errors.any?
      else
        puts "✅ Second package approved successfully with real workflow"
      end

      expect(second_package.status).to eq("approved")

      # Verify automatic end dating worked
      expect(first_package.reload.end_date).to eq(second_package.effective_date - 1.day)

      puts "✅ Real approval workflow test completed successfully"
    end

    it "reproduces and fixes the exact bug: validation failure when approving packages with same effective date" do
      Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).returns(true)

      puts "🧪 Testing the exact bug scenario: same effective date validation conflict..."

      # Create approvers
      financial_manager = create(:employee, department: :finance)

      # Step 1: Create and approve first package
      first_package = create(:salary_package,
        employee: regular_employee,
        created_by: hr_manager,
        status: :draft,
        effective_date: Date.current  # Same date as second package
      )

      first_package.actor_user_id = hr_manager.user_id
      first_package.submit!
      expect(first_package.status).to eq("pending_approval")

      # Create approval workflow for first package
      first_approval_request = ApprovalRequest.create!(
        workflow_id: "salary_package_approval_workflow",
        workflow_name: "Salary Package Approval",
        requestor_id: hr_manager.id,
        approvable: first_package,
        status: :pending,
        steps_data: [
          {
            'step_id' => '1',
            'name' => 'Financial Manager Approval',
            'sequence' => 1,
            'approval_type' => 'any',
            'approver_ids' => [ financial_manager.user_id.to_s ]
          }
        ]
      )

      # Approve first package
      step1 = first_approval_request.approval_steps.first
      ApprovalAction.create!(
        approval_request: first_approval_request,
        approval_step: step1,
        user_id: financial_manager.user_id.to_s,
        action: "approve",
        comment: "First package approved"
      )

      first_approval_request.update!(status: :approved)
      first_package.on_approval_status_change("approved", "pending_approval")
      first_package.reload
      expect(first_package.status).to eq("approved")
      puts "✅ First package approved successfully"

      # Step 2: Create second package with SAME effective date (this triggers the bug scenario)
      second_package = create(:salary_package,
        employee: regular_employee,
        created_by: hr_manager,
        status: :draft,
        effective_date: Date.current  # SAME DATE - this would cause validation error in old code
      )

      second_package.actor_user_id = hr_manager.user_id
      second_package.submit!
      expect(second_package.status).to eq("pending_approval")

      # Create approval workflow for second package
      second_approval_request = ApprovalRequest.create!(
        workflow_id: "salary_package_approval_workflow",
        workflow_name: "Salary Package Approval",
        requestor_id: hr_manager.id,
        approvable: second_package,
        status: :pending,
        steps_data: [
          {
            'step_id' => '1',
            'name' => 'Financial Manager Approval',
            'sequence' => 1,
            'approval_type' => 'any',
            'approver_ids' => [ financial_manager.user_id.to_s ]
          }
        ]
      )

      # Approve second package
      second_step1 = second_approval_request.approval_steps.first
      ApprovalAction.create!(
        approval_request: second_approval_request,
        approval_step: second_step1,
        user_id: financial_manager.user_id.to_s,
        action: "approve",
        comment: "Second package approved"
      )

      second_approval_request.update!(status: :approved)

      puts "🔄 Attempting to approve second package with same effective date..."
      puts "Before approval:"
      puts "  First package: effective_date=#{first_package.effective_date}, status=#{first_package.status}, end_date=#{first_package.end_date}"
      puts "  Second package: effective_date=#{second_package.effective_date}, status=#{second_package.status}, end_date=#{second_package.end_date}"

      begin
        # This should now work with our fix (handle_overlapping_packages called first)
        second_package.on_approval_status_change("approved", "pending_approval")
        second_package.reload
        first_package.reload

        puts "After approval:"
        puts "  First package: effective_date=#{first_package.effective_date}, status=#{first_package.status}, end_date=#{first_package.end_date}"
        puts "  Second package: effective_date=#{second_package.effective_date}, status=#{second_package.status}, end_date=#{second_package.end_date}"

        # Verify the fix worked
        expect(second_package.status).to eq("approved")
        expect(first_package.status).to eq("cancelled") # First package should be cancelled when same effective date
        expect(first_package.end_date).to be_nil # Cancelled packages don't need end_date

        puts "✅ BUG FIXED: Second package approved successfully with same effective date!"
        puts "✅ First package automatically cancelled to avoid conflict"

      rescue ActiveRecord::RecordInvalid => e
        puts "🐛 BUG STILL EXISTS: Validation error during approval!"
        puts "Exception: #{e.class}: #{e.message}"
        puts "This means our fix didn't work"

        # If this happens, our fix didn't work
        expect(second_package.reload.status).to eq("pending_approval")
        raise e

      rescue => e
        puts "🐛 Different exception occurred: #{e.class}: #{e.message}"
        puts "Backtrace: #{e.backtrace.first(3)}"
        raise e
      end
    end
  end

  describe "Salary Package Permissions" do
    it "handles rejection and resubmission workflow" do
      # Create and submit package
      Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).returns(true)

      package = create(:salary_package,
                       employee: regular_employee,
                       status: :draft,
                       actor_user_id: hr_manager.user_id
      )

      # Submit for approval
      patch "/api/finance/salary_packages/#{package.id}/submit", params: {
        employee_id: regular_employee.id
      }

      expect(response).to have_http_status(:ok)
      package.reload
      expect(package.status).to eq("pending_approval")

      # Simulate rejection (would normally come from approval system)
      package.update!(status: :rejected)

      # Edit rejected package
      patch "/api/finance/salary_packages/#{package.id}", params: {
        employee_id: regular_employee.id,
        salary_package: { base_salary: 5500, notes: "Adjusted after feedback" }
      }

      expect(response).to have_http_status(:ok)
      package.reload
      expect(package.base_salary).to eq(5500)
      expect(package.notes).to include("Adjusted after feedback")

      # Resubmit after editing
      package.update!(status: :draft) # Reset to draft for resubmission

      patch "/api/finance/salary_packages/#{package.id}/submit", params: {
        employee_id: regular_employee.id
      }

      expect(response).to have_http_status(:ok)
      package.reload
      expect(package.status).to eq("pending_approval")
    end
  end

  describe "permission-based access control" do
    context "when HR manager accesses system" do
      it "can create packages for others" do
        Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).returns(true)

        post "/api/finance/salary_packages", params: {
          employee_id: regular_employee.id,
          salary_package: { base_salary: 5000, effective_date: Date.current + 1.month }
        }

        expect(response).to have_http_status(:created)
        package = SalaryPackage.last
        expect(package.created_by_id).to eq(hr_manager.id)
      end

      it "can submit packages for others" do
        package = create(:salary_package, employee: regular_employee, status: :draft)
        Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).returns(true)

        patch "/api/finance/salary_packages/#{package.id}/submit", params: {
          employee_id: regular_employee.id
        }

        expect(response).to have_http_status(:ok)
      end

      it "can view all packages" do
        create(:salary_package, employee: regular_employee, status: :approved)
        create(:salary_package, employee: financial_manager, status: :draft)

        Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).with(:read, :salary_package).returns(true)

        get "/api/finance/salary_packages"

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['data'].length).to eq(2)
      end
    end

    context "when financial manager accesses system" do
      before do
        ApplicationController.any_instance.stubs(:current_user).returns(mock(id: financial_manager.user_id))
        ApplicationController.any_instance.stubs(:current_employee).returns(financial_manager)
      end

      it "cannot create packages (permission removed)" do
        Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).with(:create, :salary_package).returns(false)
        Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).with(:create_others, :salary_package).returns(false)

        post "/api/finance/salary_packages", params: {
          employee_id: regular_employee.id,
          salary_package: { base_salary: 5000, effective_date: Date.current + 1.month }
        }

        expect(response).to have_http_status(:forbidden)
      end

      it "can view approved and pending packages but not drafts" do
        approved = create(:salary_package, :approved, employee: regular_employee, created_by: hr_manager)
        pending = create(:salary_package, :pending_approval, employee: regular_employee, created_by: hr_manager)
        draft = create(:salary_package, :draft, employee: regular_employee, created_by: hr_manager)

        Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).with(:read, :salary_package).returns(true)
        Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).with(:manage_others, :salary_package).returns(false)

        get "/api/finance/salary_packages"

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)

        package_ids = json_response['data'].map { |p| p['id'].to_i }
        expect(package_ids).to include(approved.id, pending.id)
        expect(package_ids).not_to include(draft.id)
      end
    end

    context "when regular employee accesses system" do
      before do
        ApplicationController.any_instance.stubs(:current_user).returns(mock(id: regular_employee.user_id))
        ApplicationController.any_instance.stubs(:current_employee).returns(regular_employee)
      end

      it "cannot create packages" do
        Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).returns(false)

        post "/api/finance/salary_packages", params: {
          employee_id: regular_employee.id,
          salary_package: { base_salary: 5000, effective_date: Date.current + 1.month }
        }

        expect(response).to have_http_status(:forbidden)
      end

      it "cannot submit packages" do
        package = create(:salary_package, employee: regular_employee, status: :draft)
        Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).returns(false)

        patch "/api/finance/salary_packages/#{package.id}/submit", params: {
          employee_id: regular_employee.id
        }

        expect(response).to have_http_status(:forbidden)
      end

      it "can only view own approved packages" do
        own_approved = create(:salary_package, employee: regular_employee, status: :approved)
        own_draft = create(:salary_package, employee: regular_employee, status: :draft)
        other_approved = create(:salary_package, employee: hr_manager, status: :approved)

        Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).with(:read, :salary_package).returns(false)
        Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).with(:read_own, :salary_package).returns(true)

        get "/api/finance/salary_packages", params: {
          salary_package: { employee_id: regular_employee.id }
        }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)

        # Should only see own approved packages, not drafts or others' packages
        expect(json_response['data'].length).to eq(1)
        expect(json_response['data'][0]['id']).to eq(own_approved.id.to_s)
      end
    end
  end

  describe "creator-specific draft workflow" do
    before do
      Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).returns(true)
    end

    it "allows multiple creators to have drafts for same employee" do
      # HR manager creates draft
      ApplicationController.any_instance.stubs(:current_employee).returns(hr_manager)
      ApplicationController.any_instance.stubs(:current_user).returns(mock(id: hr_manager.user_id))

      post "/api/finance/salary_packages", params: {
        employee_id: regular_employee.id,
        salary_package: { base_salary: 5000, effective_date: Date.current + 1.month }
      }

      expect(response).to have_http_status(:created)
      hr_draft = SalaryPackage.last

      # Admin creates another draft for same employee
      ApplicationController.any_instance.stubs(:current_employee).returns(admin_user)
      ApplicationController.any_instance.stubs(:current_user).returns(mock(id: admin_user.user_id))

      post "/api/finance/salary_packages", params: {
        employee_id: regular_employee.id,
        salary_package: { base_salary: 6000, effective_date: Date.current + 2.months }
      }

      expect(response).to have_http_status(:created)
      admin_draft = SalaryPackage.last

      # Verify both drafts exist
      expect(hr_draft.created_by_id).to eq(hr_manager.id)
      expect(admin_draft.created_by_id).to eq(admin_user.id)
      expect(hr_draft.employee_id).to eq(admin_draft.employee_id)
    end

    it "auto-cancels competing drafts when one is submitted" do
      # Create drafts by different creators
      hr_draft = create(:salary_package, :draft, employee: regular_employee, created_by: hr_manager)
      admin_draft = create(:salary_package, :draft, employee: regular_employee, created_by: admin_user)

      # HR manager submits their draft
      ApplicationController.any_instance.stubs(:current_employee).returns(hr_manager)
      ApplicationController.any_instance.stubs(:current_user).returns(mock(id: hr_manager.user_id))

      patch "/api/finance/salary_packages/#{hr_draft.id}/submit", params: {
        employee_id: regular_employee.id
      }

      expect(response).to have_http_status(:ok)

      # Check results
      hr_draft.reload
      admin_draft.reload

      expect(hr_draft.status).to eq("pending_approval")
      expect(admin_draft.status).to eq("cancelled")
      expect(admin_draft.cancellation_reason).to include("Superseded by package ##{hr_draft.id}")
    end

    it "prevents new drafts when package is pending approval" do
      # Create and submit a package
      pending_package = create(:salary_package, :pending_approval, employee: regular_employee, created_by: hr_manager)

      # Try to create new draft while pending exists
      ApplicationController.any_instance.stubs(:current_employee).returns(admin_user)
      ApplicationController.any_instance.stubs(:current_user).returns(mock(id: admin_user.user_id))

      post "/api/finance/salary_packages", params: {
        employee_id: regular_employee.id,
        salary_package: { base_salary: 6000, effective_date: Date.current + 1.month }
      }

      expect(response).to have_http_status(:unprocessable_entity)
      json_response = JSON.parse(response.body)
      expect(json_response['errors'].first['detail']).to include("already pending approval")
    end
  end

  describe "error handling and edge cases" do
    before do
      Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).returns(true)
    end

    it "handles validation errors gracefully" do
      post "/api/finance/salary_packages", params: {
        employee_id: regular_employee.id,
        salary_package: {
          base_salary: -1000, # Invalid negative salary
          effective_date: nil # Missing required field
        }
      }

      expect(response).to have_http_status(:unprocessable_entity)
      json_response = JSON.parse(response.body)
      expect(json_response['errors']).to be_present
      expect(json_response['errors'].first['detail']).to include('must be greater than 0')
    end

    it "prevents duplicate draft packages" do
      create(:salary_package, employee: regular_employee, status: :draft)

      post "/api/finance/salary_packages", params: {
        employee_id: regular_employee.id,
        salary_package: {
          base_salary: 5000,
          effective_date: Date.current + 1.month
        }
      }

      expect(response).to have_http_status(:unprocessable_entity)
      json_response = JSON.parse(response.body)
      expect(json_response['errors'].first['detail']).to include('one draft salary package')
    end

    it "handles non-existent package gracefully" do
      get "/api/finance/salary_packages/99999", params: {
        employee_id: regular_employee.id
      }

      expect(response).to have_http_status(:not_found)
    end

    it "validates package belongs to specified employee" do
      package = create(:salary_package, employee: regular_employee, status: :draft)

      patch "/api/finance/salary_packages/#{package.id}/submit", params: {
        employee_id: hr_manager.id # Wrong employee ID
      }

      expect(response).to have_http_status(:unprocessable_entity)
    end
  end

  describe "serializer integration" do
    it "returns enhanced package data with approval information" do
      package = create(:salary_package, employee: regular_employee, status: :pending_approval)
      Api::Finance::SalaryPackagesController.any_instance.stubs(:can?).returns(true)

      get "/api/finance/salary_packages/#{package.id}", params: {
        employee_id: regular_employee.id
      }

      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)

      attributes = json_response['data']['attributes']
      expect(attributes).to include(
                              'status',
                              'approval_status',
                              'base_salary',
                              'total_package_value'
                            )

      expect(attributes['approval_status']).to include(
                                                 'status',
                                                 'submittable',
                                                 'editable',
                                                 'has_approval_request'
                                               )
    end
  end
end
