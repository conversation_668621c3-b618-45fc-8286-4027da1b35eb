require 'spec_helper'

RSpec.describe "Export Functionality Simple Test" do
  it "loads the commons gem with export functionality" do
    expect(defined?(Athar::Commons::VERSION)).to be_truthy
    expect(Athar::Commons::VERSION).to eq("0.3.0")
  end

  it "has export services available" do
    expect(defined?(Athar::Commons::Services::Export::ExportFactory)).to be_truthy
    expect(defined?(Athar::Commons::Services::Export::CsvExportService)).to be_truthy
    expect(defined?(Athar::Commons::Services::Export::PdfExportService)).to be_truthy
    expect(defined?(Athar::Commons::Services::Export::XlsxExportService)).to be_truthy
  end

  it "can create export services" do
    test_data = [{ 'id' => 1, 'name' => 'Test' }]
    
    expect {
      Athar::Commons::Services::Export::ExportFactory.create(test_data, :csv)
    }.not_to raise_error
    
    expect {
      Athar::Commons::Services::Export::ExportFactory.create(test_data, :pdf)
    }.not_to raise_error
    
    expect {
      Athar::Commons::Services::Export::ExportFactory.create(test_data, :xlsx)
    }.not_to raise_error
  end

  it "supports all expected export formats" do
    formats = Athar::Commons::Services::Export::ExportFactory.supported_formats
    expect(formats).to include(:csv, :pdf, :xlsx)
  end
end
