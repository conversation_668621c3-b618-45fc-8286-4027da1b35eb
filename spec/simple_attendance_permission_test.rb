require 'spec_helper'
require_relative '../config/environment'

RSpec.describe "Attendance Events Permission Logic" do
  describe "apply_permission_filters method" do
    let(:controller) { Api::Attendance::EventsController.new }

    before do
      # Mock the params method to return an ActionController::Parameters object
      controller.stubs(:params).returns(ActionController::Parameters.new({}))
    end

    context "when user has full read permission" do
      before do
        controller.stubs(:can?).with(:read, :attendance_event).returns(true)
        controller.stubs(:can?).with(:manage, :attendance_event).returns(false)
      end

      it "does not apply any filters" do
        controller.send(:apply_permission_filters)
        expect(controller.params[:filter]).to be_nil
      end
    end

    context "when user has read_own permission" do
      let(:current_employee) { stub('Employee', id: 123) }

      before do
        controller.stubs(:can?).with(:read, :attendance_event).returns(false)
        controller.stubs(:can?).with(:manage, :attendance_event).returns(false)
        controller.stubs(:can?).with(:read_own, :attendance_event).returns(true)
        controller.stubs(:current_employee).returns(current_employee)
      end

      it "applies employee filter to params" do
        controller.send(:apply_permission_filters)
        expect(controller.params[:filter][:employee_id_eq]).to eq(123)
      end
    end

    context "when user has no permissions" do
      before do
        controller.stubs(:can?).with(:read, :attendance_event).returns(false)
        controller.stubs(:can?).with(:manage, :attendance_event).returns(false)
        controller.stubs(:can?).with(:read_own, :attendance_event).returns(false)
      end

      it "applies empty result filter" do
        controller.send(:apply_permission_filters)
        expect(controller.params[:filter][:employee_id_eq]).to eq(-1)
      end
    end
  end

  describe "is_own_event? method" do
    let(:controller) { Api::Attendance::EventsController.new }
    let(:current_employee) { stub('Employee', id: 123) }
    let(:own_event) { stub('AttendanceEvent', employee_id: 123) }
    let(:other_event) { stub('AttendanceEvent', employee_id: 456) }

    before do
      controller.stubs(:current_employee).returns(current_employee)
    end

    it "returns true for own event" do
      controller.instance_variable_set(:@attendance_event, own_event)
      expect(controller.send(:is_own_event?)).to be true
    end

    it "returns false for other's event" do
      controller.instance_variable_set(:@attendance_event, other_event)
      expect(controller.send(:is_own_event?)).to be false
    end
  end
end
