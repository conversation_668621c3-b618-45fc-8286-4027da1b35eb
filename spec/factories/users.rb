FactoryBot.define do
  factory :user, class: 'OpenStruct' do
    sequence(:id) { |n| n }
    name { Faker::Name.name }
    email { Faker::Internet.unique.email }

    # Don't actually create a record, just return the OpenStruct
    skip_create

    initialize_with { OpenStruct.new(attributes) }

    trait :admin do
      name { 'Admin User' }
      email { '<EMAIL>' }
    end

    trait :employee_role do
      name { 'Employee User' }
      email { '<EMAIL>' }
    end

    trait :hr_role do
      name { 'HR User' }
      email { '<EMAIL>' }
    end

    trait :finance_role do
      name { 'Finance User' }
      email { '<EMAIL>' }
    end

    factory :user_with_role do
      transient do
        role_name { 'employee' }
      end

      after(:build) do |user, evaluator|
        # Add role information to the user object
        user.role = evaluator.role_name
        user.roles = [evaluator.role_name]
      end
    end
  end
end
