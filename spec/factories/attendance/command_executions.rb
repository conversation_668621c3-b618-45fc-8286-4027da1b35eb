FactoryBot.define do
  factory :attendance_command_execution, class: 'Attendance::CommandExecution' do
    association :device, factory: :attendance_device
    association :executed_by, factory: :employee

    command_name { 'test_command' }
    parameters { { param1: 'value1', param2: 'value2' } }
    status { :running }
    started_at { Time.current }
    completed_at { nil }
    result { {} }

    trait :running do
      status { :running }
      completed_at { nil }
    end

    trait :completed do
      status { :completed }
      completed_at { Time.current + 5.minutes }
      result { { success: true, message: 'Command executed successfully' } }
    end

    trait :failed do
      status { :failed }
      completed_at { Time.current + 2.minutes }
      result { { success: false, error: 'Command execution failed' } }
    end

    trait :clear_data do
      command_name { 'clear_data' }
      parameters { { confirm: true } }
    end

    trait :sync_users do
      command_name { 'sync_users' }
      parameters { { force: false } }
    end

    trait :restart_device do
      command_name { 'restart_device' }
      parameters { {} }
    end

    trait :with_long_execution do
      started_at { 10.minutes.ago }
      completed_at { 5.minutes.ago }
    end
  end
end
