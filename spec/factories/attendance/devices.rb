FactoryBot.define do
  factory :attendance_device, class: 'Attendance::Device' do
    sequence(:name) { |n| "Test Device #{n}" }
    adapter_type { :zkteco }
    ip_address { "192.168.100.#{rand(1..254)}" }
    port { 4370 }
    location { "Test Location" }
    status { :active }
    last_seen_at { 1.hour.ago }

    connection_config do
      {
        timeout: 30,
        password: nil
      }
    end

    sync_config do
      {
        sync_interval: 30,
        batch_size: 1000,
        retry_attempts: 3,
        retry_delay: 60,
        auto_sync_enabled: true
      }
    end

    capabilities do
      {
        real_time: true,
        user_management: true,
        clear_data: false
      }
    end

    trait :zkteco do
      adapter_type { :zkteco }
      ip_address { "***************" }
      port { 4370 }
    end

    trait :generic_http do
      adapter_type { :generic_http }
      ip_address { "*************" }
      port { 80 }
      connection_config do
        {
          base_url: "http://*************",
          username: "admin",
          password: "password"
        }
      end
    end

    trait :file_import do
      adapter_type { :file_import }
      ip_address { nil }
      port { nil }
      connection_config do
        {
          file_path: "/tmp/attendance_data.csv"
        }
      end
    end

    trait :inactive do
      status { :inactive }
    end

    trait :maintenance do
      status { :maintenance }
    end

    trait :error do
      status { :error }
    end

    trait :with_old_activity do
      last_seen_at { 2.days.ago }
    end
  end
end
