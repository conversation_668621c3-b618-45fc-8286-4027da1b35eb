FactoryBot.define do
  factory :attendance_exemption, class: 'Attendance::Exemption' do
    name { 'Test Holiday' }
    description { 'Test holiday description' }
    exemption_type { 'holiday' }
    start_date { Date.current }
    end_date { Date.current }
    is_active { true }
    affects_attendance { true }
    affects_salary { false }

    trait :holiday do
      exemption_type { 'holiday' }
      name { 'National Holiday' }
      description { 'National holiday exemption' }
    end

    trait :company_closure do
      exemption_type { 'company_closure' }
      name { 'Company Closure' }
      description { 'Company closure exemption' }
    end

    trait :emergency_closure do
      exemption_type { 'emergency_closure' }
      name { 'Emergency Closure' }
      description { 'Emergency closure exemption' }
    end

    trait :system_maintenance do
      exemption_type { 'system_maintenance' }
      name { 'System Maintenance' }
      description { 'System maintenance exemption' }
    end

    trait :inactive do
      is_active { false }
    end

    trait :affects_salary do
      affects_salary { true }
    end

    trait :multi_day do
      start_date { Date.current }
      end_date { Date.current + 3.days }
    end

    trait :last_week do
      start_date { 1.week.ago }
      end_date { 1.week.ago }
    end

    trait :next_week do
      start_date { 1.week.from_now }
      end_date { 1.week.from_now }
    end
  end
end
