FactoryBot.define do
  factory :attendance_period, class: 'Attendance::Period' do
    association :employee
    date { Date.current }
    period_type { 'work' }
    start_timestamp { Time.current.beginning_of_day.change(hour: 9).to_i }
    end_timestamp { Time.current.beginning_of_day.change(hour: 17).to_i }
    duration_minutes { 480 } # 8 hours
    auto_generated { false }
    is_predicted { false }
    activity_type { 'regular' }
    notes { 'Test period' }

    # Use save(validate: false) to bypass validations in tests
    to_create { |instance| instance.save(validate: false) }

    trait :work do
      period_type { 'work' }
      duration_minutes { 480 } # 8 hours
    end

    trait :leave do
      period_type { 'leave' }
      duration_minutes { 480 } # 8 hours full day
      activity_type { nil } # Leave periods have no activity type
    end

    trait :auto_generated do
      auto_generated { true }
    end

    trait :manual do
      auto_generated { false }
    end

    trait :auto_generated_leave do
      period_type { 'leave' }
      auto_generated { true }
      activity_type { nil }
      notes { 'Auto-generated leave: Insufficient work hours' }
    end

    trait :manual_leave do
      period_type { 'leave' }
      auto_generated { false }
      activity_type { nil }
      notes { 'Manual leave: Approved vacation' }
    end

    trait :late do
      period_type { 'late' }
      duration_minutes { 15 }
      start_timestamp { Time.current.beginning_of_day.change(hour: 9, min: 15).to_i }
      end_timestamp { Time.current.beginning_of_day.change(hour: 9, min: 30).to_i }
    end

    trait :early_departure do
      period_type { 'early_departure' }
      duration_minutes { 30 }
      start_timestamp { Time.current.beginning_of_day.change(hour: 16, min: 30).to_i }
      end_timestamp { Time.current.beginning_of_day.change(hour: 17).to_i }
    end

    trait :early_arrival do
      period_type { 'early_arrival' }
      duration_minutes { 30 }
      start_timestamp { Time.current.beginning_of_day.change(hour: 8, min: 30).to_i }
      end_timestamp { Time.current.beginning_of_day.change(hour: 9).to_i }
    end

    trait :break do
      period_type { 'break' }
      duration_minutes { 60 }
      start_timestamp { Time.current.beginning_of_day.change(hour: 12).to_i }
      end_timestamp { Time.current.beginning_of_day.change(hour: 13).to_i }
    end

    trait :predicted do
      is_predicted { true }
    end

    trait :yesterday do
      date { Date.current.yesterday }
      start_timestamp { Date.current.yesterday.beginning_of_day.change(hour: 9).to_i }
      end_timestamp { Date.current.yesterday.beginning_of_day.change(hour: 17).to_i }
    end

    trait :last_week do
      date { 1.week.ago.to_date }
      start_timestamp { 1.week.ago.beginning_of_day.change(hour: 9).to_i }
      end_timestamp { 1.week.ago.beginning_of_day.change(hour: 17).to_i }
    end

    # Specific duration traits
    trait :short_work do
      duration_minutes { 240 } # 4 hours
      end_timestamp { Time.current.beginning_of_day.change(hour: 13).to_i }
    end

    trait :long_work do
      duration_minutes { 600 } # 10 hours
      end_timestamp { Time.current.beginning_of_day.change(hour: 19).to_i }
    end

    trait :partial_day do
      duration_minutes { 300 } # 5 hours exactly (threshold)
      end_timestamp { Time.current.beginning_of_day.change(hour: 14).to_i }
    end

    trait :insufficient_hours do
      duration_minutes { 240 } # 4 hours (< 5 hour threshold)
      end_timestamp { Time.current.beginning_of_day.change(hour: 13).to_i }
    end
  end
end
