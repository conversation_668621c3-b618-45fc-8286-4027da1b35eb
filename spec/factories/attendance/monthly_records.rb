FactoryBot.define do
  factory :attendance_monthly_record, class: 'Attendance::MonthlyRecord' do
    association :employee
    year { Date.current.year }
    month { Date.current.month }
    expected_hours { 180.0 }
    actual_hours { 144.0 }
    deficit_hours { 36.0 }

    trait :current_month do
      year { Date.current.year }
      month { Date.current.month }
    end

    trait :last_month do
      year { Date.current.last_month.year }
      month { Date.current.last_month.month }
    end

    trait :full_attendance do
      actual_hours { 180.0 }
      deficit_hours { 0.0 }
    end

    trait :no_attendance do
      actual_hours { 0.0 }
      deficit_hours { 180.0 }
    end

    trait :partial_attendance do
      actual_hours { 90.0 }
      deficit_hours { 90.0 }
    end

    trait :july_2024 do
      year { 2024 }
      month { 7 }
    end

    trait :august_2024 do
      year { 2024 }
      month { 8 }
    end
  end
end
