FactoryBot.define do
  factory :employee do
    # Use transient attributes for mock data that will be stubbed
    transient do
      mock_name { Faker::Name.name }
      mock_email { Faker::Internet.unique.email }
    end

    department { Employee::DEPARTMENT_LABELS.keys.sample.to_s }
    start_date { Date.today - rand(1..365).days }
    phone { '+962790000000' }
    exempt_from_attendance_deductions { false }

    # Mock user_id to avoid RPC calls during tests
    user_id { rand(1..100) }
    status { :active }

    trait :with_user_roles do
      user_roles_list do
        [
          {
            'role' => {
              'id' => '1',
              'name' => 'employee',
              'global' => false
            },
            'project' => {
              'id' => '1',
              'name' => 'Project Alpha'
            },
            'is_default' => true
          }
        ]
      end
    end

    factory :employee_with_user_roles, traits: [ :with_user_roles ]
  end
end
