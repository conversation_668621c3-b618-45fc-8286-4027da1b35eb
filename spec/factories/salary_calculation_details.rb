FactoryBot.define do
  factory :salary_calculation_detail, class: 'SalaryCalculation::Detail' do
    association :salary_calculation
    detail_type { 'deduction' }
    category { 'leave_unpaid' }
    amount { 100.0 }
    description { 'Test salary calculation detail' }

    trait :base_salary do
      detail_type { 'base' }
      category { 'base_salary' }
      amount { 5000.0 }
      description { 'Base salary' }
    end

    trait :deduction do
      detail_type { 'deduction' }
      category { 'leave_unpaid' }
      amount { 100.0 }
      description { 'Leave deduction' }
    end

    trait :addition do
      detail_type { 'addition' }
      category { 'overtime' }
      amount { 200.0 }
      description { 'Overtime payment' }
    end

    trait :attendance_based do
      detail_type { 'deduction' }
      category { 'leave_attendance_based' }
      amount { 150.0 }
      description { 'Attendance-based leave deduction' }
    end
  end
end
