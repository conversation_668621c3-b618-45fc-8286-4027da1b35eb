FactoryBot.define do
  factory :salary_package do
    association :employee
    association :created_by, factory: :employee

    base_salary { 5000 }
    transportation_allowance { 500 }
    other_allowances { 300 }
    effective_date { Date.current + 1.month }
    status { :draft }
    notes { "Test salary package" }

    # Set actor_user_id to created_by's user_id by default to ensure consistency
    after(:build) do |salary_package|
      # Only set actor_user_id if it's not already set and ensure it's different from employee's user_id
      if salary_package.actor_user_id.blank? && salary_package.created_by&.user_id != salary_package.employee&.user_id
        salary_package.actor_user_id = salary_package.created_by&.user_id
      end
      # Ensure employee_id is set if employee association exists
      if salary_package.employee&.persisted?
        salary_package.employee_id = salary_package.employee.id
      end
    end

    trait :draft do
      status { :draft }
    end

    trait :pending_approval do
      status { :pending_approval }
      submitted_at { Time.current }
    end

    trait :approved do
      status { :approved }
      submitted_at { 1.week.ago }
    end

    trait :rejected do
      status { :rejected }
      submitted_at { 1.week.ago }
    end

    trait :cancelled do
      status { :cancelled }
      cancelled_at { Time.current }
      cancellation_reason { "Test cancellation" }
    end

    trait :auto_cancelled do
      status { :cancelled }
      cancelled_at { Time.current }
      cancellation_reason { "Superseded by package #123 submitted by Test User" }
    end

    trait :high_salary do
      base_salary { 10000 }
      transportation_allowance { 1000 }
      other_allowances { 500 }
    end

    trait :low_salary do
      base_salary { 3000 }
      transportation_allowance { 200 }
      other_allowances { 100 }
    end

    trait :current_effective_date do
      effective_date { Date.current }
    end

    trait :past_effective_date do
      effective_date { Date.current - 3.months }
    end

    trait :future_effective_date do
      effective_date { Date.current + 3.months }
    end

    trait :with_approval_request do
      after(:create) do |salary_package|
        # Create a mock approval request association
        # Note: This would need to be adjusted based on actual ApprovalRequest factory
        salary_package.approval_request = build(:approval_request, approvable: salary_package)
      end
    end

    # Specific role-based packages
    trait :for_hr_employee do
      association :employee, department: :hr
    end

    trait :for_finance_employee do
      association :employee, department: :finance
    end

    trait :for_it_employee do
      association :employee, department: :it
    end

    trait :for_admin_employee do
      association :employee, department: :admin
    end
  end
end
