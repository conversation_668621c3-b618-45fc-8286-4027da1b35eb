FactoryBot.define do
  factory :salary_calculation do
    association :employee
    association :salary_package, :approved

    period { "2025-07" }
    period_start_date { Date.new(2025, 7, 1) }
    period_end_date { Date.new(2025, 7, 31) }
    gross_salary { 5000 }
    net_salary { 4500 }
    status { :draft }
    deductions { { 'income_tax' => 300, 'social_security' => 200 } }

    trait :draft do
      status { :draft }
    end

    trait :submitted do
      status { :submitted }
    end

    trait :approved do
      status { :approved }
    end

    trait :paid do
      status { :paid }
    end

    trait :rejected do
      status { :rejected }
    end

    # Different periods
    trait :july_2025 do
      period { "2025-07" }
      period_start_date { Date.new(2025, 7, 1) }
      period_end_date { Date.new(2025, 7, 31) }
    end

    trait :june_2025 do
      period { "2025-06" }
      period_start_date { Date.new(2025, 6, 1) }
      period_end_date { Date.new(2025, 6, 30) }
    end

    trait :may_2025 do
      period { "2025-05" }
      period_start_date { Date.new(2025, 5, 1) }
      period_end_date { Date.new(2025, 5, 31) }
    end

    # Different salary amounts
    trait :high_salary do
      gross_salary { 10000 }
      net_salary { 9000 }
    end

    trait :low_salary do
      gross_salary { 3000 }
      net_salary { 2700 }
    end
  end
end
