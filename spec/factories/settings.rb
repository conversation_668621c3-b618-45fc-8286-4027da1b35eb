FactoryBot.define do
  factory :setting do
    namespace { 'attendance' }
    key { 'work_start_time' }
    value { '09:00' }
    setting_type { :string }

    # Use save(validate: false) to bypass validations
    to_create { |instance| instance.save(validate: false) }

    trait :work_start_time do
      key { 'work_start_time' }
      value { '09:00' }
      setting_type { :time }
    end

    trait :work_end_time do
      key { 'work_end_time' }
      value { '17:00' }
      setting_type { :time }
    end

    trait :break_duration do
      key { 'break_duration_minutes' }
      value { '60' }
      setting_type { :integer }
    end

    trait :late_threshold do
      key { 'late_threshold_minutes' }
      value { '15' }
      setting_type { :integer }
    end

    trait :early_departure_threshold do
      key { 'early_departure_threshold_minutes' }
      value { '30' }
      setting_type { :integer }
    end

    # Type-specific traits
    trait :integer_type do
      setting_type { :integer }
      value { '123' }
    end

    trait :boolean_type do
      setting_type { :boolean }
      value { 'true' }
    end

    trait :decimal_type do
      setting_type { :decimal }
      value { '123.45' }
    end

    trait :time_type do
      setting_type { :time }
      value { '14:30' }
    end

    trait :json_type do
      setting_type { :json }
      value { '{"key": "value"}' }
    end

    trait :array_type do
      setting_type { :array }
      value { 'item1,item2,item3' }
    end

    trait :email_type do
      setting_type { :email }
      value { '<EMAIL>' }
    end

    # Factory for creating all attendance settings at once
    factory :attendance_settings, class: OpenStruct do
      transient do
        work_start { '09:00' }
        work_end { '17:00' }
        break_duration { '60' }
        late_threshold { '15' }
        early_departure_threshold { '30' }
      end

      skip_create

      after(:build) do |setting, evaluator|
        create(:setting, :work_start_time, value: evaluator.work_start)
        create(:setting, :work_end_time, value: evaluator.work_end)
        create(:setting, :break_duration, value: evaluator.break_duration)
        create(:setting, :late_threshold, value: evaluator.late_threshold)
        create(:setting, :early_departure_threshold, value: evaluator.early_departure_threshold)
      end
    end
  end
end
